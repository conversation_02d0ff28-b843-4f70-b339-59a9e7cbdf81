/**
 * This class is responsible for Handler utility.
 *
 *
 * Methods:
 * - [MethodName]: checkMandato
 * - [MethodName]: setPermissionForNewMandato
 *
 * Author: <PERSON>
 * Date: 23-01-2025
 * @cicd_tests AccountAccountRelationTriggerUtilityTest
 */
public without sharing class AccountAccountRelationTriggerUtility {
    private final static String AGENCY = 'Agency';
    private final static String SOCIETY = 'Society';
    private final static Id ID_ACCOUNT_SOCIETY = Schema.SObjectType.FinServ__AccountAccountRelation__c.getRecordTypeInfosByDeveloperName().get('AccountSociety').getRecordTypeId();
    private final static Id ID_ACCOUNT_AGENCY = Schema.SObjectType.FinServ__AccountAccountRelation__c.getRecordTypeInfosByDeveloperName().get('AccountAgency').getRecordTypeId();
    private final static Id ID_AGENCY_SOCIETY = Schema.SObjectType.FinServ__AccountAccountRelation__c.getRecordTypeInfosByDeveloperName().get('AgencySociety').getRecordTypeId();

    // Method to check for duplicate AccountAccountRelation records
    public static void checkMandato(List<FinServ__AccountAccountRelation__c> newList) {
        System.debug('AccountAccountRelationTriggerUtility.checkMandato');

        // Query to get existing AccountAccountRelation records with specific criteria
        List<FinServ__AccountAccountRelation__c> listCompanyAgency = [
            SELECT id, FinServ__Account__c, FinServ__RelatedAccount__c, FinServ__Role__c
            FROM FinServ__AccountAccountRelation__c
            WHERE FinServ__Role__r.name = 'Agenzia' AND FinServ__Role__r.FinServ__InverseRole__c = 'Compagnia'
        ];

        // Loop through the new list of AccountAccountRelation records
        for (FinServ__AccountAccountRelation__c rel : newList) {
            // Loop through the queried list of existing AccountAccountRelation records
            for (FinServ__AccountAccountRelation__c companyAgency : listCompanyAgency) {
                // Check if there is a duplicate relation
                if (rel.FinServ__Account__c == companyAgency.FinServ__Account__c && rel.FinServ__RelatedAccount__c == companyAgency.FinServ__RelatedAccount__c && rel.FinServ__Role__c == companyAgency.FinServ__Role__c) {
                    // Add error to the duplicate relation
                    rel.addError('Non è possibile creare una relazione duplicata');
                }
            }
        }
    }

    // Method to set permissions for new AccountAccountRelation records
    public static void setPermissionForNewMandato(List<FinServ__AccountAccountRelation__c> aar) {
        Set<String> agencyId = new Set<String>();

        // Collect all agency IDs from the new AccountAccountRelation records
        for (FinServ__AccountAccountRelation__c a : aar) {
            agencyId.add(a.FinServ__Account__c);
        }

        /* ***** 27.08.2025 - Code Comment for refactor - Raffaele Granato
        // Query to get users associated with the collected agency IDs
        List<User> listUser = [SELECT Id, IdAzienda__c FROM User WHERE IdAzienda__c IN :agencyId];
        List<Id> listUserId = new List<Id>();

        // Collect user IDs from the queried users
        for (User u : listUser) {
            listUserId.add(u.Id);
        }
           ***** 27.08.2025 - Code Comment for refactor - Raffaele Granato */
        List<Id> listUserId = new List<Id>(NetworkUserHelper.getUserIdFromNetworkUserWithAgencyId(agencyId));
        System.debug('listUserId : ' + listUserId);

        // If there are any user IDs, execute the batch to assign permissions
        if (!listUserId.isEmpty()) {
            PermissionSetAssignmentBatch permissionSetAssignmentBatch = new PermissionSetAssignmentBatch(listUserId, false);
            Database.executeBatch(permissionSetAssignmentBatch, 10);
        }
    }

    public static void performShare(Set<Id> setAarId) {
        /*
        AccountAccounRelShareBatch accountAccounRelShareBatch = new AccountAccounRelShareBatch(setAarId);
        Database.executeBatch(accountAccounRelShareBatch, 10);
        */

        List<FinServ__AccountAccountRelation__c> lstAux = [
            SELECT Id, FinServ__Account__c, FinServ__Account__r.ExternalId__c, FinServ__Account__r.RecordType.DeveloperName, FinServ__RelatedAccount__c, FinServ__RelatedAccount__r.ExternalId__c, FinServ__RelatedAccount__r.RecordType.DeveloperName, FinServ__Account__r.name, FinServ__RelatedAccount__r.name, FinServ__Role__r.name, FinServ__Role__r.FinServ__InverseRole__c, FinServ__InverseRelationship__c, CreatedDate, RecordTypeId
            FROM FinServ__AccountAccountRelation__c
            WHERE FinServ__Role__r.Name = 'Cliente' AND FinServ__Role__r.FinServ__InverseRole__c IN ('Agenzia', 'Compagnia') AND Id IN :setAarId
        ];

        Set<String> groupValue = new Set<String>();
        //Set<Id> setIdsAgency = new Set<Id>();
        for (FinServ__AccountAccountRelation__c aar : lstAux) {
            if (String.valueOf(ID_ACCOUNT_AGENCY).equalsIgnoreCase(aar.RecordTypeId)) {
                groupValue.add(Label.AgencyExtIdAAR + aar.FinServ__RelatedAccount__r.ExternalId__c);
                //setIdsAgency.add(aar.FinServ__RelatedAccount__c);
            } else if (String.valueOf(ID_ACCOUNT_SOCIETY).equalsIgnoreCase(aar.RecordTypeId)) {
                groupValue.add(aar.FinServ__RelatedAccount__r.ExternalId__c);
            } else if (String.valueOf(ID_AGENCY_SOCIETY).equalsIgnoreCase(aar.RecordTypeId)) {
                groupValue.add(aar.FinServ__Account__r.ExternalId__c);
            }
        }

        //List<FinServ__AccountAccountRelation__c> listAccAccRelAgencySociety = [SELECT FinServ__Account__c, FinServ__Account__r.ExternalId__c, FinServ__RelatedAccount__r.ExternalId__c FROM FinServ__AccountAccountRelation__c WHERE FinServ__Account__c IN :setIdsAgency AND RecordTypeId = :ID_AGENCY_SOCIETY];
        //for (FinServ__AccountAccountRelation__c aar : listAccAccRelAgencySociety) {
        //    groupValue.add('D_' + aar.FinServ__Account__r.ExternalId__c + '_' + aar.FinServ__RelatedAccount__r.ExternalId__c);
        //}

        System.debug('### DEVCAP => groupValue: ' + groupValue);
        List<Group> listGroup = [SELECT Id, DeveloperName FROM Group WHERE DeveloperName IN :groupValue];
        List<FinServ__AccountAccountRelation__Share> lstAccAccRelShares = new List<FinServ__AccountAccountRelation__Share>();
        List<AccountShare> lstAccountShare = new List<AccountShare>();

        for (FinServ__AccountAccountRelation__c aar : lstAux) {
            for (Group g : listGroup) {
                //String groupDirezioneName = 'D_' + aar.FinServ__Account__r.ExternalId__c + '_' + aar.FinServ__RelatedAccount__r.ExternalId__c;
                if (String.valueOf(ID_ACCOUNT_AGENCY).equalsIgnoreCase(aar.RecordTypeId) && (g.DeveloperName.equalsIgnoreCase(Label.AgencyExtIdAAR + aar.FinServ__RelatedAccount__r.ExternalId__c) /*|| g.DeveloperName.equalsIgnoreCase(groupDirezioneName)*/)) {
                    FinServ__AccountAccountRelation__Share accAccRelAgencyShare = new FinServ__AccountAccountRelation__Share();
                    accAccRelAgencyShare.ParentId = aar.Id;
                    accAccRelAgencyShare.UserOrGroupId = g.Id;
                    accAccRelAgencyShare.AccessLevel = 'Read';
                    lstAccAccRelShares.add(accAccRelAgencyShare);
                } else if (String.valueOf(ID_ACCOUNT_SOCIETY).equalsIgnoreCase(aar.RecordTypeId) && g.DeveloperName.equalsIgnoreCase(aar.FinServ__RelatedAccount__r.ExternalId__c)) {
                    FinServ__AccountAccountRelation__Share accAccRelSocietyShare = new FinServ__AccountAccountRelation__Share();
                    accAccRelSocietyShare.ParentId = aar.Id;
                    accAccRelSocietyShare.UserOrGroupId = g.Id;
                    accAccRelSocietyShare.AccessLevel = 'Read';
                    //Account Share
                    AccountShare accShare = new AccountShare();
                    accShare.AccountId = aar.FinServ__Account__c;
                    accShare.UserOrGroupId = g.Id;
                    accShare.AccountAccessLevel = 'Read';
                    accShare.OpportunityAccessLevel = 'None';
                    lstAccAccRelShares.add(accAccRelSocietyShare);
                    lstAccountShare.add(accShare);
                } else if (String.valueOf(ID_AGENCY_SOCIETY).equalsIgnoreCase(aar.RecordTypeId)) {
                    FinServ__AccountAccountRelation__Share accAccRelSocietyShare = new FinServ__AccountAccountRelation__Share();
                    accAccRelSocietyShare.ParentId = aar.Id;
                    accAccRelSocietyShare.UserOrGroupId = g.Id;
                    accAccRelSocietyShare.AccessLevel = 'Read';
                    lstAccAccRelShares.add(accAccRelSocietyShare);
                }
            }
        }
        if (Schema.sObjectType.FinServ__AccountAccountRelation__Share.isCreateable()) {
            insert lstAccAccRelShares;
        }
        if (Schema.sObjectType.AccountShare.isCreateable()) {
            insert lstAccountShare;
        }
    }

    public static void deleteShare(List<FinServ__AccountAccountRelation__c> deleteList) {
        Set<Id> setIdAccountAAR = new Set<Id>();
        Set<String> setNameGroupAgency = new Set<String>();
        Set<String> setNameGroupSociety = new Set<String>();
        Set<String> setNameGroupAgencySociety = new Set<String>();
        for (FinServ__AccountAccountRelation__c aar : deleteList) {
            if (String.valueOf(ID_ACCOUNT_AGENCY).equalsIgnoreCase(aar.RecordTypeId)) {
                setNameGroupAgency.add(Label.AgencyExtIdAAR + aar.FinServ__RelatedAccount__r.ExternalId__c);
                setIdAccountAAR.add(aar.Id);
            } else if (String.valueOf(ID_ACCOUNT_SOCIETY).equalsIgnoreCase(aar.RecordTypeId)) {
                setNameGroupSociety.add(aar.FinServ__RelatedAccount__r.ExternalId__c);
                setIdAccountAAR.add(aar.Id);
            } else if (String.valueOf(ID_AGENCY_SOCIETY).equalsIgnoreCase(aar.RecordTypeId)) {
                setNameGroupAgencySociety.add(aar.FinServ__Account__r.ExternalId__c);
                setIdAccountAAR.add(aar.Id);
            }
        }
        Set<Id> setIdGroupAgency = new Set<Id>();
        Set<Id> setIdGroupSociety = new Set<Id>();
        Set<Id> setIdGroupAgencySociety = new Set<Id>();
        if (!setNameGroupAgency.isEmpty()) {
            setIdGroupAgency.addAll(new Map<Id, Group>([SELECT Id FROM Group WHERE DeveloperName IN :setNameGroupAgency]).keySet());
        }
        if (!setNameGroupSociety.isEmpty()) {
            setIdGroupSociety.addAll(new Map<Id, Group>([SELECT Id FROM Group WHERE DeveloperName IN :setNameGroupSociety]).keySet());
        }
        if (!setNameGroupAgencySociety.isEmpty()) {
            setIdGroupAgencySociety.addAll(new Map<Id, Group>([SELECT Id FROM Group WHERE DeveloperName IN :setNameGroupAgencySociety]).keySet());
        }
        List<FinServ__AccountAccountRelation__Share> listToDelete = [
            SELECT Id
            FROM FinServ__AccountAccountRelation__Share
            WHERE ParentId IN :setIdAccountAAR AND (UserOrGroupId IN :setIdGroupAgency OR UserOrGroupId IN :setIdGroupSociety OR UserOrGroupId IN :setIdGroupAgencySociety)
        ];
        if (Schema.sObjectType.FinServ__AccountAccountRelation__Share.isDeletable() && !listToDelete.isEmpty()) {
            delete listToDelete;
        }
    }
}
