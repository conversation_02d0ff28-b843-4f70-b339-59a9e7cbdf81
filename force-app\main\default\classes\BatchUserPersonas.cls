// SET SCOPE 1
global without sharing class BatchUser<PERSON>ersonas implements Database.Batchable<sObject>, Database.Stateful {
    
    String query;

    // SET SCOPE 1

    global BatchUserPersonas(String fromDate, String toDate, List<String> fiscalCodes, Boolean onlyBlankPersonas){

        query = 'SELECT Id, Profile.Name, Personas__c FROM User WHERE ';

        List<String> filters = new List<String>();

        if(fromDate != null){
            filters.add('LastModifiedDate >= ' + fromDate);
        }

        if(toDate != null){
            filters.add('LastModifiedDate < ' + toDate);
        }

        if(fiscalCodes != null && !fiscalCodes.isEmpty()){
            filters.add('FiscalCode__c IN (\'' + String.join(fiscalCodes, '\',\'') + '\')');
        }

        if(onlyBlankPersonas){
            filters.add('Personas__c = null');
        }

        filters.add('Profile.Name IN (\'Unipol Standard User\',\'Unipol Responsabile User\')');

        if(!filters.isEmpty()){
            query += '(' + String.join(filters, ' AND ') + ')';
        }

    }

    // SET SCOPE 1

    global BatchUserPersonas(){
        query = (Test.isRunningTest()) ? 'SELECT Id, Profile.Name, Personas__c FROM User LIMIT 1' : 'SELECT Id, Profile.Name, Personas__c FROM User';
    }
	
	global Database.QueryLocator start(Database.BatchableContext BC) {
		return Database.getQueryLocator(query);
	}

	global void execute(Database.BatchableContext BC, List<sObject> scope){
		
        // SET SCOPE 1

		if(scope != null && !scope.isEmpty()){

            try{

                for(User u : (List<User>)scope){

                    UserProvisioningAPI.UCAResponse UCAData = (Test.isRunningTest()) ? new UserProvisioningAPI.UCAResponse() : UserProvisioningAPI.getUCAData(u.FederationIdentifier);
					
                    if(Test.isRunningTest()) UCAData.result = UserProvisioningAPI.getTestReponse();
                    
                    Boolean hasTOT = false;
                    Boolean hasPAR = false;

                    if(UCAData != null && UCAData.result != null && !UCAData.result.isEmpty()){

                        for(UserProvisioningAPI.UCARequestItem uri : UCAData.result){

                            if(uri.embedded != null && uri.embedded.profiloCommerciale != null && String.isNotBlank(uri.embedded.profiloCommerciale.codiceProfiloCommerciale)){

                                if('TOT'.equalsIgnoreCase(uri.embedded.profiloCommerciale.codiceProfiloCommerciale)){
                                    hasTOT = true;
                                }else if('PAR'.equalsIgnoreCase(uri.embedded.profiloCommerciale.codiceProfiloCommerciale)){
                                    hasPAR = true;
                                }

                            }

                        }

                    }

                    Map<String, Object> ipInput = new Map<String, Object>();
                    ipInput.put('IPAssigneeId',u.Id);

                    if(hasTOT){
                        ipInput.put('IPprofileSelected','Responsabile Totale');
                        ipInput.put('IPpersonasSelected','Responsabile Totale');
                    }else if(hasPAR){
                        ipInput.put('IPprofileSelected','Responsabile Parziale');
                        ipInput.put('IPpersonasSelected','Responsabile Parziale');
                    }else{
                        ipInput.put('IPprofileSelected','Operatore');
                        ipInput.put('IPpersonasSelected','Operatore');
                    }
                    Map<String, Object> ipOutput = (Map<String, Object>) omnistudio.IntegrationProcedureService.runIntegrationService('UpdateUserPSandProfile_UpdateUserPSandProfile', ipInput, null);
                }

            }catch(Exception ex){

                System.debug('Exception --> '+ex.getMessage());
                System.debug('Exception --> '+ex.getStackTraceString());

            }

        }
		
	}
	
	global void finish(Database.BatchableContext BC) {}

}