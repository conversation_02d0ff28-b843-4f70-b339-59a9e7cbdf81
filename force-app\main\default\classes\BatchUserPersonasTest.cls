@isTest
public class BatchUserPersonasTest {
    
    @isTest
    static void test_coverage(){

        User u = new User();
        u.FirstName = 'Test2TC';
        u.LastName = 'TestLastName2TC';
        u.FederationIdentifier = 'TEST';
        u.ProfileId = [SELECT Id FROM Profile WHERE Name = 'Unipol Standard User' LIMIT 1].Id;
        u.Email = '<EMAIL>';
        u.Username = 'test' + System.now().getTime() + '@test.it.invalid3.tc';
        u.Alias = 'ttst2';
        u.TimeZoneSidKey = 'Europe/Rome';
        u.EmailEncodingKey = 'UTF-8';
        u.LanguageLocaleKey = 'en_US';
        u.LocaleSidKey = 'it_IT';
        u.FiscalCode__c = 'TESTTCBATCH';
        u.FederationIdentifier = 'TESTTCBATCH';
        insert u;

        Test.startTest();

        Database.executeBatch(new BatchUserPersonas('2025-07-17T12:38:33.000+0000','2030-07-01T12:38:33.000+0000',new List<String>{'TESTTCBATCH'},true));

        Test.stopTest();

    }
    
    @isTest
    static void test_coverage_2(){

        User u = new User();
        u.FirstName = 'Test2TC';
        u.LastName = 'TestLastName3TC';
        u.FederationIdentifier = 'TEST';
        u.ProfileId = [SELECT Id FROM Profile WHERE Name = 'Unipol Standard User' LIMIT 1].Id;
        u.Email = '<EMAIL>';
        u.Username = 'test' + System.now().getTime() + '@test.it.invalid3.tc';
        u.Alias = 'ttst3';
        u.TimeZoneSidKey = 'Europe/Rome';
        u.EmailEncodingKey = 'UTF-8';
        u.LanguageLocaleKey = 'en_US';
        u.LocaleSidKey = 'it_IT';
        u.FiscalCode__c = 'TESTTCBATCH2';
        u.FederationIdentifier = 'TESTTCBATCH2';
        insert u;

        Test.startTest();

        Database.executeBatch(new BatchUserPersonas());

        Test.stopTest();

    }

}