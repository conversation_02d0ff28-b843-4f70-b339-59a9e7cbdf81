/**
 * @description       : ConiAssignmentService.cls
 * <AUTHOR> <EMAIL>
 * @group             :
 * @last modified on  : 03-02-2025
 * @last modified by  : <EMAIL>
 * @cicd_tests TestOpportunityTriggerHandler
 **/
public without sharing class ConiAssignmentService {
    private static final String OPPORTUNITY_SHARE = System.label.ConiOpportunityShare;
    private static final String INSURANCE_POLICY_SHARE = System.label.ConiInsurancePolicyShare;
    private static final String CASE_SHARE = System.label.ConiCaseShare;
    private static final String SERVICE_APPOINTMENT_SHARE = System.label.ConiServiceAppointmentShare;
    private static final String ID = System.label.ConiId;
    private static final String SHARE = System.label.ConiShare;
    private static final String USER_OR_GROUP = System.label.ConiUserOrGroup;
    private static final String NAME = System.label.ConiName;
    private static final String EDIT = System.label.ConiEdit;
    private static final String READ = System.label.ConiRead;
    private static final String GRUPPO_REFERENTI = System.label.ConiGruppoReferenti;
    private static final String USER_OR_GROUP_ID = System.label.ConiUserOrGroupId;
    private static final String ACCOUNT_ID = System.label.ConiAccountId;
    private static final String ASSIGNED_TO = System.label.ConiAssignedTo;
    private static final String ASSIGNED_GROUP = System.label.ConiAssignedGroup;
    private static final String GROUP_OBJ = System.label.ConiGroup;
    private static final String AGENCY_FIELD = System.label.ConiAgency;
    private static final String IS_SET_REF = System.label.ConiIsSetRef;

    //Key
    private static final String ACCESS_LEVEL_KEY = System.label.ConiAccessLevelKey;
    private static final String ACCOUNT_KEY = System.label.ConiAccountKey;
    private static final String EDIT_MAP_KEY = System.label.ConiEditMapKey;
    private static final String READ_MAP_KEY = System.label.ConiReadMapKey;
    private static final String ID_KEY = System.label.ConiIdKey;
    private static final String OBJ_NAME = System.label.ConiObjNameKey;
    private static final String REF_UPDATE_ID = System.label.ConiRefUpdateIdKey;

    //Value
    private static final String OPPORTUNITY_ACCESS_LEVEL_VALUE = System.label.ConiOpportunityAccessLevelValue;
    private static final String ACCESS_LEVEL_VALUE = System.label.ConiAccessLevelValue;
    private static final String CASE_ACCESS_LEVEL_VALUE = System.label.ConiCaseAccessLevelValue;

    //Query
    private static final String CONI_ACCOUNT_VALUE = System.label.ConiConiAccountValue;
    private static final String CONI_OBJECT_SHARE = System.label.ConiConiObjectShare;
    private static final String QUERY_FIELD = System.label.ConiQueryField;
    private static final String QUERY_OBJ_SHARE = System.label.ConiQueryObjShare;

    public static Map<String, Map<String, String>> fieldNameMap = new Map<String, Map<String, String>>{
        OPPORTUNITY_SHARE => new Map<String, String>{ ACCESS_LEVEL_KEY => OPPORTUNITY_ACCESS_LEVEL_VALUE, ID_KEY => System.Label.OpportunityId },
        INSURANCE_POLICY_SHARE => new Map<String, String>{ ACCESS_LEVEL_KEY => ACCESS_LEVEL_VALUE, ID_KEY => System.Label.ParentId },
        CASE_SHARE => new Map<String, String>{ ACCESS_LEVEL_KEY => CASE_ACCESS_LEVEL_VALUE, ID_KEY => System.Label.CaseId },
        SERVICE_APPOINTMENT_SHARE => new Map<String, String>{ ACCESS_LEVEL_KEY => ACCESS_LEVEL_VALUE, ID_KEY => System.Label.ParentId }
    };
    public static Map<String, Schema.SObjectType> sObjectMap = Schema.getGlobalDescribe();
    public static Map<String, Set<String>> mapStringARR = new Map<String, Set<String>>();
    public static Set<String> deleteIdOld = new Set<String>();

    public static SObject setReferentsGroup(Boolean isSetRef, Id accountId, Id objectId, String objectName) {
        SObject objEdit = null;
        //System.debug('>>>> setReferentsGroup: ' + isSetRef);
        if (!isSetRef) {
            String externalId = [SELECT ExternalId__c FROM Account WHERE Id = :accountId LIMIT 1]?.ExternalId__c;
            if (Schema.sObjectType.Group__c.isAccessible()) {
                String referentsGroup = /*GRUPPO_REFERENTI*/ System.Label.AgencyExtIdAAR + externalId;
                /*List<Group__c> groupLst = [
                    SELECT PublicGroupId__c
                    FROM Group__c
                    WHERE Agenzia__c = :accountId
                        AND Name LIKE :referentsGroup
                    LIMIT 1
                ];*/
                List<Group> groupLst = [SELECT Id FROM Group WHERE DeveloperName = :referentsGroup LIMIT 1];
                if (groupLst.size() > 0) {
                    Id publicGroupId = groupLst[0].Id;
                    objEdit = getObject(System.Label.EditAccessLevel, objectId, publicGroupId);
                } else {
                    System.debug('##### DEV: non è stato trovato un gruppo per i referenti');
                    //AC: bisogna sollevare un'eccezione?
                }
            } else {
                throw new NoAccessException('##### DEV: Non hai i permessi per leggere Group__c.');
            }
        }
        //System.debug('>>>> objEdit: ' + objEdit);
        return objEdit;
    }

    public static String capitalize(String input) {
        if (!String.isEmpty(input)) {
            input = input.substring(0, 1).toUpperCase() + input.substring(1).toLowerCase();
        }
        return input;
    }

    public static String getObjectNameById(Id objectId) {
        String prefix = objectId.toString().substring(0, 3);
        String objectName;
        for (String key : sObjectMap.keySet()) {
            if (prefix.equals(sObjectMap.get(key).getDescribe().getKeyPrefix())) {
                objectName = capitalize(key);
                break;
            }
        }
        return objectName;
    }

    public static SObject getObject(String editLevel, Id objectId, Id publicGroupId) {
        String objectName = getObjectNameById(objectId);
        String objShareName = objectName + SHARE;
        //AC: imposto il nome dell'oggetto e i campi
        Map<String, String> fields = fieldNameMap.get(objShareName);
        Schema.SObjectType cstType = Schema.getGlobalDescribe().get(objectName + SHARE);
        SObject objEdit = cstType.newSObject();
        objEdit.put(fields.get(ACCESS_LEVEL_KEY), editLevel);
        objEdit.put(fields.get(ID_KEY), String.valueOf(objectId));
        objEdit.put(System.Label.UserOrGroupId, String.valueOf(publicGroupId));
        return objEdit;
    }

    /*public static Map<Id, Set<Id>> getAccountIdToMatchedUserOrGroupIds(Set<Id> accountList) {
        // QUERY to check if there are matches for the accounts related to the opportunities in the trigger.
        // TODO CHANGE QUERY with CustomMetadata
        Query__mdt query = [SELECT Query__c FROM Query__mdt WHERE QualifiedApiName = :CONI_ACCOUNT_VALUE];
        List<Account> accounts = Database.query(query.Query__c);
        //System.debug('>>>> accounts = ' + accounts);

        Map<Id, Set<Id>> accountIdToMatchedUserOrGroupIds = new Map<Id, Set<Id>>();
        // Names of the child relationships whose shares should be retrieved
        String strAux = System.Label.ChildObjectNames;
        List<String> childObjectNames = strAux.split(',');

        // Building the map that will have the Account Id as the key and the set of IDs of the matched UsersOrGroups as the value.
        for (Account acc : accounts) {
            Set<Id> matchedUserOrGroupIds = new Set<Id>();
            // For each child object, iterate over its records, get those records' shares, and add them to the shares list
            for (String childObjectName : childObjectNames) {
                for (SObject childRecord : acc.getSObjects(childObjectName)) {
                    for (Sobject childRecordShare : childRecord.getSObjects(System.Label.Shares)) {
                        matchedUserOrGroupIds.add((Id) childRecordShare.get(System.Label.UserOrGroupId));
                    }
                }
            }
            if (!matchedUserOrGroupIds.isEmpty()) {
                accountIdToMatchedUserOrGroupIds.put(acc.Id, matchedUserOrGroupIds);
            }
        }
        return accountIdToMatchedUserOrGroupIds;
    }*/

    public static Map<String, Object> prepareData(Set<Id> accountList) {
        //AC: inizio esecuzione query. questa query estrae tutti i valori per read ed edit
        Query__mdt query = [SELECT Query__c FROM Query__mdt WHERE QualifiedApiName = :CONI_ACCOUNT_VALUE];
        //AC: il nome della variabile accountList è quello impostato nella query presente nel meta data
        List<Account> accounts = Database.query(query.Query__c);
        String strAux = System.Label.ChildObjectNames;
        System.debug('>>>> prepareData.strAux: ' + strAux);
        List<String> childObjectNames = strAux.split(',');

        //AC: creazione delle mappe di read e di edit
        Map<Id, Set<Id>> accountIdToMatchedUserOrGroupIds = new Map<Id, Set<Id>>();
        Map<String, Map<Id, SObject>> editMap = new Map<String, Map<Id, SObject>>();
        Map<String, Map<Id, SObject>> readMap = new Map<String, Map<Id, SObject>>();
        //System.debug('>>>> CHECK ALL SHARE ON ACCOUNT: ' + JSON.serializePretty(accounts));
        for (Account acc : accounts) {
            Set<Id> matchedUserOrGroupIds = new Set<Id>();
            for (String childObjectName : childObjectNames) {
                for (SObject childRecord : acc.getSObjects(childObjectName)) {
                    for (SObject childRecordShare : childRecord.getSObjects(System.Label.Shares)) {
                        //AC: qui si fa il check dei referenti.
                        //    nella mappa di edit non deve mai essere presente il referente.
                        //    rimuoverlo dalla mappa edit risolve i problemi di riassegnazione a read del referente.
                        Boolean isReferenti = ((String) childRecordShare?.getSObject(USER_OR_GROUP)?.get(NAME))?.indexOf(GRUPPO_REFERENTI) > -1;
                        if (!isReferenti) {
                            matchedUserOrGroupIds.add((Id) childRecordShare.get(System.Label.UserOrGroupId));
                            String className = childRecordShare.getSObjectType().getDescribe().getName();
                            Map<String, String> fieldName = fieldNameMap.get(className);
                            Id objId = (Id) childRecordShare.get(fieldName.get(ID_KEY));
                            Id userOrGroupId = (Id) childRecordShare.get(USER_OR_GROUP_ID);
                            Map<Id, SObject> valueMap = new Map<Id, SObject>{ userOrGroupId => childRecordShare };
                            if (childRecordShare.get(fieldName.get(ACCESS_LEVEL_KEY)).equals(EDIT)) {
                                editMap.put(objId + '_' + childRecordShare.get(System.Label.UserOrGroupId), valueMap);
                            } else {
                                readMap.put(objId + '_' + childRecordShare.get(System.Label.UserOrGroupId), valueMap);
                            }
                        }
                    }
                }
            }
            //System.debug('>>>> prepareData.editMap: ' + editMap);
            if (!matchedUserOrGroupIds.isEmpty()) {
                accountIdToMatchedUserOrGroupIds.put(acc.Id, matchedUserOrGroupIds);
            }
        }
        return new Map<String, Object>{ ACCOUNT_KEY => accountIdToMatchedUserOrGroupIds, EDIT_MAP_KEY => editMap, READ_MAP_KEY => readMap };
    }

    public static Boolean checkReassignment(SObject objNew, SObject objOld) {
        /* System.debug('>>>> opOld.AssignedTo__c: ' + objOld.get(ASSIGNED_TO));
        System.debug('>>>> op.AssignedTo__c: ' + objNew.get(ASSIGNED_TO));
        System.debug('>>>> opOld.AssignedGroup__c: ' + objOld.get(ASSIGNED_GROUP));
        System.debug('>>>> op.AssignedGroup__c: ' + objNew.get(ASSIGNED_GROUP)); */
        Boolean ret = false;
        //AC: primo problema trovato grazie al debugger
        //    bisogna verificare anche che i dati presenti in new siano diversi da quelli presenti in old
        //    è necessario verificarlo per gli update in cui non si toccano i dati di assegnazione
        if ((objOld.get(ASSIGNED_TO) != null || objOld.get(ASSIGNED_GROUP) != null)) {
            if (objOld.get(ASSIGNED_TO) != null && objOld.get(ASSIGNED_TO) != objNew.get(ASSIGNED_TO)) {
                ret = true;
            } else if (objOld.get(ASSIGNED_GROUP) != null && objOld.get(ASSIGNED_GROUP) != objNew.get(ASSIGNED_GROUP)) {
                ret = true;
            }
        }
        //System.debug('>>>> checkReassignment: ' + ret);
        return ret;
    }

    public static Id checkIsGroup(Id objId, Map<Id, Id> grpId) {
        if (objId != null && getObjectNameById(objId).equals(GROUP_OBJ)) {
            objId = grpId.get(objId);
        }
        return objId;
    }

    public static Map<String, SObject> editPriviliges2(Map<Id, Set<Id>> accountIdToMatchedUserOrGroupIds, Map<String, Map<Id, SObject>> editMap, Map<String, Map<Id, SObject>> readMap, List<SObject> objList, Map<Id, SObject> oldSobjMap, Map<Id, Id> grpId) {
        System.debug('>>>> editPriviliges2');
        /* for (String editKey : editMap.keySet()) {
            System.debug('>>>> editPriviliges2.editKey: ' + editKey);
            //System.debug('>>>> editMapValue: ' + editMap.get(editKey));
        }
        for (String readKey : readMap.keySet()) {
            System.debug('>>>> editPriviliges2.readKey: ' + readKey);
            //System.debug('>>>> readMapValue: ' + readMap.get(readKey));
        } */
        //System.debug('>>>> editMap:' + editMap);
        //System.debug('>>>> editUgMap: ' + editUgMap);
        //System.debug('>>>> readMap: ' + readMap);
        //System.debug('>>>> readUgMap: ' + readUgMap);
        // fine esecuzione query

        Map<Id, SObject> currentEdit = new Map<Id, SObject>();
        Id currentId = null;
        Id currentUserOrGroupId = null;
        List<SObject> updateId = new List<SObject>();
        List<SObject> deleteId = new List<SObject>();
        //TODO
        //Set<String> deleteIdOld = new Set<String>();
        //TODO
        List<SObject> lstAux = new List<SObject>();
        Map<String, String> fields = null;
        String objShareName = null;
        Set<String> setOldUser = new Set<String>();
        Set<String> setActualUser = new Set<String>();
        Set<String> seAccountId = new Set<String>();
        Set<String> seAgencyId = new Set<String>();
        //AC: imposto il nome dell'oggetto e i campi
        if (!objList.isEmpty()) {
            objShareName = objList.get(0).getSObjectType().getDescribe().getName() + SHARE;
            fields = fieldNameMap.get(objShareName);
        }
        for (SObject sObj : objList) {
            //System.debug('>>>> sObj:' + sObj);
            if (sObj.get(ASSIGNED_TO) != null || sObj.get(ASSIGNED_GROUP) != null) {
                //AC: questo è necessario in caso di insert, è l'equivalente di Trigger.isUpdate
                Id oldUserOrGroupId = null;
                if (oldSobjMap != null) {
                    SObject sObjOld = oldSobjMap.get(sObj.Id);
                    oldUserOrGroupId = (Id) (sObjOld.get(ASSIGNED_GROUP) == null ? sObjOld.get(ASSIGNED_TO) : sObjOld.get(ASSIGNED_GROUP));
                    oldUserOrGroupId = checkIsGroup(oldUserOrGroupId, grpId);
                }
                currentId = (Id) sObj.get(ID);
                currentEdit = editMap.get(currentId + '_' + oldUserOrGroupId);
                setOldUser.add(oldUserOrGroupId);
                seAccountId.add((String) sObj.get(ACCOUNT_ID));
                seAgencyId.add((String) sObj.get(AGENCY_FIELD));
                setActualUser.add(sObj.get(ASSIGNED_TO) != null ? (String) sObj.get(ASSIGNED_TO) : checkIsGroup((Id) sObj.get(ASSIGNED_GROUP), grpId) /*(String) sObj.get(ASSIGNED_GROUP)*/);
                //System.debug('>>>> editMapId: ' + currentId + '_' + oldUserOrGroupId);
                Integer cntAcc = 0;
                if (accountIdToMatchedUserOrGroupIds.containsKey((Id) sObj.get(ACCOUNT_ID))) {
                    Set<Id> matchedUserOrGroupIds = accountIdToMatchedUserOrGroupIds.get((Id) sObj.get(ACCOUNT_ID));
                    if (currentEdit == null) {
                        //AC: in questo caso è la prima assegnazione
                        //System.debug('>>>> AC: assegnazione');
                        //System.debug('>>>> matchedUserOrGroupIds: ' + matchedUserOrGroupIds);
                        for (Id usOrGID : matchedUserOrGroupIds) {
                            cntAcc = countInMap(editMap, usOrGID);
                            if (cntAcc >= 1 && !editMap.containsKey(sObj.get(ID) + '_' + usOrGID)) {
                                SObject obj = getObject(System.Label.ReadAccessLevel, (Id) sObj.get(ID), usOrGID);
                                lstAux.add(obj);
                                //System.debug('>>>> lstAux.add 2: ' + obj);
                            }
                        }
                    } else {
                        //AC: in questo caso è la riassegnazione
                        //System.debug('>>>> currentEdit: ' + currentEdit);
                        //System.debug('>>>> currentId: ' + currentId);
                        //System.debug('>>>> oldUserOrGroupId: ' + oldUserOrGroupId);
                        //AC: assegno al nuovo assegnatario
                        Id uOrGId = (Id) (sObj.get(ASSIGNED_TO) == null ? sObj.get(ASSIGNED_GROUP) : sObj.get(ASSIGNED_TO));
                        uOrGId = checkIsGroup(uOrGId, grpId);
                        SObject objNew = getObject(System.Label.EditAccessLevel, (Id) sObj.get(ID), uOrGId);
                        lstAux.add(objNew);
                        //System.debug('>>>> lstAux.add 3: ' + objNew);
                        Integer cntKey = countInMap(editMap, oldUserOrGroupId);
                        //System.debug('>>>> cntKey: ' + cntKey);
                        if (cntKey > 1) {
                            updateId.add(currentEdit.get(oldUserOrGroupId));
                        } else {
                            //if (cntKey <= 1 || !isClosed) {
                            if (currentEdit != null) {
                                deleteId.add(currentEdit.get(oldUserOrGroupId));
                                deleteIdOld.add((String) oldUserOrGroupId);
                                System.debug('***** deleteIdOld: ' + deleteIdOld);
                            }
                            Set<String> readKeys = readMap.keySet();
                            //System.debug('>>>> readKeys: ' + readKeys);
                            for (String key : readKeys) {
                                if (key.split('_')[1] == oldUserOrGroupId) {
                                    //System.debug('>>>> key: ' + key);
                                    Map<Id, SObject> readValue = readMap.get(key);
                                    deleteId.add(readValue.get(oldUserOrGroupId));
                                    // deleteIdOld.add((String) oldUserOrGroupId);
                                    // System.debug('***** deleteIdOld: ' + deleteIdOld);
                                }
                            }
                        }
                        //System.debug('>>>> deleteId: ' + deleteId);
                        for (Id usOrGID : matchedUserOrGroupIds) {
                            if (usOrGID != oldUserOrGroupId) {
                                cntAcc = countInMap(editMap, usOrGID);
                                if (cntAcc >= 1) {
                                    SObject obj = getObject(System.Label.ReadAccessLevel, (Id) sObj.get(ID), usOrGID);
                                    lstAux.add(obj);
                                    //System.debug('>>>> lstAux.add 1: ' + obj);
                                }
                            }
                        }
                    }
                }

                //Map<String, Map<Id, SObject>> editMap,
                currentUserOrGroupId = (Id) (sObj.get(ASSIGNED_GROUP) == null ? sObj.get(ASSIGNED_TO) : sObj.get(ASSIGNED_GROUP));
                currentUserOrGroupId = checkIsGroup(currentUserOrGroupId, grpId);
                Integer cnt = 0;
                for (String kEditMap : editMap.keySet()) {
                    String[] kv = kEditMap.split('_');
                    if (kv[1] != String.valueOf(currentUserOrGroupId) && !deleteIdOld.contains(kv[1])) {
                        String objId = kv[0];
                        SObject obj = getObject(System.Label.ReadAccessLevel, objId, currentUserOrGroupId);
                        lstAux.add(obj);
                    }
                    //System.debug('>>>> cnt: ' + cnt);
                    cnt++;
                }
            }
        }
        Query__mdt query = [SELECT Query__c FROM Query__mdt WHERE QualifiedApiName = :CONI_OBJECT_SHARE];
        List<Id> updateIdLst = new List<Id>();
        if (updateId.size() > 0) {
            //System.debug('>>>> updateId: ' + updateId);
            for (SObject obj : updateId) {
                updateIdLst.add((Id) obj.get(ID));
            }
            List<SObject> objUpdate = Database.query(query.Query__c.replace(QUERY_FIELD, fields.get(ACCESS_LEVEL_KEY)).replace(QUERY_OBJ_SHARE, objShareName));
            for (SObject os : objUpdate) {
                os.put(fields.get(ACCESS_LEVEL_KEY), READ);
            }
            //System.debug('>>>> objUpdate: ' + objUpdate);
            update objUpdate;
        }
        if (deleteId.size() > 0) {
            System.debug('>>>> deleteId: ' + deleteId);
            for (SObject obj : deleteId) {
                updateIdLst.add((Id) obj.get(ID));
            }
            String sql = query.Query__c.replace(', ' + QUERY_FIELD, '').replace(QUERY_OBJ_SHARE, objShareName);
            /* +  ' AND ' + fields.get(ACCESS_LEVEL_KEY) + ' = \'READ\'';*/
            System.debug('>>> sql: ' + sql);
            List<SObject> objDelete = Database.query(sql);
            System.debug('>>>> objDelete: ' + objDelete);
            delete objDelete;
            ConiAssignmentHelper.deleteShareAAR(setOldUser, seAccountId, seAgencyId);
        }
        //System.debug('>>>> editPriviliges2.lstAux: ' + lstAux);
        mapStringARR.put('AccountId', seAccountId);
        mapStringARR.put('AgencyId', seAgencyId);
        mapStringARR.put('ActualUser', setActualUser);
        return lstToMap(lstAux);
    }

    public static Map<String, SObject> lstToMap(List<SObject> lst) {
        //provare a far diventare questa opportunityId + _ + userOrGroupId come chiave
        Map<String, SObject> ret = new Map<String, SObject>();
        Map<String, String> fields = null;
        String objShareName;
        for (SObject obj : lst) {
            //AC: bisogna farlo in ogni iterazione perchè lst contiene vari tipi di oggetti (OpportunityShare, CaseShare, etc.)
            objShareName = obj.getSObjectType().getDescribe().getName();
            fields = fieldNameMap.get(objShareName);
            //System.debug('>>>> lstToMap: ' + obj);
            String accessLevel = (String) obj.get(fields.get(ACCESS_LEVEL_KEY));
            //AC: bisogna togliere il read in caso ci sia già un edit e non bisogna mettere il read nel caso ci sia già un edit (sono due casi diverse)
            Boolean add = true;
            String key = (Id) obj.get(fields.get(ID_KEY)) + '_' + (Id) obj.get(USER_OR_GROUP_ID) + '_';
            if (accessLevel.equals(EDIT) && ret.containsKey(key + READ)) {
                ret.remove(key);
            } else if (ret.containsKey(key + EDIT)) {
                add = false;
            }
            if (add) {
                //System.debug('>>>> lstToMap.key: ' + key);
                ret.put(key + accessLevel, obj);
            }
        }
        return ret;
    }

    public static Integer countInMap(Map<String, Map<Id, SObject>> editMap, Id currentUserOrGroupId) {
        System.debug('**** countInMap');
        //System.debug('**** editMap : ' + JSON.serializePretty(editMap));
        System.debug('**** currentUserOrGroupId : ' + currentUserOrGroupId);
        Set<String> editKeys = editMap.keySet();
        System.debug('**** editKeys : ' + editKeys);
        Integer cntKey = 0;
        for (String key : editKeys) {
            Map<Id, SObject> editObj = editMap.get(key);
            System.debug('**** editObj : ' + editObj);
            String keyId = key.split('_')[1];
            System.debug('**** keyId : ' + keyId);
            if (keyId == String.valueOf(currentUserOrGroupId)) {
                cntKey++;
            }
        }
        System.debug('**** After Count cntKey : ' + cntKey);
        return cntKey;
    }

    public static Boolean checkAddFromEditMap(SObject sObj, SObject sObjOld, SObject value) {
        //AC: è necessario mettere il try catch perchè, per l'edit attuale impostato manualmente e che permette di creare le read
        //    per l'edit attuale, non è presente value.getSObject(USER_OR_GROUP)
        Boolean ret = true;
        try {
            String oldUserOrGroupId = (String) value.getSObject(USER_OR_GROUP).get(ID);
            //AC: questa deve essere tutta in una riga perchè, se non fosse, durante i test bisogna far si che vengano eseguite tutte le righe.
            ret = (sObjOld == null || sObjOld != null && ((sObjOld.get(ASSIGNED_TO) != null && sObjOld.get(ASSIGNED_TO) != oldUserOrGroupId) || (sObjOld.get(ASSIGNED_GROUP) != null && sObjOld.get(ASSIGNED_GROUP) != oldUserOrGroupId)));
        } catch (Exception e) {
            System.debug('>>>> ConiAssignmentService.checkAddFromEditMap: ' + e.getMessage());
        }
        return ret;
    }

    public static Boolean persistList(List<SObject> lstAux, Set<Id> refUpdateId, String objName, Map<String, Set<String>> mapAAR) {
        Boolean ret = true;
        System.debug('### DEVCAP => lstAux = ' + lstAux);
        if (!lstAux.isEmpty()) {
            //System.debug('>>>> lstAux.SIZE: ' + lstAux.size());
            //System.debug('>>>> lstAux: ' + JSON.serialize(lstAux));
            /*
            IMPORTANT: following the SF indications we will insert or delete the apexShares records inside the Handler classes and not calling the Queueable class
            ConiQueApexShareIns insApexShares = new ConiQueApexShareIns(lstAux);
            System.enqueueJob(insApexShares);
            */
            //System.debug('>>>> refUpdateId: ' + refUpdateId);
            if (!refUpdateId.isEmpty() && !System.isBatch()) {
                ConiQueRefUpdate insApexShares = new ConiQueRefUpdate(new Map<String, Object>{ REF_UPDATE_ID => refUpdateId, OBJ_NAME => objName });
                System.enqueueJob(insApexShares);
            }
            try {
                Boolean isSuccess = true;
                System.debug('### DEVCAP => lstAux INSIDE TRY BEFORE INSERT = ' + lstAux);
                Database.SaveResult[] lstInsRsl = Database.insert(lstAux, false);
                for (Database.SaveResult sr : lstInsRsl) {
                    if (!sr.isSuccess()) {
                        System.debug('>>>> Insert KO!');
                        isSuccess = false;
                    } else {
                        System.debug('>>>> sr: ' + sr.Id);
                    }
                }
                if (isSuccess) {
                    System.debug('**** STO PER ABBINARE LE AAR');
                    ConiAssignmentHelper.abbinamentoAAR(mapAAR.get('AccountId'), mapAAR.get('AgencyId'), mapAAR.get('ActualUser'));
                    System.debug('Statement insert with success!');
                }
            } catch (Exception e) {
                System.debug('The following exception has occurred: ' + e.getMessage());
                ret = false;
            }
        }
        return ret;
    }

    /*public static Boolean persistList(List<SObject> lstAux, Set<Id> refUpdateId, String objName) {
        return persistList(lstAux, refUpdateId, objName, new Map<String, Set<String>>());
    }*/

    public static Map<String, String> addMapAux(Map<String, String> fields, String accessLevel, Id objId, Id userOrGroupId) {
        return new Map<String, String>{ fields.get(ACCESS_LEVEL_KEY) => accessLevel, fields.get(ID_KEY) => String.valueOf(objId), System.Label.UserOrGroupId => String.valueOf(userOrGroupId) };
    }

    public static void performShares(List<SObject> newSobjLst, Map<Id, SObject> newSobjMap, Map<Id, SObject> oldSobjMap) {
        //CHIUSURA ATTIVITA
        List<SObject> closedNewObjectsList = new List<SObject>();
        Map<Id, SObject> closedNewObjectsMap = new Map<Id, SObject>();
        Map<Id, SObject> closedOldObjectsMap = new Map<Id, SObject>();

        //RIASSEGNAZIONE ATTIVITA
        List<SObject> reassignedNewObjectsList = new List<SObject>();
        Map<Id, SObject> reassignedNewObjectsMap = new Map<Id, SObject>();
        Map<Id, SObject> reassignedOldObjectsMap = new Map<Id, SObject>();

        //CAMBIO AGENZIA
        List<SObject> listAttivitaNewAgency = new List<SObject>();
        Map<Id, SObject> mapAttivitaOldAgency = new Map<Id, SObject>();

        if (ConiQueRefUpdate.isQueueableUpdate) {
            return;
        }

        if (Trigger.isAfter && Trigger.isInsert) {
            managePerformShares(newSobjLst, newSobjMap, oldSobjMap);
            return;
        }

        for (SObject sObj : newSobjLst) {
            SObject sObjOld = null;
            if (Trigger.isUpdate) {
                sObjOld = (SObject) oldSobjMap.get((Id) sObj.get(ID));
            }

            if (ConiAssignmentHelper.checkIsClosed(sObj, sObjOld)) {
                closedNewObjectsList.add(sObj);
                closedNewObjectsMap.put(sObj.Id, sObj);
                closedOldObjectsMap.put(sObj.Id, sObjOld);
            } else if (ConiAssignmentHelper.checkIfAgencyChanged(sObj, sObjOld)) {
                listAttivitaNewAgency.add(sObj);
                mapAttivitaOldAgency.put(sObjOld.Id, sObjOld);
                // oldAgencyId.add((String) sObjOld.get(AGENCY_FIELD));
                // oldAccountId.add((String) sObjOld.get(ACCOUNT_ID));
            } else {
                reassignedNewObjectsList.add(sObj);
                reassignedNewObjectsMap.put(sObj.Id, sObj);
                reassignedOldObjectsMap.put(sObj.Id, sObjOld);
            }

            // if (ConiAssignmentHelper.checkIfAgencyChanged(sObj, sObjOld)) {
            //     oldAgencyId.add((String) sObjOld.get(AGENCY_FIELD));
            //     oldAccountId.add((String) sObjOld.get(ACCOUNT_ID));
            // }
        }
        if (!reassignedNewObjectsList.isEmpty()) {
            managePerformShares(reassignedNewObjectsList, reassignedNewObjectsMap, reassignedOldObjectsMap);
        }
        if (!closedNewObjectsList.isEmpty()) {
            gestisciCambioStato(closedNewObjectsList, closedNewObjectsMap, closedOldObjectsMap);
        }
        if (!listAttivitaNewAgency.isEmpty()) {
            ConiAssignmentHelper.deleteForChangeAgencyShareAAR(listAttivitaNewAgency, mapAttivitaOldAgency);
        }
        // if (!oldAgencyId.isEmpty() && !oldAccountId.isEmpty()) {
        //     ConiAssignmentHelper.deleteForChangeAgencyShareAAR(oldAgencyId, oldAccountId);
        // }
    }

    public static void gestisciCambioStato(List<SObject> newSobjLst, Map<Id, SObject> newSobjMap, Map<Id, SObject> oldSobjMap) {
        Id CIP = Schema.SObjectType.Group__c.getRecordTypeInfosByDeveloperName().get('CIP').getRecordTypeId();
        Set<Id> setIdAssigned = new Set<Id>();
        Set<Id> setAccountId = new Set<Id>();
        Set<Id> setAgenziaId = new Set<Id>();
        Set<Id> setObjectId = new Set<Id>();
        Set<Id> setGroupId = new Set<Id>();
        //Set<Id> setGroupIdCIP = new Set<Id>();
        Set<String> setGroupNameCIP = new Set<String>();
        Set<Id> setGroupSocietyCIP = new Set<Id>();
        Set<Id> setPublicGroupIdCIP = new Set<Id>();
        Set<Id> setPublicGroupId = new Set<Id>();
        for (SObject sObj : newSobjLst) {
            SObject sObjOld = (SObject) oldSobjMap.get((Id) sObj.get(ID));

            // PARANOIA CHECK: il cambio è sempra da APERTO a CHIUSO
            if (ConiAssignmentHelper.checkIfStatusReopen(sObj, sObjOld)) {
                //throw new ConiAssignmentException(Label.ConiAssignmentExceptionApertoChiuso);
                //throw new AuraHandledException('ERRORE');
                continue;
            }

            // 1. Get utente / gruppo assegnato
            Id userOrGroupId = sObj.get(ASSIGNED_GROUP) != null ? (Id) sObj.get(ASSIGNED_GROUP) : (Id) sObj.get(ASSIGNED_TO);
            setIdAssigned.add(userOrGroupId);
            Id accountId = sObj.get(ACCOUNT_ID) != null ? (Id) sObj.get(ACCOUNT_ID) : null;
            setAccountId.add(accountId);
            Id agenziaId = sObj.get(AGENCY_FIELD) != null ? (Id) sObj.get(AGENCY_FIELD) : null;
            setAgenziaId.add(agenziaId);
            Id idObject = (Id) sObj.get(ID);
            setObjectId.add(idObject);
            Id groupId = sObj.get(ASSIGNED_GROUP) != null ? (Id) sObj.get(ASSIGNED_GROUP) : null;
            setGroupId.add(groupId);
        }

        for (Group__c gr : [SELECT Id, Society__c, CIP__c, PublicGroupId__c, RecordTypeId FROM Group__c WHERE Id IN :setGroupId]) {
            //setGroupIdCIP.add(gr.Id);
            if (String.valueOf(CIP).equalsIgnoreCase(gr.RecordTypeId)) {
                String cipPadLeft = gr.CIP__c?.leftPad(5, '0');
                setGroupNameCIP.add(cipPadLeft);
                setGroupSocietyCIP.add(gr.Society__c);
                setPublicGroupIdCIP.add(gr.PublicGroupId__c);
            }
            setPublicGroupId.add(gr.PublicGroupId__c);
        }

        // 2. Set READ sharing rule esistente su sObj per utente / gruppo assegnato
        ConiAssignmentHelper.updateShareInRead(setObjectId, setAccountId, setAgenziaId);

        // 3. Verifica abbinamento: esiste almeno una attività (adc, opp) attiva per cliente in agenzia assegnata a utente / gruppo
        Boolean noAbbinamento = ConiAssignmentHelper.verificaAbbinamento(setAccountId, setAgenziaId, setIdAssigned, setGroupNameCIP, setGroupSocietyCIP);

        // 3a. SI
        // Non fare nulla
        // 3b. NO
        if (noAbbinamento) {
            // Togli abbinamento a utente / gruppo in agenzia sul cliente
            // 3b.1 Delete sharing rule (devono esistere solo in READ) tra le attività del cliente in agenzia e utente / gruppo
            // 3b.1 Delete sharing rule AAR tra cliente e agenzia e utente / gruppo
            if (!setPublicGroupIdCIP.isEmpty()) {
                setIdAssigned.addAll(setPublicGroupIdCIP);
            }
            if (!setPublicGroupId.isEmpty()) {
                setIdAssigned.addAll(setPublicGroupId);
            }
            ConiAssignmentHelper.deleteRecordShare(setAccountId, setAgenziaId, setIdAssigned);

            Set<String> setStringUserOrGroup = ConiAssignmentHelper.deserializeSetIdInSetString(setIdAssigned);
            Set<String> setStringAccountId = ConiAssignmentHelper.deserializeSetIdInSetString(setAccountId);
            Set<String> setStringAgenziaId = ConiAssignmentHelper.deserializeSetIdInSetString(setAgenziaId);
            ConiAssignmentHelper.deleteShareAAR(setStringUserOrGroup, setStringAccountId, setStringAgenziaId);
        }
    }

    public static Boolean managePerformShares(List<SObject> newSobjLst, Map<Id, SObject> newSobjMap, Map<Id, SObject> oldSobjMap) {
        if (ConiQueRefUpdate.isQueueableUpdate) {
            System.debug('>>>> performShares in queuable');
            return true;
        }

        if (Trigger.isBefore && Trigger.isInsert) {
            System.debug('>>>> trigger before insert');
            return true;
        }

        System.debug('>>>> performShares: ' + System.now());
        Set<Id> setAccsObj = new Set<Id>();
        List<SObject> lstAux = new List<SObject>();
        Map<Id, Map<String, String>> mapAux = new Map<Id, Map<String, String>>();
        // Set<Id> setAccountId = new Set<Id>();
        // Map<Id, Id> groupUserIds = new Map<Id, Id>();
        Boolean isReassignment = false;
        Set<Id> refUpdateId = new Set<Id>();

        Map<String, String> fields;
        String objShareName;
        String objName;

        // Set<String> oldAgencyId = new Set<String>();
        // Set<String> oldAccountId = new Set<String>();
        // Boolean isClosed = false;
        // List<SObject> listIsClosed = new List<SObject>();

        if (!newSobjLst.isEmpty()) {
            //AC: assegno il nome e i campi
            objName = newSobjLst.get(0).getSObjectType().getDescribe().getName();
            objShareName = objName + SHARE;
            fields = fieldNameMap.get(objShareName);
            //Map<Id, Id> grpMap = new Map<Id, Id>();
            Map<Id, Set<Id>> grpMap = new Map<Id, Set<Id>>();
            //TODO
            Set<Id> setOldGroup = new Set<Id>();
            //TODO
            for (SObject sObj : newSobjLst) {
                SObject sObjOld = null;
                if (Trigger.isUpdate) {
                    sObjOld = (SObject) oldSobjMap.get((Id) sObj.get(ID));
                }
                //System.debug('>>>> sObj.isSetRef__c: ' + sObj.get(IS_SET_REF));
                //System.debug('>>>> sObjOld: ' + sObjOld);

                if (sObj.get(AGENCY_FIELD) != null) {
                    System.debug('AGENCY_FIELD valorizzato');
                    //AC: in fase di inserimetno creo il referente
                    SObject obEdit = setReferentsGroup((Boolean) sObj.get(IS_SET_REF), (Id) sObj.get(AGENCY_FIELD), (Id) sObj.get(ID), objShareName);
                    System.debug('**** obEdit : ' + obEdit);
                    if (obEdit != null) {
                        //System.debug('>>>> lstAux.add: ' + obEdit);
                        lstAux.add(obEdit);
                        mapAux.put((Id) sObj.get(ID), addMapAux(fields, System.Label.EditAccessLevel, (Id) sObj.get(ID), (Id) obEdit.get(System.Label.UserOrGroupId)));
                        refUpdateId.add((Id) sObj.get(ID));
                        System.debug('**** lstAux : ' + JSON.serializePretty(lstAux));
                        System.debug('**** mapAux : ' + JSON.serializePretty(mapAux));
                    }
                    //AC: verificare anche la nuova sObj abbia l'assegnatario diverso da old
                    if (sObj.get(ASSIGNED_GROUP) != null) {
                        //SObject objEdit = getObject(System.Label.EditAccessLevel, (Id) sObj.get(ID), (Id) sObj.get(ASSIGNED_GROUP));
                        // Add the new share to the list of shares to be inserted
                        //lstAux.add(objEdit);
                        // Put the Opportunity Id and the EDIT share record in the Aux map
                        //mapAux.put((Id) sObj.get(ID), addMapAux(fields, System.Label.EditAccessLevel, (Id) sObj.get(ID), (Id) sObj.get(ASSIGNED_GROUP)));

                        if (!grpMap.containsKey((Id) sObj.get(ASSIGNED_GROUP))) {
                            Set<Id> objIds = new Set<Id>{ (Id) sObj.get(ID) };
                            grpMap.put((Id) sObj.get(ASSIGNED_GROUP), objIds);
                        } else {
                            grpMap.get((Id) sObj.get(ASSIGNED_GROUP)).add((Id) sObj.get(ID));
                        }

                        //grpMap.put((Id) sObj.get(ASSIGNED_GROUP), (Id)sObj.get(ID));
                    }
                    //AC: verificare anche la nuova sObj abbia l'assegnatario diverso da old
                    else if (sObj.get(ASSIGNED_TO) != null) {
                        SObject objEdit = getObject(System.Label.EditAccessLevel, (Id) sObj.get(ID), (Id) sObj.get(ASSIGNED_TO));
                        // Add the new share to the list of shares to be inserted
                        // Add the Opportunity Id and the EDIT share record in the Aux map
                        //System.debug('>>>> lstAux.add: ' + objEdit);
                        lstAux.add(objEdit);
                        mapAux.put((Id) sObj.get(ID), addMapAux(fields, System.Label.EditAccessLevel, (Id) sObj.get(ID), (Id) sObj.get(ASSIGNED_TO)));
                        System.debug('**** lstAux : ' + JSON.serializePretty(lstAux));
                        System.debug('**** mapAux : ' + JSON.serializePretty(mapAux));
                    }
                    if (Trigger.isUpdate) {
                        isReassignment = checkReassignment(sObj, sObjOld);
                    }
                    //System.debug('>>>> mapAux = ' + mapAux);
                    if (!setAccsObj.contains((Id) sObj.get(ACCOUNT_ID))) {
                        setAccsObj.add((Id) sObj.get(ACCOUNT_ID));
                    }

                    //TODO
                    if (sObjOld != null && sObjOld.get(ASSIGNED_GROUP) != null) {
                        if (!grpMap.containsKey((Id) sObjOld.get(ASSIGNED_GROUP))) {
                            Set<Id> objIds = new Set<Id>{ (Id) sObjOld.get(ID) };
                            grpMap.put((Id) sObjOld.get(ASSIGNED_GROUP), objIds);
                        } else {
                            grpMap.get((Id) sObjOld.get(ASSIGNED_GROUP)).add((Id) sObjOld.get(ID));
                        }
                        setOldGroup.add((Id) sObjOld.get(ASSIGNED_GROUP));
                    }
                    //TODO
                }
                // if (sObjOld != null && sObj.get(AGENCY_FIELD) != sObjOld.get(AGENCY_FIELD) && sObjOld.get(AGENCY_FIELD) != null) {
                //     System.debug('**** CAMBIO AGENZIA');
                //     oldAgencyId.add((String) sObjOld.get(AGENCY_FIELD));
                //     oldAccountId.add((String) sObjOld.get(ACCOUNT_ID));
                // }
            }
            Map<Id, Id> grpId = new Map<Id, Id>();
            Map<Id, Id> grpIdEdit = new Map<Id, Id>();
            if (!grpMap.isEmpty()) {
                List<Group__c> grp = [SELECT Id, PublicGroupId__c FROM Group__c WHERE Id IN :grpMap.keySet()];
                for (Group__c g : grp) {
                    //TODO
                    if (!setOldGroup.contains(g.Id)) {
                        Set<Id> objIds = grpMap.get(g.Id);
                        for (Id currObjId : objIds) {
                            grpId.put(currObjId, g.PublicGroupId__c);
                        }
                    }
                    //TODO
                    //grpId.put(grpMap.get(g.Id), g.PublicGroupId__c);
                    grpIdEdit.put(g.Id, g.PublicGroupId__c);
                }
                for (Id gId : grpId.keySet()) {
                    //System.debug('>>> gId: ' + grpId.get(gId));
                    SObject objEditGrp = getObject(System.Label.EditAccessLevel, gId, grpId.get(gId));
                    // Add the new share to the list of shares to be inserted
                    lstAux.add(objEditGrp);
                    // Put the Opportunity Id and the EDIT share record in the Aux map
                    mapAux.put((Id) gId, addMapAux(fields, System.Label.EditAccessLevel, gId, (Id) grpId.get(gId)));
                }
            }
            if (!setAccsObj.isEmpty()) {
                //System.debug('>>>> setAccsObj = ' + setAccsObj);
                Map<String, Object> shareData = prepareData(setAccsObj);
                Map<Id, Set<Id>> accountIdToMatchedUserOrGroupIds = (Map<Id, Set<Id>>) shareData.get(ACCOUNT_KEY);
                Map<String, Map<Id, SObject>> editMap = (Map<String, Map<Id, SObject>>) shareData.get(EDIT_MAP_KEY);
                Map<String, Map<Id, SObject>> readMap = (Map<String, Map<Id, SObject>>) shareData.get(READ_MAP_KEY);
                System.debug('**** accountIdToMatchedUserOrGroupIds : ' + JSON.serializePretty(accountIdToMatchedUserOrGroupIds));
                System.debug('**** editMap : ' + JSON.serializePretty(editMap));
                System.debug('**** readMap : ' + JSON.serializePretty(readMap));
                //AC: aggiungo a editMap l'attuale edit
                for (Id sObjId : mapAux.keySet()) {
                    Map<String, String> priv = mapAux.get(sObjId);
                    String userOrGroupId = priv.get(System.Label.UserOrGroupId);
                    String key = sObjId + '_' + userOrGroupId;
                    SObject editMapValue = getObject(System.Label.EditAccessLevel, sObjId, userOrGroupId);
                    Map<Id, SObject> valueMap = new Map<Id, SObject>{ userOrGroupId => editMapValue };
                    editMap.put(key, valueMap);
                    //AC: aggiungo agli account l'attuale sObj
                    SObject o = newSobjMap.get(sObjId);
                    Set<Id> matchedUserOrGroupIds = accountIdToMatchedUserOrGroupIds.get((Id) o.get(ACCOUNT_ID));
                    if (matchedUserOrGroupIds != null) {
                        matchedUserOrGroupIds.add(userOrGroupId);
                    }
                }

                //Dare un nome più parlante perchè il valore restituito nella lista contiene oggetti con share tipo Read
                Map<String, SObject> mapEditPriv = null;
                if (accountIdToMatchedUserOrGroupIds != null) {
                    mapEditPriv = editPriviliges2(accountIdToMatchedUserOrGroupIds, editMap, readMap, newSobjLst, oldSobjMap, grpIdEdit);
                }
                //System.debug('>>>> mapEditPriv: ' + mapEditPriv);
                Map<String, SObject> mapAuxPriv = lstToMap(lstAux);
                //System.debug('>>>> mapAuxPriv: ' + mapAuxPriv);
                for (String objId : mapEditPriv.keySet()) {
                    String[] objIdSplit = objId.split('_');
                    Boolean add = true;
                    if (objIdSplit[2].equals(EDIT)) {
                        String key = objIdSplit[0] + '_' + objIdSplit[1];
                        if (mapAuxPriv.containsKey(key + '_' + READ)) {
                            mapAuxPriv.remove(key);
                        } else if (mapAuxPriv.containsKey(key + '_' + EDIT)) {
                            add = false;
                        }
                    }
                    if (add && !mapAuxPriv.containsKey(objId)) {
                        lstAux.add(mapEditPriv.get(objId));
                    }
                }

                //System.debug('>>>> accountIdToMatchedUserOrGroupIds = ' + accountIdToMatchedUserOrGroupIds);
                Map<Id, Set<String>> mapSetAuxIDs = new Map<Id, Set<String>>();

                // Building the map that will have the Opportunity Id as the key and the set of IDs of the matched UsersOrGroups as the value to create the READ shares.
                if (accountIdToMatchedUserOrGroupIds != null) {
                    for (SObject sObj : newSobjLst) {
                        if (accountIdToMatchedUserOrGroupIds.containsKey((Id) sObj.get(ACCOUNT_ID)) && !editMap.isEmpty()) {
                            //System.debug('>>>> editMap: ' + editMap);
                            for (String key : editMap.keySet()) {
                                String[] sObjUG = key.split('_');
                                if (sObjUG[0].equals(sObj.get(ID)) && editMap.containsKey(key)) {
                                    Map<Id, SObject> editObj = editMap.get(key);
                                    String keyEditObj = editObj.keySet().iterator().next();
                                    SObject value = editObj.get(keyEditObj);
                                    if (oldSobjMap != null && oldSobjMap.containsKey((Id) sObj.get(ID))) {
                                        if (checkAddFromEditMap(sObj, oldSobjMap.get((Id) sObj.get(ID)), value)) {
                                            if (mapSetAuxIDs.containsKey((Id) sObj.get(ID))) {
                                                Set<String> setA = mapSetAuxIDs.get((String) sObj.get(ID));
                                                setA.add(sObjUG[1]);
                                            } else {
                                                mapSetAuxIDs.put(sObj.Id, new Set<String>{ sObjUG[1] });
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }
                    if (mapSetAuxIDs != null && !mapSetAuxIDs.isEmpty() && mapSetAuxIDs != null) {
                        //System.debug('>>>> mapSetAuxIDs = ' + mapSetAuxIDs);
                        for (Id sObjId : mapSetAuxIDs.keySet()) {
                            Set<String> setAux = mapSetAuxIDs.get(sObjId);
                            if (setAux != null && !setAux.isEmpty()) {
                                for (String idUsrGrpKey : setAux) {
                                    if (checkIfIdIsInvalid(idUsrGrpKey)) {
                                        continue;
                                    }
                                    if (String.isNotEmpty(idUsrGrpKey) && !mapAux.containsKey(sObjId)) {
                                        SObject obj = getObject(System.Label.ReadAccessLevel, sObjId, idUsrGrpKey);
                                        //System.debug('>>>> lstAux.add: ' + obj);
                                        lstAux.add(obj);
                                    } else if (mapAux.containsKey(sObjId)) {
                                        Map<String, String> mapOppAux = mapAux.get(sObjId);
                                        //System.debug('>>>> mapOppAux: ' + mapOppAux);
                                        //System.debug('>>>> String.valueOf(idUsrGrpKey): ' + String.valueOf(idUsrGrpKey));
                                        //System.debug('>>>> sObjId: ' + sObjId);
                                        System.debug('***** deleteIdOld: ' + deleteIdOld);
                                        System.debug('***** idUsrGrpKey: ' + idUsrGrpKey);
                                        if (String.isNotEmpty(idUsrGrpKey) && mapOppAux.get(System.Label.UserOrGroupId) != String.valueOf(idUsrGrpKey) && !deleteIdOld.contains((Id) idUsrGrpKey)) {
                                            SObject obj = getObject(System.Label.ReadAccessLevel, sObjId, idUsrGrpKey);
                                            //System.debug('>>>> lstAux.add: ' + obj);
                                            lstAux.add(obj);
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }
            // if (!oldAgencyId.isEmpty()) {
            //     System.debug('**** STO PER ELIMINARE LE AAR AL CAMBIO AGENZIA');
            //     ConiAssignmentHelper.deleteForChangeAgencyShareAAR(oldAgencyId, oldAccountId);
            // }
        }
        System.debug('### DEVCAP => lstAux = ' + lstAux);
        System.debug('### DEVCAP => refUpdateId = ' + refUpdateId);
        System.debug('### DEVCAP => objName = ' + objName);
        return persistList(lstAux, refUpdateId, objName, mapStringARR);
    }

    private static Boolean checkIfIdIsInvalid(String idUsrGrpKey) {
        if (String.isBlank(idUsrGrpKey)) {
            return true;
        }
        String regex = '^[a-zA-Z0-9]{15}(?:[a-zA-Z0-9]{3})?$';
        System.debug('#### checkIfIdIsInvalid: ' + idUsrGrpKey);
        System.debug('#### checkIfIdIsInvalid isInvalid: ' + !Pattern.matches(regex, idUsrGrpKey));
        return !Pattern.matches(regex, idUsrGrpKey);
    }
}
