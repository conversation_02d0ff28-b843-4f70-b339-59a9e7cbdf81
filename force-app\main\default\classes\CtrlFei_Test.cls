@isTest
public class CtrlFei_Test {

    @testSetup
	static void setup() {
		// Creazione di un utente di test
        User testUser = new User(
            Alias = 'testuser',
            Email = '<EMAIL>',
            EmailEncodingKey = 'UTF-8',
            LastName = 'User',
            LanguageLocaleKey = 'en_US',
            LocaleSidKey = 'en_US',
            ProfileId = [SELECT Id FROM Profile WHERE Name='Standard User' LIMIT 1].Id,
            TimeZoneSidKey = 'America/Los_Angeles',
            UserName = 'testuser@exampleCtrlFei_Test.com',
            FederationIdentifier = '1234567890'
        );
        insert testUser;
    
        // Creazione di NetworkUser__c di test
        NetworkUser__c nw = new NetworkUser__c(
            NetworkUser__c = 'TestNetworkUser',
            FiscalCode__c = '1234567890'
        );
        insert nw;
     }
 
    public class NetworkUserWrapper {
		public String label { get; set; }
     	public String value { get; set; }
    }
 
    @isTest
    static void testNetworkUserLogic() {
		// Impostazione dell'utente di test come utente corrente
        User testUser = [SELECT Id FROM User WHERE FederationIdentifier = '1234567890' LIMIT 1];
        System.runAs(testUser) {
            Test.startTest();
            CtrlFeiUserSelect.getPicklistUsers('Variazione_generica',null);
            CtrlFeiUserSelect.getPicklistUsers('Variazione_generica','SOC_1');
            // Esegui la logica da testare
            User currentUser = [SELECT Id, FederationIdentifier FROM User WHERE Id = :UserInfo.getUserId() LIMIT 1];
            System.debug(currentUser);
            List<NetworkUser__c> nwList = [SELECT Id, NetworkUser__c FROM NetworkUser__c WHERE FiscalCode__c = :currentUser.FederationIdentifier];
            System.debug(nwList);
            
            List<NetworkUserWrapper> result = new List<NetworkUserWrapper>();
            for(NetworkUser__c nw : nwList) {
                NetworkUserWrapper nuw = new NetworkUserWrapper();
                nuw.label = nw.NetworkUser__c;
                nuw.value = nw.NetworkUser__c;
                result.add(nuw);
            }

        	Test.stopTest();
        }
     }
    
    @isTest
    static void test_container(){

        Test.startTest();

        Account acc = new Account();
        acc.Name = 'TEST';
        insert acc;

        InsurancePolicy ip = new InsurancePolicy();
        ip.Name = 'TEST';
        ip.PolicyName = 'VITA.POLIZZA';
        ip.NameInsuredId = acc.Id;
        ip.CIP__c = 'TEST';
        ip.Company__c = 'TEST';
        ip.CompanyCode__c = 'TEST';
        ip.Society__c = acc.Id;
        insert ip; 

        try{

            CtrlFeiContainer.getCurrentInsurancePolicy(ip.Id);

        }catch(Exception ex){
            System.debug('Exception --> '+ex.getMessage());
            System.debug('Exception --> '+ex.getStackTraceString());
        }

        Test.stopTest();

    }

    @isTest
    static void test_UserSelect(){

        Test.startTest();

        Account acc = new Account();
        acc.Name = 'TEST';
        insert acc;

        InsurancePolicy ip = new InsurancePolicy();
        ip.Name = 'TEST';
        ip.PolicyName = 'VITA.POLIZZA';
        ip.NameInsuredId = acc.Id;
        ip.CIP__c = 'TEST';
        ip.Company__c = 'TEST';
        ip.CompanyCode__c = 'TEST';
        ip.Society__c = acc.Id;
        insert ip; 

        try{

            CtrlFeiUserSelect.getPicklistUsers('Variazione_generica',null);

        }catch(Exception ex){
            System.debug('Exception --> '+ex.getMessage());
            System.debug('Exception --> '+ex.getStackTraceString());
        }

        try{

            CtrlFeiUserSelect.getPicklistUsers('Variazione_generica','SOC_1');

        }catch(Exception ex){
            System.debug('Exception --> '+ex.getMessage());
            System.debug('Exception --> '+ex.getStackTraceString());
        }

        Test.stopTest();

    }

    @isTest
    static void test_Utils(){

        Test.startTest();

        Account acc = new Account();
        acc.Name = 'TEST';
        insert acc;

        InsurancePolicy ip = new InsurancePolicy();
        ip.Name = 'TEST';
        ip.PolicyName = 'VITA.POLIZZA';
        ip.NameInsuredId = acc.Id;
        ip.CIP__c = 'TEST';
        ip.Company__c = 'TEST';
        ip.CompanyCode__c = 'TEST';
        ip.Society__c = acc.Id;
        insert ip; 

        try{

            CtrlFEIUtils utils = new CtrlFEIUtils();
            utils.call('test', new Map<String, Object>());

        }catch(Exception ex){
            System.debug('Exception --> '+ex.getMessage());
            System.debug('Exception --> '+ex.getStackTraceString());
        }

        try{

            CtrlFEIUtils utils = new CtrlFEIUtils();
            utils.call('replacePlaceholder', new Map<String, Object>());

        }catch(Exception ex){
            System.debug('Exception --> '+ex.getMessage());
            System.debug('Exception --> '+ex.getStackTraceString());
        }
        
        try{

            CtrlFEIUtils utils = new CtrlFEIUtils();
            utils.call('replaceCONTID', new Map<String, Object>());

        }catch(Exception ex){
            System.debug('Exception --> '+ex.getMessage());
            System.debug('Exception --> '+ex.getStackTraceString());
        }
        
        try{

            CtrlFEIUtils utils = new CtrlFEIUtils();
            utils.call('replaceCONTIDExtended', new Map<String, Object>());

        }catch(Exception ex){
            System.debug('Exception --> '+ex.getMessage());
            System.debug('Exception --> '+ex.getStackTraceString());
        }
        
        try{

            CtrlFEIUtils utils = new CtrlFEIUtils();
            utils.call('sendFeiRequest', new Map<String, Object>());

        }catch(Exception ex){
            System.debug('Exception --> '+ex.getMessage());
            System.debug('Exception --> '+ex.getStackTraceString());
        }
        
        try{

            CtrlFEIUtils utils = new CtrlFEIUtils();
            utils.call('sendFEARequest', new Map<String, Object>());

        }catch(Exception ex){
            System.debug('Exception --> '+ex.getMessage());
            System.debug('Exception --> '+ex.getStackTraceString());
        }
        
        try{

            CtrlFEIUtils utils = new CtrlFEIUtils();
            utils.call('sendLINKPOSTRequest', new Map<String, Object>());

        }catch(Exception ex){
            System.debug('Exception --> '+ex.getMessage());
            System.debug('Exception --> '+ex.getStackTraceString());
        }

        Test.stopTest();

    }

}