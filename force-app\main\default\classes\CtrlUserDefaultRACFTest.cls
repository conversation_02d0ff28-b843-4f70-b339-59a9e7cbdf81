@isTest
public class CtrlUserDefaultRACFTest {
    
    @testSetup
    static void setup() {
       	
        Id agencyRecordTypeId = Schema.SObjectType.Account.getRecordTypeInfosByDeveloperName().get('Agency').getRecordTypeId();
        Account accountAgenzia1 = new Account(
            Name = 'Agenzia 1', RecordTypeId = agencyRecordTypeId,  
            ExternalId__c = 'AGE_1'
        );
        insert accountAgenzia1;
        
        NetworkUser__c nu = new NetworkUser__c();
        nu.NetworkUser__c = '000000';
        nu.FiscalCode__c = 'TESTRACF';
        nu.IsActive__c = true;
        nu.Profile__c = 'A';
        nu.Society__c = 'SOC_1';
        nu.Agency__c = accountAgenzia1.Id;

        NetworkUser__c nu2 = new NetworkUser__c();
        nu2.NetworkUser__c = '000001';
        nu2.FiscalCode__c = 'TESTRACF';
        nu2.IsActive__c = true;
        nu2.Profile__c = 'A';
        nu2.Society__c = 'SOC_1';
        nu2.Agency__c = accountAgenzia1.Id;

        NetworkUser__c nu3 = new NetworkUser__c();
        nu3.NetworkUser__c = '000002';
        nu3.FiscalCode__c = null;
        nu3.IsActive__c = true;
        nu3.Profile__c = 'A';
        nu3.Society__c = 'SOC_1';
        nu3.Agency__c = accountAgenzia1.Id;
        
        NetworkUser__c nu4 = new NetworkUser__c();
        nu4.NetworkUser__c = '000003';
        nu4.FiscalCode__c = 'TESTRACF';
        nu4.IsActive__c = true;
        nu4.Profile__c = 'A';
        nu4.Society__c = 'SOC_4';
        nu4.Agency__c = accountAgenzia1.Id;
        
        insert new List<NetworkUser__c>{ nu, nu2, nu3, nu4 };

    }

    @isTest
    static void test_coverage(){
        
        Test.startTest();
        
        User u = new User();
        u.FirstName = 'Test2';
        u.LastName = 'TestLastName2RACF';
        u.FederationIdentifier = 'TEST';
        u.ProfileId = [SELECT Id FROM Profile WHERE Name = 'System Administrator' LIMIT 1].Id;
        u.Email = '<EMAIL>';
        u.Username = 'test' + System.now().getTime() + '@test.it.invalid3.racf';
        u.Alias = 'racf';
        u.TimeZoneSidKey = 'Europe/Rome';
        u.EmailEncodingKey = 'UTF-8';
        u.LanguageLocaleKey = 'en_US';
        u.LocaleSidKey = 'it_IT';
        u.FiscalCode__c = 'TESTRACF';
        u.FederationIdentifier = 'TESTRACF';
        insert u;
        
        System.runAs(u){
            
            try{
                CtrlUserDefaultRACF.getData();
            }catch(Exception e){}
            
            try{
                NetworkUser__c nu = [SELECT Id, Society__c FROM NetworkUser__c LIMIT 1];
                CtrlUserDefaultRACF.setPreferredNetworkUser(nu.Id, nu.Society__c);
            }catch(Exception ex){}
            
        }
        
        Test.stopTest();
        
    }
    
}