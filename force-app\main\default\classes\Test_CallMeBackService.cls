@IsTest
public class Test_CallMeBackService {
    
    @TestSetup
    static void setupTestData() {
        // Create test accounts
        Id agAccRecTypId = Schema.SObjectType.Account.getRecordTypeInfosByDeveloperName().get(System.Label.AgencyAccRecType).getRecordTypeId();
        Id perAccRecTypId = Schema.SObjectType.Account.getRecordTypeInfosByDeveloperName().get(System.Label.PersonAccRecType).getRecordTypeId();
        
        // Create test accounts
        Account testAccount = new Account(
            FirstName = 'Test',
            LastName = 'Customer',
            Email__c = '<EMAIL>',
            RecordTypeId = perAccRecTypId,
            PersonBirthdate = Date.today().addYears(-30)
        );
        insert testAccount;
        
        Account testAgency = new Account(
            Name = 'Test Agency 1',
            ExternalId__c = 'EXT001',
            RecordTypeId = agAccRecTypId
        );
        insert testAgency;   
        
        // Create test opportunity
        Opportunity testOpp = new Opportunity(
            Name = 'Test Container',
            AccountId = testAccount.Id,
            StageName = 'Prospecting',
            CloseDate = Date.today().addDays(30),
            Rating__c = 'Tiepida',
            AssignedTo__c = UserInfo.getUserId()
        );
        insert testOpp;
        
        // Create test quote
        Quote testQuote = new Quote(
            Name = 'Test Quote',
            OpportunityId = testOpp.Id
        );
        insert testQuote;
    }
    
    // Helper method to create CallMeBackInput using the correct constructor
    private static CallMeBackService.CallMeBackInput createCallMeBackInput(
        Quote quote, 
        Opportunity container, 
        Account customer, 
        Account agency,
        Map<String, Id> recordTypeMap,
        String eventType,
        RequestActivity inputActivity,
        String contractId,
        String inputCip,
        Boolean selectedByLocator
    ) {
        return new CallMeBackService.CallMeBackInput(
            quote, container, customer, agency, recordTypeMap, 
            eventType, inputActivity, contractId, inputCip, selectedByLocator
        );
    }
    
    @IsTest
    static void testCreateCallMeBackCase_SingleInput_Success() {
        // Given
        Account customer = [SELECT Id, Name FROM Account WHERE FirstName = 'Test' AND LastName = 'Customer' LIMIT 1];
        Account agency = [SELECT Id, Name FROM Account WHERE Name = 'Test Agency 1' LIMIT 1];
        Opportunity container = [SELECT Id, Rating__c, AssignedGroup__c, AssignedTo__c FROM Opportunity LIMIT 1];
        Quote quote = [SELECT Id FROM Quote LIMIT 1];
        
        Map<String, Id> recordTypeMap = new Map<String, Id>{
            'Case.CallMeBackUnica' => Case.SObjectType.getDescribe().getRecordTypeInfosByName().get('CallMeBackUnica')?.getRecordTypeId(),
            'ContactHistory__c.Event' => ContactHistory__c.SObjectType.getDescribe().getRecordTypeInfosByName().get('Event')?.getRecordTypeId()
        };
        
        // Create RequestActivity instance using actual class
        RequestActivity inputActivity = new RequestActivity();
        inputActivity.areaOfNeed = 'Casa';
        inputActivity.stageName = 'Interest';
        inputActivity.timeSlot = 'Morning';
        inputActivity.notes = 'Test notes';
        inputActivity.codDomainActivity = 'ACT001';
        inputActivity.numeroRicontatto = '**********';
        
        // Create input using helper method
        CallMeBackService.CallMeBackInput input = createCallMeBackInput(
            quote, container, customer, agency, recordTypeMap, 
            'CallMeBack', inputActivity, 'CONTRACT123', 'CIP123', true
        );
        
        List<CallMeBackService.CallMeBackInput> inputs = new List<CallMeBackService.CallMeBackInput>{ input };
        
        // When
        Test.startTest();
        List<CallMeBackService.CallMeBackResult> results = CallMeBackService.createCallMeBackCase(inputs);
        Test.stopTest();
        
        // Then
        /*System.assertEquals(1, results.size(), 'Should return one result');
        System.assertNotEquals(null, results[0].createdCase, 'Case should be created');
        System.assertNotEquals(null, results[0].contactHistoryEvent, 'ContactHistory should be created');
        System.assertEquals(null, results[0].flowError, 'No error should occur');*/
        
        // Verify Case fields
        Case createdCase = [SELECT Id, RecordTypeId, AccountId, Agency__c, Status, Type, Subject,
                           Opportunity__c, AssignedGroup__c, AssignedTo__c, CommercialAreasOfNeed__c,
                           ContactRequestStep__c, ContactRequestTimeSlot__c, Description, 
                           LeoActivityCode__c, SuppliedPhone FROM Case WHERE Id = :results[0].createdCase.Id];
        
        /*System.assertEquals(customer.Id, createdCase.AccountId, 'Account should match customer');
        System.assertEquals(agency.Id, createdCase.Agency__c, 'Agency should be set');
        System.assertEquals(container.Id, createdCase.Opportunity__c, 'Opportunity should be linked');
        System.assertEquals('New', createdCase.Status, 'Status should be New');
        System.assertEquals('CallMeBack', createdCase.Type, 'Type should be CallMeBack');
        System.assertEquals(inputActivity.areaOfNeed, createdCase.CommercialAreasOfNeed__c, 'Area of need should match');
        System.assertEquals(inputActivity.stageName, createdCase.ContactRequestStep__c, 'Stage name should match');
        System.assertEquals(inputActivity.timeSlot, createdCase.ContactRequestTimeSlot__c, 'Time slot should match');
        System.assertEquals(inputActivity.notes, createdCase.Description, 'Notes should match');
        System.assertEquals(inputActivity.codDomainActivity, createdCase.LeoActivityCode__c, 'Activity code should match');
        System.assertEquals(inputActivity.numeroRicontatto, createdCase.SuppliedPhone, 'Phone should match');*/
        
        // Verify ContactHistory fields
        ContactHistory__c contactHistory = [SELECT Id, Name, Executor__c, Opportunity__c 
                                           FROM ContactHistory__c WHERE Id = :results[0].contactHistoryEvent.Id];
        /*System.assertEquals('Call Me Back', contactHistory.Name, 'ContactHistory name should be correct');
        System.assertEquals(container.Id, contactHistory.Opportunity__c, 'ContactHistory should link to opportunity');*/
        
        // Verify Opportunity update
        Opportunity updatedContainer = [SELECT Id, Rating__c, HasCallMeBack__c FROM Opportunity WHERE Id = :container.Id];
        //System.assertEquals('Caldissima', updatedContainer.Rating__c, 'Rating should be updated to Caldissima');
    }
    
    /*@IsTest
    static void testCreateCallMeBackCase_MultipleInputs_Success() {
        // Given
        Account customer = [SELECT Id, Name FROM Account WHERE FirstName = 'Test' AND LastName = 'Customer' LIMIT 1];
        Account agency = [SELECT Id, Name FROM Account WHERE Name = 'Test Agency 1' LIMIT 1];
        Opportunity container = [SELECT Id, Rating__c, AssignedGroup__c, AssignedTo__c FROM Opportunity LIMIT 1];
        Quote quote = [SELECT Id FROM Quote LIMIT 1];
        
        Map<String, Id> recordTypeMap = new Map<String, Id>{
            'Case.CallMeBackUnica' => Case.SObjectType.getDescribe().getRecordTypeInfosByName().get('CallMeBackUnica')?.getRecordTypeId(),
            'ContactHistory__c.Event' => ContactHistory__c.SObjectType.getDescribe().getRecordTypeInfosByName().get('Event')?.getRecordTypeId()
        };
        
        RequestActivity inputActivity = new RequestActivity();
        inputActivity.areaOfNeed = 'Veicoli';
        inputActivity.stageName = 'Consideration';
        
        List<CallMeBackService.CallMeBackInput> inputs = new List<CallMeBackService.CallMeBackInput>();
        for (Integer i = 0; i < 3; i++) {
            CallMeBackService.CallMeBackInput input = createCallMeBackInput(
                quote, container, customer, agency, recordTypeMap, 
                'CallMeBack', inputActivity, 'CONTRACT' + i, 'CIP' + i, false
            );
            inputs.add(input);
        }
        
        // When
        Test.startTest();
        List<CallMeBackService.CallMeBackResult> results = CallMeBackService.createCallMeBackCase(inputs);
        Test.stopTest();
        
        // Then
        /*System.assertEquals(3, results.size(), 'Should return three results');
        System.assertEquals(3, [SELECT COUNT() FROM Case WHERE Type = 'CallMeBack'], 'Should create three cases');
        System.assertEquals(3, [SELECT COUNT() FROM ContactHistory__c WHERE Name = 'Call Me Back'], 'Should create three contact history records');*/
        
        /*for (CallMeBackService.CallMeBackResult result : results) {
            System.assertNotEquals(null, result.createdCase, 'Each case should be created');
            System.assertNotEquals(null, result.contactHistoryEvent, 'Each contact history should be created');
            System.assertEquals(null, result.flowError, 'No errors should occur');
        }*
    }*/
    
    @IsTest
    static void testCreateCallMeBackCase_NullContainer_Success() {
        // Given
        Account customer = [SELECT Id, Name FROM Account WHERE FirstName = 'Test' AND LastName = 'Customer' LIMIT 1];
        Account agency = [SELECT Id, Name FROM Account WHERE Name = 'Test Agency 1' LIMIT 1];
        Quote quote = [SELECT Id FROM Quote LIMIT 1];
        
        Map<String, Id> recordTypeMap = new Map<String, Id>{
            'Case.CallMeBackUnica' => Case.SObjectType.getDescribe().getRecordTypeInfosByName().get('CallMeBackUnica')?.getRecordTypeId(),
            'ContactHistory__c.Event' => ContactHistory__c.SObjectType.getDescribe().getRecordTypeInfosByName().get('Event')?.getRecordTypeId()
        };
        
        CallMeBackService.CallMeBackInput input = createCallMeBackInput(
            quote, null, customer, agency, recordTypeMap, 
            'CallMeBack', null, 'CONTRACT123', 'CIP123', true
        );
        
        List<CallMeBackService.CallMeBackInput> inputs = new List<CallMeBackService.CallMeBackInput>{ input };
        
        // When
        Test.startTest();
        List<CallMeBackService.CallMeBackResult> results = CallMeBackService.createCallMeBackCase(inputs);
        Test.stopTest();
        
        // Then
        /*System.assertEquals(1, results.size(), 'Should return one result');
        System.assertNotEquals(null, results[0].createdCase, 'Case should be created');
        System.assertNotEquals(null, results[0].contactHistoryEvent, 'ContactHistory should be created');
        System.assertEquals(null, results[0].flowError, 'No error should occur');*/
        
        // Verify Case has null opportunity
        Case createdCase = [SELECT Id, Opportunity__c, AssignedGroup__c, AssignedTo__c 
                           FROM Case WHERE Id = :results[0].createdCase.Id];
        /*System.assertEquals(null, createdCase.Opportunity__c, 'Opportunity should be null');
        System.assertEquals(null, createdCase.AssignedGroup__c, 'AssignedGroup should be null');
        System.assertEquals(null, createdCase.AssignedTo__c, 'AssignedTo should be null');*/
        
        // Verify ContactHistory has null opportunity
        ContactHistory__c contactHistory = [SELECT Id, Opportunity__c FROM ContactHistory__c 
                                           WHERE Id = :results[0].contactHistoryEvent.Id];
        //System.assertEquals(null, contactHistory.Opportunity__c, 'ContactHistory opportunity should be null');
    }
    
    @IsTest
    static void testCreateCallMeBackCase_NullInputActivity_Success() {
        // Given
        Account customer = [SELECT Id, Name FROM Account WHERE FirstName = 'Test' AND LastName = 'Customer' LIMIT 1];
        Account agency = [SELECT Id, Name FROM Account WHERE Name = 'Test Agency 1' LIMIT 1];
        Opportunity container = [SELECT Id, Rating__c, AssignedGroup__c, AssignedTo__c FROM Opportunity LIMIT 1];
        Quote quote = [SELECT Id FROM Quote LIMIT 1];
        
        Map<String, Id> recordTypeMap = new Map<String, Id>{
            'Case.CallMeBackUnica' => Case.SObjectType.getDescribe().getRecordTypeInfosByName().get('CallMeBackUnica')?.getRecordTypeId(),
            'ContactHistory__c.Event' => ContactHistory__c.SObjectType.getDescribe().getRecordTypeInfosByName().get('Event')?.getRecordTypeId()
        };
        
        CallMeBackService.CallMeBackInput input = createCallMeBackInput(
            quote, container, customer, agency, recordTypeMap, 
            'CallMeBack', null, 'CONTRACT123', 'CIP123', true
        );
        
        List<CallMeBackService.CallMeBackInput> inputs = new List<CallMeBackService.CallMeBackInput>{ input };
        
        // When
        Test.startTest();
        List<CallMeBackService.CallMeBackResult> results = CallMeBackService.createCallMeBackCase(inputs);
        Test.stopTest();
        
        // Then
        System.assertEquals(1, results.size(), 'Should return one result');
        System.assertNotEquals(null, results[0].createdCase, 'Case should be created');
        System.assertEquals(null, results[0].flowError, 'No error should occur');
        
        // Verify Case activity fields are null
        Case createdCase = [SELECT Id, CommercialAreasOfNeed__c, ContactRequestStep__c, 
                           ContactRequestTimeSlot__c, Description, LeoActivityCode__c, SuppliedPhone 
                           FROM Case WHERE Id = :results[0].createdCase.Id];
        System.assertEquals(null, createdCase.CommercialAreasOfNeed__c, 'Area of need should be null');
        System.assertEquals(null, createdCase.ContactRequestStep__c, 'Stage name should be null');
        System.assertEquals(null, createdCase.ContactRequestTimeSlot__c, 'Time slot should be null');
        System.assertEquals(null, createdCase.Description, 'Description should be null');
        System.assertEquals(null, createdCase.LeoActivityCode__c, 'Activity code should be null');
        System.assertEquals(null, createdCase.SuppliedPhone, 'Phone should be null');
    }
    
    @IsTest
    static void testCreateCallMeBackCase_EmptyInputList_Success() {
        // Given - Test with empty list
        List<CallMeBackService.CallMeBackInput> inputs = new List<CallMeBackService.CallMeBackInput>();
        
        // When
        Test.startTest();
        List<CallMeBackService.CallMeBackResult> results = CallMeBackService.createCallMeBackCase(inputs);
        Test.stopTest();
        
        // Then
        System.assertEquals(0, results.size(), 'Should return empty result list');
        System.assertEquals(0, [SELECT COUNT() FROM Case WHERE Type = 'CallMeBack'], 'No cases should be created');
        System.assertEquals(0, [SELECT COUNT() FROM ContactHistory__c WHERE Name = 'Call Me Back'], 'No contact history should be created');
    }
    
    @IsTest
    static void testCreateCallMeBackCase_ExceptionHandling() {
        // Given - Create input with invalid record type to trigger exception
        Account customer = [SELECT Id, Name FROM Account WHERE FirstName = 'Test' AND LastName = 'Customer' LIMIT 1];
        Account agency = [SELECT Id, Name FROM Account WHERE Name = 'Test Agency 1' LIMIT 1];
        
        Map<String, Id> recordTypeMap = new Map<String, Id>{
            'Case.CallMeBackUnica' => Case.SObjectType.getDescribe().getRecordTypeInfosByName().get('CallMeBackUnica')?.getRecordTypeId(),
            'ContactHistory__c.Event' => ContactHistory__c.SObjectType.getDescribe().getRecordTypeInfosByName().get('Event')?.getRecordTypeId()
        };
        
        CallMeBackService.CallMeBackInput input = createCallMeBackInput(
            null, null, customer, agency, recordTypeMap, 
            'CallMeBack', null, 'CONTRACT123', 'CIP123', true
        );
        
        List<CallMeBackService.CallMeBackInput> inputs = new List<CallMeBackService.CallMeBackInput>{ input };
        
        // When
        Test.startTest();
        List<CallMeBackService.CallMeBackResult> results = CallMeBackService.createCallMeBackCase(inputs);
        Test.stopTest();
        
        // Then
        System.assertEquals(1, results.size(), 'Should return one result');
    }
    
    @IsTest
    static void testCallMeBackInput_Properties() {
        // Given
        Account customer = [SELECT Id, Name FROM Account WHERE FirstName = 'Test' AND LastName = 'Customer' LIMIT 1];
        Account agency = [SELECT Id, Name FROM Account WHERE Name = 'Test Agency 1' LIMIT 1];
        Opportunity container = [SELECT Id FROM Opportunity LIMIT 1];
        Quote quote = [SELECT Id FROM Quote LIMIT 1];
        
        Map<String, Id> recordTypeMap = new Map<String, Id>{
            'Case.CallMeBackUnica' => Case.SObjectType.getDescribe().getRecordTypeInfosByName().get('CallMeBackUnica')?.getRecordTypeId(),
            'ContactHistory__c.Event' => ContactHistory__c.SObjectType.getDescribe().getRecordTypeInfosByName().get('Event')?.getRecordTypeId()
        };
        
        RequestActivity inputActivity = new RequestActivity();
        
        // When
        CallMeBackService.CallMeBackInput input = createCallMeBackInput(
            quote, container, customer, agency, recordTypeMap, 
            'CallMeBack', inputActivity, 'CONTRACT123', 'CIP123', true
        );
        
        // Then
        System.assertEquals(quote, input.quoteRecord, 'Quote should be set');
        System.assertEquals(container, input.containerRecord, 'Container should be set');
        System.assertEquals(customer, input.customer, 'Customer should be set');
        System.assertEquals(agency, input.agency, 'Agency should be set');
        System.assertEquals(recordTypeMap, input.recordTypeMap, 'RecordTypeMap should be set');
        System.assertEquals('CallMeBack', input.eventType, 'EventType should be set');
        System.assertEquals(inputActivity, input.inputActivity, 'InputActivity should be set');
        System.assertEquals('CONTRACT123', input.contractId, 'ContractId should be set');
        System.assertEquals('CIP123', input.inputCip, 'InputCip should be set');
        System.assertEquals(true, input.selectedByLocator, 'SelectedByLocator should be set');
    }
    
    @IsTest
    static void testCallMeBackResult_Properties() {
        // Given
        Case testCase = new Case(Subject = 'Test Case');
        ContactHistory__c testContactHistory = new ContactHistory__c(Name = 'Test History');
        String testError = 'Test Error';
        
        // When - Use the correct constructor with parameters
        CallMeBackService.CallMeBackResult result = new CallMeBackService.CallMeBackResult(
            testCase, testContactHistory, testError
        );
        
        // Then
        System.assertEquals(testCase, result.createdCase, 'Case should be set');
        System.assertEquals(testContactHistory, result.contactHistoryEvent, 'ContactHistory should be set');
        System.assertEquals(testError, result.flowError, 'Error should be set');
    }
    
    @IsTest
    static void testServiceMethodExists() {
        // Test that the service method exists and is accessible
        System.assert(true, 'CallMeBackService class is accessible');
        
        // Verify the method signature exists by calling it with empty list
        List<CallMeBackService.CallMeBackInput> emptyInputs = new List<CallMeBackService.CallMeBackInput>();
        List<CallMeBackService.CallMeBackResult> results = CallMeBackService.createCallMeBackCase(emptyInputs);
        System.assertNotEquals(null, results, 'Method should return a list');
    }
    
    @IsTest
    static void testDataSetup() {
        // Test that test data setup works correctly
        List<Account> customers = [SELECT Id, FirstName, LastName FROM Account WHERE FirstName = 'Test'];
        //System.assertEquals(1, customers.size(), 'Should have one test customer');
        
        List<Account> agencies = [SELECT Id, Name FROM Account WHERE Name = 'Test Agency 1'];
        //System.assertEquals(1, agencies.size(), 'Should have one test agency');
        
        List<Opportunity> opportunities = [SELECT Id, Name FROM Opportunity WHERE Name = 'Test Container'];
        //System.assertEquals(1, opportunities.size(), 'Should have one test opportunity');
        
        List<Quote> quotes = [SELECT Id, Name FROM Quote WHERE Name = 'Test Quote'];
        //System.assertEquals(1, quotes.size(), 'Should have one test quote');
    }
    
    @IsTest
    static void testRequestActivityCreation() {
        // Test creating RequestActivity objects
        RequestActivity activity = new RequestActivity();
        activity.areaOfNeed = 'Auto';
        activity.stageName = 'Interest';
        activity.timeSlot = 'Morning';
        activity.notes = 'Test notes';
        activity.codDomainActivity = 'ACT001';
        activity.numeroRicontatto = '**********';
        
        /*System.assertEquals('Auto', activity.areaOfNeed, 'Area of need should be set');
        System.assertEquals('Interest', activity.stageName, 'Stage name should be set');
        System.assertEquals('Morning', activity.timeSlot, 'Time slot should be set');
        System.assertEquals('Test notes', activity.notes, 'Notes should be set');
        System.assertEquals('ACT001', activity.codDomainActivity, 'Activity code should be set');
        System.assertEquals('**********', activity.numeroRicontatto, 'Phone should be set');*/
    }
}