@IsTest
public class Test_DecisionMatrixService {
    
    @TestSetup
    static void setupTestData() {
        // Create test accounts with different revenue and employee counts
        List<Account> testAccounts = new List<Account>();
        
        Id perAccRecTypId = Schema.SObjectType.Account.getRecordTypeInfosByDeveloperName().get(System.Label.PersonAccRecType).getRecordTypeId();
        
        // Create test accounts with different characteristics                
        Account largeAccount = new Account(
            FirstName = 'Large',
            LastName = 'Enterprise',
            Email__c = '<EMAIL>',
            RecordTypeId = perAccRecTypId,
            PersonBirthdate = Date.today().addYears(-30),
            AnnualRevenue = 2000000,
            NumberOfEmployees = 100
        );
        testAccounts.add(largeAccount);
        
        Account smallAccount = new Account(
            FirstName = 'Small',
            LastName = 'Business',
            Email__c = '<EMAIL>',
            RecordTypeId = perAccRecTypId,
            PersonBirthdate = Date.today().addYears(-30),            
            AnnualRevenue = 500000,
            NumberOfEmployees = 25
        );
        testAccounts.add(smallAccount);
        
        Account nullRevenueAccount = new Account(
            FirstName = 'Unknown',
            LastName = 'Size',
            Email__c = '<EMAIL>',
            RecordTypeId = perAccRecTypId,
            PersonBirthdate = Date.today().addYears(-30)            
        );
        testAccounts.add(nullRevenueAccount);
        
        insert testAccounts;
        
        // Create test opportunities
        List<Opportunity> testOpportunities = new List<Opportunity>();
        
        testOpportunities.add(new Opportunity(
            Name = 'Large Opp',
            AccountId = testAccounts[0].Id,
            StageName = 'Nuovo',
            CloseDate = Date.today().addDays(30)
        ));
        
        testOpportunities.add(new Opportunity(
            Name = 'Small Opp',
            AccountId = testAccounts[1].Id,
            StageName = 'Nuovo', 
            CloseDate = Date.today().addDays(30)
        ));
        
        testOpportunities.add(new Opportunity(
            Name = 'No Revenue Opp',
            AccountId = testAccounts[2].Id,
            StageName = 'Nuovo',
            CloseDate = Date.today().addDays(30)
        ));
        
        insert testOpportunities;
    }
    
    @IsTest
    static void testParametriMotore_DefaultValues() {
        List<Opportunity> opps = [SELECT Id, AccountId, Account.Id, Account.RecordTypeFormula__c FROM Opportunity LIMIT 1];
        Map<Id, Opportunity> oppMap = new Map<Id, Opportunity>(opps);
        
        DecisionMatrixService.LookupTableInput input = createLookupInput(
            'Parametri Motore',
            opps[0].Id,
            'Tutti',
            'Standard Channel',
            false,
            'Calda',
            'Prospect',
            'Canale Fisico',
            'quote1'
        );
        
        Test.startTest();
        Map<String, DecisionMatrixService.LookupResult> results = 
            DecisionMatrixService.getLookupTableOutputData(new List<DecisionMatrixService.LookupTableInput>{input}, oppMap);
        Test.stopTest();
        
        System.assertEquals(1, results.size(), 'Should return one result');
        DecisionMatrixService.LookupResult result = results.get('quote1');
        System.assertNotEquals(null, result, 'Result should not be null');
        System.assertEquals(5, result.MaxRiassegnazioni, 'Default MaxRiassegnazioni should be 5');
        System.assertEquals(10, result.RaggioCentroideKM, 'Default RaggioCentroideKM should be 10');
        System.assertEquals(50, result.RaggioEstensioneKM, 'Default RaggioEstensioneKM should be 50');
        System.assertEquals(true, result.Discrezionalita, 'Default Discrezionalita should be true');
        System.assertEquals('80%', result.TassoContattabilita, 'Default TassoContattabilita should be 80%');
        System.assertEquals(false, result.flagNullOutput, 'flagNullOutput should be false');
    }
    
    @IsTest
    static void testParametriMotore_UnicaChannel() {
        List<Opportunity> opps = [SELECT Id, AccountId, Account.Id, Account.RecordTypeFormula__c FROM Opportunity LIMIT 1];
        Map<Id, Opportunity> oppMap = new Map<Id, Opportunity>(opps);
        
        DecisionMatrixService.LookupTableInput input = createLookupInput(
            'Parametri Motore',
            opps[0].Id,
            'Tutti',
            'Preventivatore digitale Unica',
            false,
            'Calda',
            'Prospect',
            'Canale Fisico',
            'quote2'
        );
        
        Test.startTest();
        Map<String, DecisionMatrixService.LookupResult> results = 
            DecisionMatrixService.getLookupTableOutputData(new List<DecisionMatrixService.LookupTableInput>{input}, oppMap);
        Test.stopTest();
        
        DecisionMatrixService.LookupResult result = results.get('quote2');
        System.assertEquals(10, result.MaxRiassegnazioni, 'Unica channel should have MaxRiassegnazioni = 10');
    }
    
    @IsTest
    static void testSLATempiLavorazione_CaldissimaTemperatura() {
        List<Opportunity> opps = [SELECT Id, AccountId, Account.Id, Account.RecordTypeFormula__c FROM Opportunity LIMIT 1];
        Map<Id, Opportunity> oppMap = new Map<Id, Opportunity>(opps);
        
        DecisionMatrixService.LookupTableInput input = createLookupInput(
            'SLA - Tempi di lavorazione',
            opps[0].Id,
            'Tutti',
            'Standard',
            false,
            'Caldissima',
            'Prospect',
            'Canale Fisico',
            'quote3'
        );
        
        Test.startTest();
        Map<String, DecisionMatrixService.LookupResult> results = 
            DecisionMatrixService.getLookupTableOutputData(new List<DecisionMatrixService.LookupTableInput>{input}, oppMap);
        Test.stopTest();
        
        DecisionMatrixService.LookupResult result = results.get('quote3');
        System.assertEquals(1, result.GiorniSLAPresaInCarico, 'Caldissima should have 1 day for PresaInCarico');
        System.assertEquals(2, result.GiorniSLATrattativa, 'Caldissima should have 2 days for Trattativa');
        System.assertEquals(false, result.flagNullOutput, 'flagNullOutput should be false');
    }
    
    @IsTest
    static void testSLATempiLavorazione_PrevidenzaIntegrativa() {
        List<Opportunity> opps = [SELECT Id, AccountId, Account.Id, Account.RecordTypeFormula__c FROM Opportunity LIMIT 1];
        Map<Id, Opportunity> oppMap = new Map<Id, Opportunity>(opps);
        
        DecisionMatrixService.LookupTableInput input = createLookupInput(
            'SLA - Tempi di lavorazione',
            opps[0].Id,
            'Previdenza Integrativa',
            'Standard',
            false,
            'Calda',
            'Prospect',
            'Canale Fisico',
            'quote4'
        );
        
        Test.startTest();
        Map<String, DecisionMatrixService.LookupResult> results = 
            DecisionMatrixService.getLookupTableOutputData(new List<DecisionMatrixService.LookupTableInput>{input}, oppMap);
        Test.stopTest();
        
        DecisionMatrixService.LookupResult result = results.get('quote4');
        System.assertEquals(3, result.GiorniSLAPresaInCarico, 'Previdenza should have 3 days for PresaInCarico');
        System.assertEquals(5, result.GiorniSLATrattativa, 'Previdenza should have 5 days for Trattativa');
        System.assertEquals(1, result.GiorniSLAPresaInCaricoCallMeBack, 'Previdenza should have 1 day for CallMeBack PresaInCarico');
        System.assertEquals(2, result.GiorniSLATrattativaCallMeBack, 'Previdenza should have 2 days for CallMeBack Trattativa');
    }
    
    @IsTest
    static void testSLATempiLavorazione_PromozioneBPER() {
        List<Opportunity> opps = [SELECT Id, AccountId, Account.Id, Account.RecordTypeFormula__c FROM Opportunity LIMIT 1];
        Map<Id, Opportunity> oppMap = new Map<Id, Opportunity>(opps);
        
        DecisionMatrixService.LookupTableInput input = createLookupInput(
            'SLA - Tempi di lavorazione',
            opps[0].Id,
            'Altro',
            'Promozione BPER',
            false,
            'Calda',
            'Prospect',
            'Canale Fisico',
            'quote5'
        );
        
        Test.startTest();
        Map<String, DecisionMatrixService.LookupResult> results = 
            DecisionMatrixService.getLookupTableOutputData(new List<DecisionMatrixService.LookupTableInput>{input}, oppMap);
        Test.stopTest();
        
        DecisionMatrixService.LookupResult result = results.get('quote5');
        System.assertEquals(7, result.GiorniSLAPresaInCarico, 'BPER should have 7 days for PresaInCarico');
        System.assertEquals(10, result.GiorniSLATrattativa, 'BPER should have 10 days for Trattativa');
    }
    
    @IsTest
    static void testSLATempiLavorazione_DefaultValues() {
        List<Opportunity> opps = [SELECT Id, AccountId, Account.Id, Account.RecordTypeFormula__c FROM Opportunity LIMIT 1];
        Map<Id, Opportunity> oppMap = new Map<Id, Opportunity>(opps);
        
        DecisionMatrixService.LookupTableInput input = createLookupInput(
            'Tempi di Lavorazione - Altro',
            opps[0].Id,
            'Altri Ambiti',
            'Other Channel',
            false,
            'Media',
            'Prospect',
            'Canale Fisico',
            'quote6'
        );
        
        Test.startTest();
        Map<String, DecisionMatrixService.LookupResult> results = 
            DecisionMatrixService.getLookupTableOutputData(new List<DecisionMatrixService.LookupTableInput>{input}, oppMap);
        Test.stopTest();
        
        DecisionMatrixService.LookupResult result = results.get('quote6');
        System.assertEquals(2, result.GiorniSLAPresaInCarico, 'Default should have 2 days for PresaInCarico');
        System.assertEquals(60, result.GiorniSLATrattativa, 'Default should have 60 days for Trattativa');
        System.assertEquals(1, result.GiorniSLAPresaInCaricoCallMeBack, 'Default should have 1 day for CallMeBack PresaInCarico');
        System.assertEquals(30, result.GiorniSLATrattativaCallMeBack, 'Default should have 30 days for CallMeBack Trattativa');
    }
    
    @IsTest
    static void testSLAGestioneAssegnazione_CaldissimaTemperatura() {
        List<Opportunity> opps = [SELECT Id, AccountId, Account.Id, Account.RecordTypeFormula__c FROM Opportunity LIMIT 1];
        Map<Id, Opportunity> oppMap = new Map<Id, Opportunity>(opps);
        
        DecisionMatrixService.LookupTableInput input = createLookupInput(
            'SLA - Gestione assegnazione',
            opps[0].Id,
            'Tutti',
            'Standard',
            false,
            'Caldissima',
            'Prospect',
            'Canale Fisico',
            'quote7'
        );
        
        Test.startTest();
        Map<String, DecisionMatrixService.LookupResult> results = 
            DecisionMatrixService.getLookupTableOutputData(new List<DecisionMatrixService.LookupTableInput>{input}, oppMap);
        Test.stopTest();
        
        DecisionMatrixService.LookupResult result = results.get('quote7');
        System.assertEquals(true, result.Assegnazione, 'Caldissima should have Assegnazione = true');
        System.assertEquals('Agenzia', result.CanaleAssegnazione, 'Caldissima should use Agenzia');
        System.assertEquals(false, result.Riassegnazione, 'Caldissima should have Riassegnazione = false');
        System.assertEquals('Contact Center', result.CanaleRiassegnazione, 'Should use Contact Center for riassegnazione');
    }
    
    @IsTest
    static void testSLAGestioneAssegnazione_PreventivatorePrevidenza() {
        List<Opportunity> opps = [SELECT Id, AccountId, Account.Id, Account.RecordTypeFormula__c FROM Opportunity LIMIT 1];
        Map<Id, Opportunity> oppMap = new Map<Id, Opportunity>(opps);
        
        DecisionMatrixService.LookupTableInput input = createLookupInput(
            'SLA - Gestione assegnazione',
            opps[0].Id,
            'Previdenza',
            'Preventivatore Previdenza',
            false,
            'Media',
            'Prospect',
            'Canale Fisico',
            'quote8'
        );
        
        Test.startTest();
        Map<String, DecisionMatrixService.LookupResult> results = 
            DecisionMatrixService.getLookupTableOutputData(new List<DecisionMatrixService.LookupTableInput>{input}, oppMap);
        Test.stopTest();
        
        DecisionMatrixService.LookupResult result = results.get('quote8');
        System.assertEquals(true, result.Assegnazione, 'Preventivatore Previdenza should have Assegnazione = true');
        System.assertEquals('Agenzia', result.CanaleAssegnazione, 'Should use Agenzia');
        System.assertEquals(false, result.Riassegnazione, 'Should have Riassegnazione = false');
        System.assertEquals('Contact Center', result.CanaleRiassegnazione, 'Should use Contact Center');
    }
    
    @IsTest
    static void testSLAGestioneAssegnazione_PromozioneBPER_LargeAccount() {
        List<Opportunity> opps = [SELECT Id, AccountId, Account.Id, Account.RecordTypeFormula__c FROM Opportunity LIMIT 1];
        Map<Id, Opportunity> oppMap = new Map<Id, Opportunity>(opps);
        
        DecisionMatrixService.LookupTableInput input = createLookupInput(
            'SLA - Gestione assegnazione',
            opps[0].Id,
            'Commerciale',
            'Promozione BPER',
            false,
            'Media',
            'Prospect',
            'Canale Fisico',
            'quote9'
        );
        
        Test.startTest();
        Map<String, DecisionMatrixService.LookupResult> results = 
            DecisionMatrixService.getLookupTableOutputData(new List<DecisionMatrixService.LookupTableInput>{input}, oppMap);
        Test.stopTest();
        
        DecisionMatrixService.LookupResult result = results.get('quote9');
        /*System.assertEquals(true, result.Assegnazione, 'Large BPER account should have Assegnazione = true');
        System.assertEquals('Branch BPER', result.CanaleAssegnazione, 'Large account should use Branch BPER');
        System.assertEquals(false, result.Riassegnazione, 'Should have Riassegnazione = false');
        System.assertEquals('Contact Center', result.CanaleRiassegnazione, 'Should use Contact Center');*/
    }
    
    @IsTest
    static void testSLAGestioneAssegnazione_PromozioneBPER_SmallAccount() {
        List<Opportunity> opps = [SELECT Id, AccountId, Account.Id, Account.RecordTypeFormula__c FROM Opportunity LIMIT 1];
        Map<Id, Opportunity> oppMap = new Map<Id, Opportunity>(opps);
        
        DecisionMatrixService.LookupTableInput input = createLookupInput(
            'SLA - Gestione assegnazione',
            opps[0].Id,
            'Commerciale',
            'Promozione BPER',
            false,
            'Media',
            'Prospect',
            'Canale Fisico',
            'quote10'
        );
        
        Test.startTest();
        Map<String, DecisionMatrixService.LookupResult> results = 
            DecisionMatrixService.getLookupTableOutputData(new List<DecisionMatrixService.LookupTableInput>{input}, oppMap);
        Test.stopTest();
        
        DecisionMatrixService.LookupResult result = results.get('quote10');
        /*System.assertEquals(false, result.Assegnazione, 'Small BPER account should have Assegnazione = false');
        System.assertEquals('Contact Center', result.CanaleAssegnazione, 'Small account should use Contact Center');
        System.assertEquals(false, result.Riassegnazione, 'Should have Riassegnazione = false');
        System.assertEquals('Contact Center', result.CanaleRiassegnazione, 'Should use Contact Center');*/
    }
    
    @IsTest
    static void testSLAGestioneAssegnazione_DefaultUnica() {
        List<Opportunity> opps = [SELECT Id, AccountId, Account.Id, Account.RecordTypeFormula__c FROM Opportunity LIMIT 1];
        Map<Id, Opportunity> oppMap = new Map<Id, Opportunity>(opps);
        
        DecisionMatrixService.LookupTableInput input = createLookupInput(
            'SLA - Gestione assegnazione',
            opps[0].Id,
            'Altro',
            'Other Channel',
            false,
            'Media',
            'Prospect',
            'Canale Fisico',
            'quote11'
        );
        
        Test.startTest();
        Map<String, DecisionMatrixService.LookupResult> results = 
            DecisionMatrixService.getLookupTableOutputData(new List<DecisionMatrixService.LookupTableInput>{input}, oppMap);
        Test.stopTest();
        
        DecisionMatrixService.LookupResult result = results.get('quote11');
        System.assertEquals(true, result.Assegnazione, 'Default should have Assegnazione = true');
        System.assertEquals('Agenzia', result.CanaleAssegnazione, 'Default should use Agenzia');
        System.assertEquals(false, result.Riassegnazione, 'Default should have Riassegnazione = false');
        System.assertEquals('Contact Center', result.CanaleRiassegnazione, 'Default should use Contact Center');
    }
    
    @IsTest
    static void testFlagNullOutput_SLATempi() {
        List<Opportunity> opps = [SELECT Id, AccountId, Account.Id, Account.RecordTypeFormula__c FROM Opportunity LIMIT 1];
        Map<Id, Opportunity> oppMap = new Map<Id, Opportunity>(opps);
        
        // Create a mock input that should result in null SLA values
        DecisionMatrixService.LookupTableInput input = createLookupInput(
            'SLA - Tempi di lavorazione',
            opps[0].Id,
            'Test Ambito',
            'Test Channel',
            false,
            'Test Temperature',
            'Prospect',
            'Canale Fisico',
            'quote12'
        );
        
        Test.startTest();
        Map<String, DecisionMatrixService.LookupResult> results = 
            DecisionMatrixService.getLookupTableOutputData(new List<DecisionMatrixService.LookupTableInput>{input}, oppMap);
        Test.stopTest();
        
        DecisionMatrixService.LookupResult result = results.get('quote12');
        // Since none of the conditions match, it should use default values, not null
        System.assertEquals(2, result.GiorniSLAPresaInCarico, 'Should use default value');
        System.assertEquals(60, result.GiorniSLATrattativa, 'Should use default value');
        System.assertEquals(false, result.flagNullOutput, 'Should not be null since default values are set');
    }
    
    @IsTest
    static void testBulkProcessing() {
        List<Opportunity> opps = [SELECT Id, AccountId, Account.Id, Account.RecordTypeFormula__c FROM Opportunity];
        Map<Id, Opportunity> oppMap = new Map<Id, Opportunity>(opps);
        
        List<DecisionMatrixService.LookupTableInput> inputs = new List<DecisionMatrixService.LookupTableInput>();
        
        // Create multiple inputs with different decision matrix types
        for (Integer i = 0; i < opps.size(); i++) {
            inputs.add(createLookupInput(
                'Parametri Motore',
                opps[i].Id,
                'Tutti',
                'Standard',
                false,
                'Calda',
                'Prospect',
                'Canale Fisico',
                'quote_bulk_' + i
            ));
        }
        
        Test.startTest();
        Map<String, DecisionMatrixService.LookupResult> results = 
            DecisionMatrixService.getLookupTableOutputData(inputs, oppMap);
        Test.stopTest();
        
        System.assertEquals(opps.size(), results.size(), 'Should return results for all inputs');
        
        for (String quoteId : results.keySet()) {
            DecisionMatrixService.LookupResult result = results.get(quoteId);
            System.assertNotEquals(null, result, 'Each result should not be null');
            System.assertEquals(5, result.MaxRiassegnazioni, 'All should have default MaxRiassegnazioni');
        }
    }
    
    @IsTest
    static void testEmptyInputList() {
        Map<Id, Opportunity> oppMap = new Map<Id, Opportunity>();
        
        Test.startTest();
        Map<String, DecisionMatrixService.LookupResult> results = 
            DecisionMatrixService.getLookupTableOutputData(new List<DecisionMatrixService.LookupTableInput>(), oppMap);
        Test.stopTest();
        
        System.assertEquals(0, results.size(), 'Empty input should return empty results');
    }
    
    @IsTest
    static void testNullOpportunityHandling() {
        DecisionMatrixService.LookupTableInput input = createLookupInput(
            'Parametri Motore',
            null, // null opportunity ID
            'Tutti',
            'Standard',
            false,
            'Calda',
            'Prospect',
            'Canale Fisico',
            'quote_null'
        );
        
        Test.startTest();
        Map<String, DecisionMatrixService.LookupResult> results = 
            DecisionMatrixService.getLookupTableOutputData(
                new List<DecisionMatrixService.LookupTableInput>{input}, 
                new Map<Id, Opportunity>()
            );
        Test.stopTest();
        
        System.assertEquals(1, results.size(), 'Should handle null opportunity gracefully');
        DecisionMatrixService.LookupResult result = results.get('quote_null');
        System.assertNotEquals(null, result, 'Result should still be created');
        System.assertEquals('quote_null', result.quoteRecordId, 'Should preserve quoteRecordId');
    }
    
    // Helper method to create LookupTableInput
    private static DecisionMatrixService.LookupTableInput createLookupInput(
        String decisionMatrixName,
        Id opportunityId,
        String ambito,
        String canale,
        Boolean isMultiambito,
        String temperatura,
        String tipoSoggetto,
        String tipoTrattativa,
        String quoteRecordId
    ) {
        DecisionMatrixService.LookupTableInput input = new DecisionMatrixService.LookupTableInput();
        input.decisionMatrixName = decisionMatrixName;
        input.opportunityId = opportunityId;
        input.ambito = ambito;
        input.canale = canale;
        input.isMultiambito = isMultiambito;
        input.temperatura = temperatura;
        input.tipoSoggetto = tipoSoggetto;
        input.tipoTrattativa = tipoTrattativa;
        input.quoteRecordId = quoteRecordId;
        
        // Set opportunityInput if opportunityId is provided
        if (opportunityId != null) {
            List<Opportunity> opps = [SELECT Id, AccountId, Account.Id, Account.RecordTypeFormula__c FROM Opportunity WHERE Id = :opportunityId LIMIT 1];
            if (!opps.isEmpty()) {
                input.opportunityInput = opps[0];
            }
        }
        
        return input;
    }
}