@isTest
public class Test_FlowFormulaHelper {
    
    @isTest
    static void testHasMultipleNeeds() {
        // Test with null
        System.assertEquals(false, FlowFormulaHelper.hasMultipleNeeds(null));
        
        // Test with single need
        System.assertEquals(false, FlowFormulaHelper.hasMultipleNeeds('Insurance'));
        
        // Test with multiple needs
        System.assertEquals(true, FlowFormulaHelper.hasMultipleNeeds('Insurance;Pension'));
        
        // Test with empty string
        System.assertEquals(false, FlowFormulaHelper.hasMultipleNeeds(''));
    }
    
    @isTest
    static void testGetQuoteRating() {
        // Test with callback - should always return 'Caldissima'
        System.assertEquals('Caldissima', FlowFormulaHelper.getQuoteRating('Show price', true));
        
        // Test without callback - various statuses
        System.assertEquals('Tiepida', FlowFormulaHelper.getQuoteRating('Show price', false));
        System.assertEquals('Calda', FlowFormulaHelper.getQuoteRating('Salvataggio preventivo', false));
        System.assertEquals('Caldissima', FlowFormulaHelper.getQuoteRating('Acquisto non concluso', false));
        System.assertEquals('Tiepida', FlowFormulaHelper.getQuoteRating('Prospetto previdenziale', false));
        System.assertEquals('Calda', FlowFormulaHelper.getQuoteRating('Scelta del prodotto', false));
        System.assertEquals('Caldissima', FlowFormulaHelper.getQuoteRating('Offerta previdenziale', false));
        System.assertEquals('Caldissima', FlowFormulaHelper.getQuoteRating('Download riepilogo', false));
        
        // Test with unknown status
        System.assertEquals('', FlowFormulaHelper.getQuoteRating('Unknown Status', false));
        System.assertEquals('', FlowFormulaHelper.getQuoteRating(null, false));
    }
    
    @isTest
    static void testGetProductRatingFormula() {
        // Test with callback
        System.assertEquals('Caldissima', FlowFormulaHelper.getProductRatingFormula(true, 'Show price'));
        
        // Test without callback
        System.assertEquals('Tiepida', FlowFormulaHelper.getProductRatingFormula(false, 'Show price'));
        System.assertEquals('Calda', FlowFormulaHelper.getProductRatingFormula(false, 'Salvataggio preventivo'));
    }
    
    @isTest
    static void testRatingPriorityFormula() {
        // Test product rating higher than container
        System.assertEquals(true, FlowFormulaHelper.ratingPriorityFormula('Caldissima', 'Calda'));
        System.assertEquals(true, FlowFormulaHelper.ratingPriorityFormula('Calda', 'Tiepida'));
        System.assertEquals(true, FlowFormulaHelper.ratingPriorityFormula('Tiepida', 'Fredda'));
        
        // Test product rating lower than container
        System.assertEquals(false, FlowFormulaHelper.ratingPriorityFormula('Calda', 'Caldissima'));
        System.assertEquals(false, FlowFormulaHelper.ratingPriorityFormula('Tiepida', 'Calda'));
        System.assertEquals(false, FlowFormulaHelper.ratingPriorityFormula('Fredda', 'Tiepida'));
        
        // Test equal ratings
        System.assertEquals(false, FlowFormulaHelper.ratingPriorityFormula('Calda', 'Calda'));
        
        // Test with null/empty values
        System.assertEquals(false, FlowFormulaHelper.ratingPriorityFormula('', 'Calda'));
        System.assertEquals(true, FlowFormulaHelper.ratingPriorityFormula('Calda', ''));
    }
    
    @isTest
    static void testGetQuoteRatingForGenericOpt() {
        System.assertEquals('Caldissima', FlowFormulaHelper.getQuoteRatingForGenericOpt('Show price', true));
        System.assertEquals('Tiepida', FlowFormulaHelper.getQuoteRatingForGenericOpt('Show price', false));
    }
    
    @isTest
    static void testGetDefaultCloseDate() {
        Date testDate = Date.today();
        Date expectedDate = testDate.addDays(60);
        System.assertEquals(expectedDate, FlowFormulaHelper.getDefaultCloseDate(testDate));
    }
    
    @isTest
    static void testIsEventTypeCallMeBack() {
        System.assertEquals(true, FlowFormulaHelper.isEventTypeCallMeBack('RICHIESTA_CONTATTO'));
        System.assertEquals(false, FlowFormulaHelper.isEventTypeCallMeBack('OTHER_EVENT'));
        System.assertEquals(false, FlowFormulaHelper.isEventTypeCallMeBack(null));
    }
    
    @isTest
    static void testGetEventTypeToStatus() {
        System.assertEquals('Salvataggio preventivo', FlowFormulaHelper.getEventTypeToStatus('SALVA_PREVENTIVO'));
        System.assertEquals('Acquisto non concluso', FlowFormulaHelper.getEventTypeToStatus('POSIZIONE_ABBANDONATA_DA_CARRELLO'));
        System.assertEquals('Acquisto non concluso', FlowFormulaHelper.getEventTypeToStatus('MANCATO_ACQUISTO_CARRELLO'));
        System.assertEquals('Acquisto non concluso', FlowFormulaHelper.getEventTypeToStatus('PAGAMENTO_KO'));
        System.assertEquals('', FlowFormulaHelper.getEventTypeToStatus('UNKNOWN_EVENT'));
        System.assertEquals('', FlowFormulaHelper.getEventTypeToStatus(null));
    }
    
    @isTest
    static void testGetCaseSubjectFormula() {
        System.assertEquals('Autore:John Doe', FlowFormulaHelper.getCaseSubjectFormula('John Doe'));
        System.assertEquals('Autore:', FlowFormulaHelper.getCaseSubjectFormula(''));
        System.assertEquals('Autore:null', FlowFormulaHelper.getCaseSubjectFormula(null));
    }
    
    @isTest
    static void testGetSubjectFormula() {
        System.assertEquals('Soggetto:John Doe', FlowFormulaHelper.getSubjectFormula('John Doe'));
        System.assertEquals('Soggetto:', FlowFormulaHelper.getSubjectFormula(''));
        System.assertEquals('Soggetto:null', FlowFormulaHelper.getSubjectFormula(null));
    }
    
    @isTest
    static void testGetFirstStatusPriority() {
        System.assertEquals(1, FlowFormulaHelper.getFirstStatusPriority('Salvataggio preventivo'));
        System.assertEquals(2, FlowFormulaHelper.getFirstStatusPriority('Acquisto non concluso'));
        System.assertEquals(0, FlowFormulaHelper.getFirstStatusPriority('Other Status'));
        System.assertEquals(0, FlowFormulaHelper.getFirstStatusPriority(null));
    }
    
    @isTest
    static void testGetOtherStatusPriority() {
        System.assertEquals(1, FlowFormulaHelper.getOtherStatusPriority('Salvataggio preventivo'));
        System.assertEquals(2, FlowFormulaHelper.getOtherStatusPriority('Acquisto non concluso'));
        System.assertEquals(0, FlowFormulaHelper.getOtherStatusPriority('Other Status'));
        System.assertEquals(0, FlowFormulaHelper.getOtherStatusPriority(null));
    }
    
    @isTest
    static void testGetClientTypeFormula() {
        System.assertEquals('Cliente', FlowFormulaHelper.getClientTypeFormula('Cliente'));
        System.assertEquals('Prospect Puro', FlowFormulaHelper.getClientTypeFormula(''));
        System.assertEquals('Prospect Puro', FlowFormulaHelper.getClientTypeFormula(null));
        System.assertEquals('VIP', FlowFormulaHelper.getClientTypeFormula('VIP'));
    }
    
    @isTest
    static void testGetAmbitoLookupTableFormula() {
        // Test with multiple areas
        System.assertEquals('Tutti', FlowFormulaHelper.getAmbitoLookupTableFormula(true, 'Insurance'));
        
        // Test with blank area
        System.assertEquals('Vuoto', FlowFormulaHelper.getAmbitoLookupTableFormula(false, ''));
        System.assertEquals('Vuoto', FlowFormulaHelper.getAmbitoLookupTableFormula(false, null));
        
        // Test with single area
        System.assertEquals('Insurance', FlowFormulaHelper.getAmbitoLookupTableFormula(false, 'Insurance'));
    }
    
    @isTest
    static void testGetSLALookupTable() {
        // Test without parent opportunity
        System.assertEquals('SLA - Tempi di lavorazione', FlowFormulaHelper.getSLALookupTable(null, 'Online', null));
        System.assertEquals('Tempi di Lavorazione - Altro', FlowFormulaHelper.getSLALookupTable(null, 'Agenzia', null));
        
        Id agAccRecTypId = Schema.SObjectType.Account.getRecordTypeInfosByDeveloperName().get(System.Label.AgencyAccRecType).getRecordTypeId();
        Id perAccRecTypId = Schema.SObjectType.Account.getRecordTypeInfosByDeveloperName().get(System.Label.PersonAccRecType).getRecordTypeId();
        
        // Create test accounts
        Account testAccount = new Account(
            FirstName = 'Test',
            LastName = 'Customer',
            Email__c = '<EMAIL>',
            RecordTypeId = perAccRecTypId,
            PersonBirthdate = Date.today().addYears(-30)
        );
        insert testAccount;
        
        Account testAgency = new Account(
            Name = 'Test Agency',
            ExternalId__c = 'EXT001',
            RecordTypeId = agAccRecTypId
        );
        insert testAgency;

        Id contRecTypId = Schema.SObjectType.Opportunity.getRecordTypeInfosByDeveloperName().get('Omnicanale').getRecordTypeId();
        Id prodRecTypId = Schema.SObjectType.Opportunity.getRecordTypeInfosByDeveloperName().get('Prodotto').getRecordTypeId();

        // Create opportunities
        Opportunity containerOpp = new Opportunity(
            Name = 'Container Opportunity',
            AccountId = testAccount.Id,
            StageName = 'Nuovo',
            CloseDate = Date.today().addDays(30),
            Amount = 1000,
            JourneyStep__c = 'Salvataggio Preventivo',
            Agency__c = testAgency.Id,
			recordtypeid = contRecTypId,            
            Channel__c = 'Omnicanale'
        );
        insert containerOpp;
        
        // Test with parent opportunity
        Id testOpportunityId = containerOpp.Id;
        System.assertEquals('SLA - Tempi di lavorazione', FlowFormulaHelper.getSLALookupTable(testOpportunityId, 'Online', 'Online'));
        System.assertEquals('Tempi di Lavorazione - Altro', FlowFormulaHelper.getSLALookupTable(testOpportunityId, 'Online', 'Agenzia'));
    }
    
    @isTest
    static void testGetTemperaturaLookupTableFormula() {
        Id agAccRecTypId = Schema.SObjectType.Account.getRecordTypeInfosByDeveloperName().get(System.Label.AgencyAccRecType).getRecordTypeId();
        Id perAccRecTypId = Schema.SObjectType.Account.getRecordTypeInfosByDeveloperName().get(System.Label.PersonAccRecType).getRecordTypeId();
        
        // Create test accounts
        Account testAccount = new Account(
            FirstName = 'Test2',
            LastName = 'Customer2',
            Email__c = '<EMAIL>',
            RecordTypeId = perAccRecTypId,
            PersonBirthdate = Date.today().addYears(-30)
        );
        insert testAccount;
        
        Account testAgency = new Account(
            Name = 'Test Agency2',
            ExternalId__c = 'EXT003',
            RecordTypeId = agAccRecTypId
        );
        insert testAgency;

        Id contRecTypId = Schema.SObjectType.Opportunity.getRecordTypeInfosByDeveloperName().get('Omnicanale').getRecordTypeId();
        Id prodRecTypId = Schema.SObjectType.Opportunity.getRecordTypeInfosByDeveloperName().get('Prodotto').getRecordTypeId();

        // Create opportunities
        Opportunity containerOpp = new Opportunity(
            Name = 'Container Opportunity2',
            AccountId = testAccount.Id,
            StageName = 'Nuovo',
            CloseDate = Date.today().addDays(30),
            Amount = 1000,
            JourneyStep__c = 'Salvataggio Preventivo',
            Agency__c = testAgency.Id,
			recordtypeid = contRecTypId,            
            Channel__c = 'Omnicanale'
        );
        insert containerOpp;
        
        // Test with parent opportunity
        Id testOpportunityId = containerOpp.Id;
        
        // Test without parent opportunity
        System.assertEquals('Tutti', FlowFormulaHelper.getTemperaturaLookupTableFormula(true, 'Calda', 'Tiepida', null));
        System.assertEquals('Calda', FlowFormulaHelper.getTemperaturaLookupTableFormula(false, 'Calda', 'Tiepida', null));
        
        // Test with parent opportunity
        System.assertEquals('Tutti', FlowFormulaHelper.getTemperaturaLookupTableFormula(true, 'Calda', 'Tiepida', testOpportunityId));
        System.assertEquals('Tiepida', FlowFormulaHelper.getTemperaturaLookupTableFormula(false, 'Calda', 'Tiepida', testOpportunityId));
    }
    
    @isTest
    static void testGetTipoSoggettoFormula() {
        System.assertEquals('Prospect Puro', FlowFormulaHelper.getTipoSoggettoFormula('PureProspect'));
        System.assertEquals('Cliente', FlowFormulaHelper.getTipoSoggettoFormula('Client'));
        System.assertEquals('Other', FlowFormulaHelper.getTipoSoggettoFormula('Other'));
        System.assertEquals(null, FlowFormulaHelper.getTipoSoggettoFormula(null));
    }
    
    @isTest
    static void testGetSLAStartEndOfDay() {
        DateTime result = FlowFormulaHelper.getSLAStartEndOfDay();
        Date today = Date.today();
        DateTime expected = DateTime.newInstance(today.year(), today.month(), today.day(), 23, 59, 0);
        System.assertEquals(expected, result);
    }
    
    @isTest
    static void testGetOffset() {
        System.assertEquals(5.0/24.0 + 59.0/(24.0*60.0), FlowFormulaHelper.getOffset('TakenInCharge'));
        System.assertEquals(0.0, FlowFormulaHelper.getOffset('Other'));
        System.assertEquals(0.0, FlowFormulaHelper.getOffset(null));
    }
    
    @isTest
    static void testAddOffsetToDateTime() {
        DateTime testDateTime = DateTime.now();
        Decimal offsetHours = 0.25; // 6 hours (0.25 * 24)
        DateTime result = FlowFormulaHelper.addOffsetToDateTime(testDateTime, offsetHours);
        DateTime expected = testDateTime.addHours(6);
        System.assertEquals(expected, result);
    }
    
    @isTest
    static void testGetContainerNeedFormula() {
        // Test with blank starting needs
        System.assertEquals('Insurance', FlowFormulaHelper.getContainerNeedFormula('', 'Insurance'));
        System.assertEquals('Insurance', FlowFormulaHelper.getContainerNeedFormula(null, 'Insurance'));
        
        // Test with existing needs
        System.assertEquals('Pension;Insurance', FlowFormulaHelper.getContainerNeedFormula('Pension', 'Insurance'));
    }
    
    @isTest
    static void testGetSingleAreaOfNeedFormula() {
        System.assertEquals('Insurance', FlowFormulaHelper.getSingleAreaOfNeedFormula('Insurance'));
        System.assertEquals('InsurancePension', FlowFormulaHelper.getSingleAreaOfNeedFormula('Insurance;Pension'));
        System.assertEquals('', FlowFormulaHelper.getSingleAreaOfNeedFormula(''));
        //System.assertEquals('', FlowFormulaHelper.getSingleAreaOfNeedFormula(null));
    }
    
    @isTest
    static void testGetDefaultCloseDateFromQuote() {
        Date testDate = Date.today();
        Date expectedDate = testDate.addDays(60);
        System.assertEquals(expectedDate, FlowFormulaHelper.getDefaultCloseDateFromQuote(testDate));
    }
}