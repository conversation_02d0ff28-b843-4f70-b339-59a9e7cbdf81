@isTest
public class Test_QuotePostProcessingBatch {
    
    @testSetup
    static void setupTestData() {
        // Create RecordTypes
        List<RecordType> recordTypes = new List<RecordType>();
        Id agAccRecTypId = Schema.SObjectType.Account.getRecordTypeInfosByDeveloperName().get(System.Label.AgencyAccRecType).getRecordTypeId();
        Id perAccRecTypId = Schema.SObjectType.Account.getRecordTypeInfosByDeveloperName().get(System.Label.PersonAccRecType).getRecordTypeId();
        
        // Create test accounts
        Account testAccount = new Account(
            FirstName = 'Test',
            LastName = 'Customer',
            Email__c = '<EMAIL>',
            RecordTypeId = perAccRecTypId,
            PersonBirthdate = Date.today().addYears(-30)
        );
        insert testAccount;
        
        Account testAgency = new Account(
            Name = 'Test Agency',
            ExternalId__c = 'EXT001',
            RecordTypeId = agAccRecTypId
        );
        insert testAgency;

        Id contRecTypId = Schema.SObjectType.Opportunity.getRecordTypeInfosByDeveloperName().get('Omnicanale').getRecordTypeId();
        Id prodRecTypId = Schema.SObjectType.Opportunity.getRecordTypeInfosByDeveloperName().get('Prodotto').getRecordTypeId();

        // Create opportunities
        Opportunity containerOpp = new Opportunity(
            Name = 'Container Opportunity',
            AccountId = testAccount.Id,
            StageName = 'Nuovo',
            CloseDate = Date.today().addDays(30),
            Amount = 1000,
            JourneyStep__c = 'Salvataggio Preventivo',
            Agency__c = testAgency.Id,
			recordtypeid = contRecTypId,            
            Channel__c = 'Omnicanale'
        );
        insert containerOpp;
        
        Opportunity productOpp = new Opportunity(
            Name = 'Product Opportunity',
            AccountId = testAccount.Id,
            StageName = 'Nuovo',
            CloseDate = Date.today().addDays(30),
            Amount = 500,
            Parent__c = containerOpp.Id,
            AreaOfNeed__c = 'Casa',
            Agency__c = testAgency.Id,
            HasCallMeBack__c = false,
			recordtypeid = prodRecTypId,            
            CIP__c = 'CIP001'
        );
        insert productOpp;
        
        Opportunity productOpp2 = new Opportunity(
            Name = 'Product Opportunity2',
            AccountId = testAccount.Id,
            StageName = 'Nuovo',
            CloseDate = Date.today().addDays(30),
            Amount = 500,
            Parent__c = containerOpp.Id,
            AreaOfNeed__c = 'Casa',
            Agency__c = testAgency.Id,
            HasCallMeBack__c = false,
			recordtypeid = prodRecTypId,            
            CIP__c = 'CIP002'
        );
        insert productOpp2;

        Opportunity productOpp3 = new Opportunity(
            Name = 'Product Opportunity3',
            AccountId = testAccount.Id,
            StageName = 'Nuovo',
            CloseDate = Date.today().addDays(30),
            Amount = 500,
            Parent__c = containerOpp.Id,
            AreaOfNeed__c = 'Casa',
            Agency__c = testAgency.Id,
            HasCallMeBack__c = false,
			recordtypeid = prodRecTypId,            
            CIP__c = 'CIP003'
        );
        insert productOpp3;        
        
        Id insuRecTypId = Schema.SObjectType.Quote.getRecordTypeInfosByDeveloperName().get('INSURANCE').getRecordTypeId();
        
        // Create quotes
        Quote testQuote1 = new Quote(
            Name = 'Test Quote 1',
            OpportunityId = productOpp.Id,
            QuoteAccountId = testAccount.Id,
            Status = 'Draft',
            to_work__c = true,
            ExternalId__c = 'EXT_QUOTE_001',
            DomainType__c = 'PU',
            EngagementPoint__c = 'Canale digitale',
            BillingName = 'Test Customer',
            Email = '<EMAIL>',
            Phone = '**********',
            Agency__c = testAgency.Id,
            FolderId__c = 'FOLDER001',
            CIP__c = '001',
            QuoteAmount__c = 500,
            AreasOfNeed__c = 'Casa',
            CreatedDateTPD__c = DateTime.now(),
			Recordtypeid = insuRecTypId,            
            BillingStreet = 'Test Street',
            BillingCity = 'Test City',
            BillingState = 'Test State',
            BillingPostalCode = '12345',
            BillingCountry = 'Italy'
        );
        
        Quote testQuote2 = new Quote(
            Name = 'Test Quote 2',
            OpportunityId = productOpp2.Id,
            QuoteAccountId = testAccount.Id,
            Status = 'Approved',
            to_work__c = true,
            ExternalId__c = 'EXT_QUOTE_002',
            DomainType__c = 'ESSIG_VITA_PREVIDENZA',
            EngagementPoint__c = 'Agenzia',
            BillingName = 'Test Customer 2',
            Email = '<EMAIL>',
            Phone = '**********',
            Agency__c = testAgency.Id,
            FolderId__c = 'FOLDER002',
			Recordtypeid = insuRecTypId,            
            QuoteAmount__c = 750
        );
        
        Quote testQuote3 = new Quote(
            Name = 'Test Quote 3 - No Config',
            OpportunityId = productOpp3.Id,
            QuoteAccountId = testAccount.Id,
            Status = 'Draft',
            to_work__c = true,
            ExternalId__c = 'EXT_QUOTE_003',
            DomainType__c = 'PU', // This will cause missing config
            EngagementPoint__c = 'Unknown Channel',
            BillingName = 'Test Customer 3',
			Recordtypeid = insuRecTypId,            
            Agency__c = testAgency.Id
        );
        
        List<Quote> quotes = new List<Quote>{testQuote1, testQuote2, testQuote3};
        insert quotes;
        
        // Create opportunity coverages
        List<OpportunityCoverage__c> coverages = new List<OpportunityCoverage__c>();
        for (Quote q : quotes) {
            OpportunityCoverage__c coverage = new OpportunityCoverage__c(
                Quote__c = q.Id,
                Amount__c = 100,
                AreaOfNeed__c = 'Casa',
                Asset__c = 'Via Toledo 1, Napoli',
                Description__c = 'Test Coverage',
                Email__c = '<EMAIL>',
                FirstName__c = 'Test',
                LastName__c = 'Coverage',
                BirthDate__c = Date.today().addYears(-25),
                ExternalId__c = 'COV_' + q.ExternalId__c,
                FiscalCode__c = 'TESTFC123456789',
                MobilePhone__c = '**********'
            );
            coverages.add(coverage);
        }
        insert coverages;
    }
    
    @isTest
    static void testBatchExecutionSuccess() {
        /*Test.startTest();
        
        QuotePostProcessingBatch batch = new QuotePostProcessingBatch();
        try{
            Database.executeBatch(batch, 200);
        }catch(Exception ex){}
        
        Test.stopTest();*/
        
        // Verify quotes were processed
        //List<Quote> processedQuotes = [SELECT Id, to_work__c, Status FROM Quote WHERE to_work__c = false];
        //System.assert(processedQuotes.size() > 0, 'Quotes should have been processed');
        
        // Verify debug logs were generated (basic verification)
        //System.debug('Test completed - batch should have processed quotes successfully');
    }
    
    @isTest
    static void testBatchConstructor() {
        Test.startTest();
        
        QuotePostProcessingBatch batch = new QuotePostProcessingBatch();
        
        Test.stopTest();
        
        // Verify initialization
        System.assertNotEquals(null, batch.opportunitiesToUpsertMap, 
                              'Opportunities map should be initialized');
        System.assertNotEquals(null, batch.contactHistoryEventsToCreate, 
                              'Contact history list should be initialized');
        System.assertNotEquals(null, batch.casesToCreate, 
                              'Cases list should be initialized');
        System.assertNotEquals(null, batch.recordTypeMap, 
                              'Record type map should be initialized');
    }
    
    @isTest
    static void testQueryLocatorGeneration() {
        Test.startTest();
        
        QuotePostProcessingBatch batch = new QuotePostProcessingBatch();
        Database.BatchableContext bc = null; // Mock context
        Database.QueryLocator ql = batch.start(bc);
        
        Test.stopTest();
        
        System.assertNotEquals(null, ql, 'Query locator should be generated');
        
        // Verify the query returns quotes with to_work__c = true
        List<Quote> quotesToProcess = [SELECT Id FROM Quote WHERE to_work__c = true];
        System.assert(quotesToProcess.size() > 0, 'Should have quotes ready for processing');
    }
    
    @isTest
    static void testChannelCodeDetermination() {
        // Create quotes with different engagement points and domain types
        List<Quote> testQuotes = [SELECT Id, DomainType__c, EngagementPoint__c, 
                                        Opportunity.Channel__c 
                                 FROM Quote WHERE to_work__c = true];
        
        Test.startTest();
        
        QuotePostProcessingBatch batch = new QuotePostProcessingBatch();
        
        // Test the channel code logic
        for (Quote q : testQuotes) {
            String expectedChannelCode;
            
            if (q.DomainType__c == 'ESSIG_VITA_PREVIDENZA') {
                expectedChannelCode = 'Preventivatore_Previdenza';
            } else if (q.EngagementPoint__c == 'Agenzia') {
                expectedChannelCode = 'Agenziale';
            } else if (q.EngagementPoint__c == 'Canale digitale') {
                if (q.DomainType__c == 'PU') {
                    expectedChannelCode = 'Omnicanale';
                } else if (q.DomainType__c == 'PEGA') {
                    expectedChannelCode = 'APP';
                } else {
                    expectedChannelCode = 'Omnicanale';
                }
            } else {
                expectedChannelCode = 'Omnicanale';
            }
            
            System.debug('Quote ' + q.Id + ' should have channel code: ' + expectedChannelCode);
        }
        
        Test.stopTest();
    }
    
    @isTest
    static void testBatchWithEmptyScope() {
        // Update all quotes to set to_work__c = false
        List<Quote> quotes = [SELECT Id FROM Quote];
        for (Quote q : quotes) {
            q.to_work__c = false;
        }
        update quotes;
        
        Test.startTest();
        
        QuotePostProcessingBatch batch = new QuotePostProcessingBatch();
        Database.executeBatch(batch, 200);
        
        Test.stopTest();
        
        // Should complete without errors even with no records to process
        System.debug('Batch completed with empty scope');
    }
    
    @isTest
    static void testBatchFinishMethod() {
        Test.startTest();
        
        QuotePostProcessingBatch batch = new QuotePostProcessingBatch();
        Database.BatchableContext bc = null; // Mock context
        batch.finish(bc);
        
        Test.stopTest();
        
        // Verify finish method executes without errors
        System.debug('Finish method executed successfully');
    }
    
    @isTest
    static void testEventSourceDetermination() {
        List<Quote> testQuotes = [SELECT Id, DomainType__c FROM Quote WHERE to_work__c = true];
        
        Test.startTest();
        
        for (Quote q : testQuotes) {
            String expectedEventSource;
            
            if (q.DomainType__c == 'PEGA') {
                expectedEventSource = 'Pega';
            } else {
                expectedEventSource = 'Web';
            }
            
            System.debug('Quote ' + q.Id + ' should have event source: ' + expectedEventSource);
        }
        
        Test.stopTest();
    }
    
    /*@isTest
    static void testBatchWithLargeVolume() {
        // Create additional quotes for volume testing
        List<Quote> additionalQuotes = new List<Quote>();
        Account testAccount = [SELECT Id FROM Account WHERE Name = 'Test Customer' LIMIT 1];
        Account testAgency = [SELECT Id FROM Account WHERE Name = 'Test Agency' LIMIT 1];
        Opportunity productOpp = [SELECT Id FROM Opportunity WHERE Name = 'Product Opportunity' LIMIT 1];
        
        for (Integer i = 0; i < 50; i++) {
            Quote q = new Quote(
                Name = 'Bulk Quote ' + i,
                OpportunityId = productOpp.Id,
                QuoteAccountId = testAccount.Id,
                Status = 'Draft',
                to_work__c = true,
                ExternalId__c = 'BULK_' + i,
                DomainType__c = 'PU',
                EngagementPoint__c = 'Canale digitale',
                BillingName = 'Bulk Customer ' + i,
                Agency__c = testAgency.Id
            );
            additionalQuotes.add(q);
        }
        insert additionalQuotes;
        
        Test.startTest();
        
        QuotePostProcessingBatch batch = new QuotePostProcessingBatch();
        Database.executeBatch(batch, 10); // Smaller batch size to test chunking
        
        Test.stopTest();
        
        // Verify all quotes were processed
        List<Quote> remainingQuotes = [SELECT Id FROM Quote WHERE to_work__c = true];
        System.assertEquals(0, remainingQuotes.size(), 
                           'All quotes should have been processed');
    }*/
}