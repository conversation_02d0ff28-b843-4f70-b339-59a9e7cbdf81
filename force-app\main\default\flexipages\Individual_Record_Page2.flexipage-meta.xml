<?xml version="1.0" encoding="UTF-8"?>
<FlexiPage xmlns="http://soap.sforce.com/2006/04/metadata">
    <flexiPageRegions>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>actionNames</name>
                    <valueList>
                        <valueListItems>
                            <value>Account.Presa_appuntamento</value>
                        </valueListItems>
                        <valueListItems>
                            <value>CustomButton.Account.Nuova_Nota</value>
                        </valueListItems>
                        <valueListItems>
                            <value>Account.Crea_Trattativa</value>
                        </valueListItems>
                    </valueList>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>collapsed</name>
                    <value>false</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>enableActionsConfiguration</name>
                    <value>true</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>enableActionsInNative</name>
                    <value>true</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>hideChatterActions</name>
                    <value>false</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>numVisibleActions</name>
                    <value>3</value>
                </componentInstanceProperties>
                <componentName>force:highlightsPanel</componentName>
                <identifier>force_highlightsPanel</identifier>
            </componentInstance>
        </itemInstances>
        <name>header</name>
        <type>Region</type>
    </flexiPageRegions>
    <flexiPageRegions>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>exposedAttributes</name>
                    <value>{&quot;Societa&quot;:&quot;UnipolSai&quot;}</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>flexcardName</name>
                    <value>Anagrafica</value>
                </componentInstanceProperties>
                <componentName>runtime_omnistudio:flexcard</componentName>
                <identifier>runtime_omnistudio_flexcard6</identifier>
            </componentInstance>
        </itemInstances>
        <name>Facet-7cd015fb-d063-4a68-abd0-6a125ffe6322</name>
        <type>Facet</type>
    </flexiPageRegions>
    <flexiPageRegions>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>flexcardName</name>
                    <value>AnagraficaUniSalute</value>
                </componentInstanceProperties>
                <componentName>runtime_omnistudio:flexcard</componentName>
                <identifier>runtime_omnistudio_flexcard4</identifier>
            </componentInstance>
        </itemInstances>
        <name>Facet-866c9c28-de2a-400b-9558-d3a50d579de9</name>
        <type>Facet</type>
    </flexiPageRegions>
    <flexiPageRegions>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>flexcardName</name>
                    <value>AnagraficaUnipolRental</value>
                </componentInstanceProperties>
                <componentName>runtime_omnistudio:flexcard</componentName>
                <identifier>runtime_omnistudio_flexcard2</identifier>
            </componentInstance>
        </itemInstances>
        <name>Facet-6ul3sprpa4k</name>
        <type>Facet</type>
    </flexiPageRegions>
    <flexiPageRegions>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>flexcardName</name>
                    <value>AnagraficaUnipolTech</value>
                </componentInstanceProperties>
                <componentName>runtime_omnistudio:flexcard</componentName>
                <identifier>runtime_omnistudio_flexcard5</identifier>
            </componentInstance>
        </itemInstances>
        <name>Facet-1d35d8c1-b5b5-4798-a3cc-cb7af5c8d4d8</name>
        <type>Facet</type>
    </flexiPageRegions>
    <flexiPageRegions>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>active</name>
                    <value>true</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>body</name>
                    <value>Facet-7cd015fb-d063-4a68-abd0-6a125ffe6322</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>title</name>
                    <value>Unipol</value>
                </componentInstanceProperties>
                <componentName>flexipage:tab</componentName>
                <identifier>flexipage_tab</identifier>
                <visibilityRule>
                    <booleanFilter>1 OR 2 OR 3 OR 4</booleanFilter>
                    <criteria>
                        <leftValue>{!$Permission.CustomPermission.TabsUnipolSai}</leftValue>
                        <operator>EQUAL</operator>
                        <rightValue>true</rightValue>
                    </criteria>
                    <criteria>
                        <leftValue>{!$Permission.CustomPermission.CC_Direzione}</leftValue>
                        <operator>EQUAL</operator>
                        <rightValue>true</rightValue>
                    </criteria>
                    <criteria>
                        <leftValue>{!$Permission.CustomPermission.CC_Operatore}</leftValue>
                        <operator>EQUAL</operator>
                        <rightValue>true</rightValue>
                    </criteria>
                    <criteria>
                        <leftValue>{!$Permission.CustomPermission.CC_Supervisor}</leftValue>
                        <operator>EQUAL</operator>
                        <rightValue>true</rightValue>
                    </criteria>
                </visibilityRule>
            </componentInstance>
        </itemInstances>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>body</name>
                    <value>Facet-866c9c28-de2a-400b-9558-d3a50d579de9</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>title</name>
                    <value>UniSalute</value>
                </componentInstanceProperties>
                <componentName>flexipage:tab</componentName>
                <identifier>flexipage_tab3</identifier>
                <visibilityRule>
                    <criteria>
                        <leftValue>{!$Permission.CustomPermission.TabsUniSalute}</leftValue>
                        <operator>EQUAL</operator>
                        <rightValue>true</rightValue>
                    </criteria>
                </visibilityRule>
            </componentInstance>
        </itemInstances>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>body</name>
                    <value>Facet-6ul3sprpa4k</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>title</name>
                    <value>UnipolRental</value>
                </componentInstanceProperties>
                <componentName>flexipage:tab</componentName>
                <identifier>flexipage_tab11</identifier>
                <visibilityRule>
                    <criteria>
                        <leftValue>{!$Permission.CustomPermission.TabsUnipolRental}</leftValue>
                        <operator>EQUAL</operator>
                        <rightValue>true</rightValue>
                    </criteria>
                </visibilityRule>
            </componentInstance>
        </itemInstances>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>body</name>
                    <value>Facet-1d35d8c1-b5b5-4798-a3cc-cb7af5c8d4d8</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>title</name>
                    <value>UnipolTech</value>
                </componentInstanceProperties>
                <componentName>flexipage:tab</componentName>
                <identifier>flexipage_tab12</identifier>
                <visibilityRule>
                    <criteria>
                        <leftValue>{!$Permission.CustomPermission.TabsUnipolTech}</leftValue>
                        <operator>EQUAL</operator>
                        <rightValue>true</rightValue>
                    </criteria>
                </visibilityRule>
            </componentInstance>
        </itemInstances>
        <name>Facet-3b1523dd-2e7a-4558-ae32-91d8c2a56dd3</name>
        <type>Facet</type>
    </flexiPageRegions>
    <flexiPageRegions>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>tabs</name>
                    <value>Facet-3b1523dd-2e7a-4558-ae32-91d8c2a56dd3</value>
                </componentInstanceProperties>
                <componentName>flexipage:tabset</componentName>
                <identifier>flexipage_tabset2</identifier>
            </componentInstance>
        </itemInstances>
        <name>leftcol</name>
        <type>Region</type>
    </flexiPageRegions>
    <flexiPageRegions>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>flexcardName</name>
                    <value>UniProdottiPressoAgenziaSelector</value>
                </componentInstanceProperties>
                <componentName>runtime_omnistudio:flexcard</componentName>
                <identifier>runtime_omnistudio_flexcard12</identifier>
            </componentInstance>
        </itemInstances>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>flexcardName</name>
                    <value>UniProdottiAssicurativi</value>
                </componentInstanceProperties>
                <componentName>runtime_omnistudio:flexcard</componentName>
                <identifier>runtime_omnistudio_flexcard9</identifier>
            </componentInstance>
        </itemInstances>
        <itemInstances>
            <componentInstance>
                <componentName>cfUniProdottiAssicurativiAltraAgenzia</componentName>
                <identifier>c_cfUniProdottiAssicurativiAltraAgenzia</identifier>
            </componentInstance>
        </itemInstances>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>flexcardName</name>
                    <value>UniProdottiNonAssicurativi</value>
                </componentInstanceProperties>
                <componentName>runtime_omnistudio:flexcard</componentName>
                <identifier>runtime_omnistudio_flexcard14</identifier>
            </componentInstance>
        </itemInstances>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>flexcardName</name>
                    <value>UniTitoliInScadenza</value>
                </componentInstanceProperties>
                <componentName>runtime_omnistudio:flexcard</componentName>
                <identifier>runtime_omnistudio_flexcard13</identifier>
            </componentInstance>
        </itemInstances>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>flexcardName</name>
                    <value>UniContenziosi</value>
                </componentInstanceProperties>
                <componentName>runtime_omnistudio:flexcard</componentName>
                <identifier>runtime_omnistudio_flexcard10</identifier>
            </componentInstance>
        </itemInstances>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>flexcardName</name>
                    <value>UniSinistri</value>
                </componentInstanceProperties>
                <componentName>runtime_omnistudio:flexcard</componentName>
                <identifier>runtime_omnistudio_flexcard17</identifier>
            </componentInstance>
        </itemInstances>
        <name>Facet-4948cb4b-f7eb-4ea2-8bde-fc7a267f99c1</name>
        <type>Facet</type>
    </flexiPageRegions>
    <flexiPageRegions>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>flexcardName</name>
                    <value>SS_Documentazione</value>
                </componentInstanceProperties>
                <componentName>runtime_omnistudio:flexcard</componentName>
                <identifier>runtime_omnistudio_flexcard7</identifier>
            </componentInstance>
        </itemInstances>
        <name>Facet-9a7614f2-9c45-48f1-b23b-6aac8d8b68cc</name>
        <type>Facet</type>
    </flexiPageRegions>
    <flexiPageRegions>
        <itemInstances>
            <componentInstance>
                <componentName>builder_industries_insurance:lifeEvents</componentName>
                <identifier>builder_industries_insurance_lifeEvents</identifier>
            </componentInstance>
        </itemInstances>
        <itemInstances>
            <componentInstance>
                <componentName>cfSS_DettagliSocioEconomici</componentName>
                <identifier>c_cfSS_DettagliSocioEconomici</identifier>
            </componentInstance>
        </itemInstances>
        <name>Facet-84f0b58f-a6cb-4580-9d18-7c96b3f95cfb</name>
        <type>Facet</type>
    </flexiPageRegions>
    <flexiPageRegions>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>flexcardName</name>
                    <value>Storico_Attivita</value>
                </componentInstanceProperties>
                <componentName>runtime_omnistudio:flexcard</componentName>
                <identifier>runtime_omnistudio_flexcard8</identifier>
            </componentInstance>
        </itemInstances>
        <name>Facet-asn2n301fze</name>
        <type>Facet</type>
    </flexiPageRegions>
    <flexiPageRegions>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>showLegacyActivityComposer</name>
                    <value>false</value>
                </componentInstanceProperties>
                <componentName>runtime_sales_activities:activityPanel</componentName>
                <identifier>runtime_sales_activities_activityPanel2</identifier>
            </componentInstance>
        </itemInstances>
        <name>Facet-b5b94027-69f6-4403-a64e-41ed2b5c0a12</name>
        <type>Facet</type>
    </flexiPageRegions>
    <flexiPageRegions>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>body</name>
                    <value>Facet-4948cb4b-f7eb-4ea2-8bde-fc7a267f99c1</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>title</name>
                    <value>Prodotti</value>
                </componentInstanceProperties>
                <componentName>flexipage:tab</componentName>
                <identifier>flexipage_tab14</identifier>
            </componentInstance>
        </itemInstances>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>body</name>
                    <value>Facet-9a7614f2-9c45-48f1-b23b-6aac8d8b68cc</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>title</name>
                    <value>Documentazione</value>
                </componentInstanceProperties>
                <componentName>flexipage:tab</componentName>
                <identifier>flexipage_tab4</identifier>
            </componentInstance>
        </itemInstances>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>body</name>
                    <value>Facet-84f0b58f-a6cb-4580-9d18-7c96b3f95cfb</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>title</name>
                    <value>Info Aggiuntive</value>
                </componentInstanceProperties>
                <componentName>flexipage:tab</componentName>
                <identifier>flexipage_tab5</identifier>
            </componentInstance>
        </itemInstances>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>body</name>
                    <value>Facet-asn2n301fze</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>title</name>
                    <value>Timeline</value>
                </componentInstanceProperties>
                <componentName>flexipage:tab</componentName>
                <identifier>flexipage_tab6</identifier>
            </componentInstance>
        </itemInstances>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>body</name>
                    <value>Facet-b5b94027-69f6-4403-a64e-41ed2b5c0a12</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>title</name>
                    <value>Reg Comunicazioni</value>
                </componentInstanceProperties>
                <componentName>flexipage:tab</componentName>
                <identifier>flexipage_tab7</identifier>
            </componentInstance>
        </itemInstances>
        <name>Facet-331840e9-cf2a-42cf-b75b-13d39db21bd8</name>
        <type>Facet</type>
    </flexiPageRegions>
    <flexiPageRegions>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>stage</name>
                    <value>Aperte</value>
                </componentInstanceProperties>
                <componentName>opportunityRelatedList</componentName>
                <identifier>c_opportunityRelatedList</identifier>
            </componentInstance>
        </itemInstances>
        <name>Facet-c1c434a0-93b6-49e1-af92-75f56d691698</name>
        <type>Facet</type>
    </flexiPageRegions>
    <flexiPageRegions>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>stage</name>
                    <value>Chiuse</value>
                </componentInstanceProperties>
                <componentName>opportunityRelatedList</componentName>
                <identifier>c_opportunityRelatedList2</identifier>
            </componentInstance>
        </itemInstances>
        <name>Facet-yr4rpk3dyzl</name>
        <type>Facet</type>
    </flexiPageRegions>
    <flexiPageRegions>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>body</name>
                    <value>Facet-c1c434a0-93b6-49e1-af92-75f56d691698</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>title</name>
                    <value>Trattative Aperte</value>
                </componentInstanceProperties>
                <componentName>flexipage:tab</componentName>
                <identifier>flexipage_tab10</identifier>
            </componentInstance>
        </itemInstances>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>body</name>
                    <value>Facet-yr4rpk3dyzl</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>title</name>
                    <value>Trattative Chiuse</value>
                </componentInstanceProperties>
                <componentName>flexipage:tab</componentName>
                <identifier>flexipage_tab13</identifier>
            </componentInstance>
        </itemInstances>
        <name>Facet-bcacd009-1ca4-4129-9072-050fea5eb91e</name>
        <type>Facet</type>
    </flexiPageRegions>
    <flexiPageRegions>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>stato</name>
                    <value>Aperte</value>
                </componentInstanceProperties>
                <componentName>caseRelatedList</componentName>
                <identifier>c_caseRelatedList</identifier>
            </componentInstance>
        </itemInstances>
        <name>Facet-131c3af4-5f7e-45a2-8d7b-2babd5115755</name>
        <type>Facet</type>
    </flexiPageRegions>
    <flexiPageRegions>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>stato</name>
                    <value>Chiuse</value>
                </componentInstanceProperties>
                <componentName>caseRelatedList</componentName>
                <identifier>c_caseRelatedList2</identifier>
            </componentInstance>
        </itemInstances>
        <name>Facet-fd34cf4e-d8c6-4dc9-88fa-ea9d14af2b68</name>
        <type>Facet</type>
    </flexiPageRegions>
    <flexiPageRegions>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>active</name>
                    <value>true</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>body</name>
                    <value>Facet-131c3af4-5f7e-45a2-8d7b-2babd5115755</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>title</name>
                    <value>Attività Aperte</value>
                </componentInstanceProperties>
                <componentName>flexipage:tab</componentName>
                <identifier>flexipage_tab8</identifier>
            </componentInstance>
        </itemInstances>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>body</name>
                    <value>Facet-fd34cf4e-d8c6-4dc9-88fa-ea9d14af2b68</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>title</name>
                    <value>Attività Chiuse</value>
                </componentInstanceProperties>
                <componentName>flexipage:tab</componentName>
                <identifier>flexipage_tab9</identifier>
            </componentInstance>
        </itemInstances>
        <name>Facet-f7a67339-b1fb-447a-ad07-58ff15d4f09b</name>
        <type>Facet</type>
    </flexiPageRegions>
    <flexiPageRegions>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>tabs</name>
                    <value>Facet-331840e9-cf2a-42cf-b75b-13d39db21bd8</value>
                </componentInstanceProperties>
                <componentName>flexipage:tabset</componentName>
                <identifier>flexipage_tabset</identifier>
            </componentInstance>
        </itemInstances>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>tabs</name>
                    <value>Facet-bcacd009-1ca4-4129-9072-050fea5eb91e</value>
                </componentInstanceProperties>
                <componentName>flexipage:tabset</componentName>
                <identifier>flexipage_tabset3</identifier>
                <visibilityRule>
                    <booleanFilter>1 OR 2</booleanFilter>
                    <criteria>
                        <leftValue>{!$Permission.CustomPermission.TabsUniSalute}</leftValue>
                        <operator>EQUAL</operator>
                        <rightValue>true</rightValue>
                    </criteria>
                    <criteria>
                        <leftValue>{!$Permission.CustomPermission.TabsUnipolSai}</leftValue>
                        <operator>EQUAL</operator>
                        <rightValue>true</rightValue>
                    </criteria>
                </visibilityRule>
            </componentInstance>
        </itemInstances>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>tabs</name>
                    <value>Facet-f7a67339-b1fb-447a-ad07-58ff15d4f09b</value>
                </componentInstanceProperties>
                <componentName>flexipage:tabset</componentName>
                <identifier>flexipage_tabset4</identifier>
                <visibilityRule>
                    <booleanFilter>1 OR 2</booleanFilter>
                    <criteria>
                        <leftValue>{!$Permission.CustomPermission.TabsUniSalute}</leftValue>
                        <operator>EQUAL</operator>
                        <rightValue>true</rightValue>
                    </criteria>
                    <criteria>
                        <leftValue>{!$Permission.CustomPermission.TabsUnipolSai}</leftValue>
                        <operator>EQUAL</operator>
                        <rightValue>true</rightValue>
                    </criteria>
                </visibilityRule>
            </componentInstance>
        </itemInstances>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>flexcardName</name>
                    <value>SS_Box_Task_Contatto</value>
                </componentInstanceProperties>
                <componentName>runtime_omnistudio:flexcard</componentName>
                <identifier>runtime_omnistudio_flexcard3</identifier>
            </componentInstance>
        </itemInstances>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>actionNames</name>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>adminFilters</name>
                    <valueList>
                        <valueListItems>
                            <value>CASES.STATUS|EQUALS|[&quot;Closed&quot;]</value>
                        </valueListItems>
                    </valueList>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>maxRecordsToDisplay</name>
                    <value>2</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>parentFieldApiName</name>
                    <value>Account.Id</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>relatedListApiName</name>
                    <value>Cases</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>relatedListDisplayType</name>
                    <value>CARD</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>relatedListFieldAliases</name>
                    <valueList>
                        <valueListItems>
                            <value>CASES.CASE_NUMBER</value>
                        </valueListItems>
                        <valueListItems>
                            <value>ClosedDate__c</value>
                        </valueListItems>
                        <valueListItems>
                            <value>CASES.ORIGIN</value>
                        </valueListItems>
                        <valueListItems>
                            <value>CommercialAreasOfNeed__c</value>
                        </valueListItems>
                        <valueListItems>
                            <value>Esito__c</value>
                        </valueListItems>
                    </valueList>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>relatedListLabel</name>
                    <value>Storico attività</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>showActionBar</name>
                    <value>false</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>sortFieldAlias</name>
                    <value>__DEFAULT__</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>sortFieldOrder</name>
                    <value>Default</value>
                </componentInstanceProperties>
                <componentName>lst:dynamicRelatedList</componentName>
                <identifier>lst_dynamicRelatedList</identifier>
            </componentInstance>
        </itemInstances>
        <name>rightcol</name>
        <type>Region</type>
    </flexiPageRegions>
    <masterLabel>Individual Record Page</masterLabel>
    <sobjectType>Account</sobjectType>
    <template>
        <name>flexipage:recordHomeTwoColEqualHeaderTemplateDesktop</name>
        <properties>
            <name>enablePageActionConfig</name>
            <value>false</value>
        </properties>
    </template>
    <type>RecordPage</type>
</FlexiPage>
