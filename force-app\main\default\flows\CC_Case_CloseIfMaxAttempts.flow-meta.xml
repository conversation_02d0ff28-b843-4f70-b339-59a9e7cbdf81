<?xml version="1.0" encoding="UTF-8"?>
<Flow xmlns="http://soap.sforce.com/2006/04/metadata">
    <apiVersion>64.0</apiVersion>
    <decisions>
        <name>Decision1</name>
        <label>Decision1</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <defaultConnectorLabel>Default Outcome</defaultConnectorLabel>
        <rules>
            <name>Chiamate_massimo_effettuate_raggiunto</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>$Record.ActualCalls__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <elementReference>$Record.MaxNumberCallAttempts__c</elementReference>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Update_Case_1</targetReference>
            </connector>
            <label>Chiamate massimo effettuate raggiunto</label>
        </rules>
        <rules>
            <name>Tentativi_non_risposti</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>$Record.UnansweredAttempts__c</leftValueReference>
                <operator>GreaterThan</operator>
                <rightValue>
                    <numberValue>0.0</numberValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Update_Case_2</targetReference>
            </connector>
            <label>Tentativi non risposti</label>
        </rules>
    </decisions>
    <environments>Default</environments>
    <interviewLabel>CC_Case_CloseIfMaxAttempts {!$Flow.CurrentDateTime}</interviewLabel>
    <label>CC_Case_CloseIfMaxAttempts</label>
    <processMetadataValues>
        <name>BuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>CanvasMode</name>
        <value>
            <stringValue>AUTO_LAYOUT_CANVAS</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>OriginBuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processType>AutoLaunchedFlow</processType>
    <recordUpdates>
        <name>Update_Case_1</name>
        <label>Update Case 1</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <inputAssignments>
            <field>Esito__c</field>
            <value>
                <stringValue>Mancata risposta -limite chiamate</stringValue>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Status</field>
            <value>
                <stringValue>Chiuso</stringValue>
            </value>
        </inputAssignments>
        <inputReference>$Record</inputReference>
    </recordUpdates>
    <recordUpdates>
        <name>Update_Case_2</name>
        <label>Update Case 2</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <inputAssignments>
            <field>Intermediate_Outcome__c</field>
            <value>
                <stringValue>Tentato Contatto</stringValue>
            </value>
        </inputAssignments>
        <inputReference>$Record</inputReference>
    </recordUpdates>
    <start>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <connector>
            <targetReference>Decision1</targetReference>
        </connector>
        <filterLogic>(1 OR 2) AND 3 AND 4</filterLogic>
        <filters>
            <field>ActualCalls__c</field>
            <operator>IsNull</operator>
            <value>
                <booleanValue>false</booleanValue>
            </value>
        </filters>
        <filters>
            <field>UnansweredAttempts__c</field>
            <operator>IsNull</operator>
            <value>
                <booleanValue>false</booleanValue>
            </value>
        </filters>
        <filters>
            <field>Esito__c</field>
            <operator>IsNull</operator>
            <value>
                <booleanValue>true</booleanValue>
            </value>
        </filters>
        <filters>
            <field>Status</field>
            <operator>NotEqualTo</operator>
            <value>
                <stringValue>Chiuso</stringValue>
            </value>
        </filters>
        <object>Case</object>
        <recordTriggerType>CreateAndUpdate</recordTriggerType>
        <triggerType>RecordAfterSave</triggerType>
    </start>
    <status>Active</status>
</Flow>
