<?xml version="1.0" encoding="UTF-8"?>
<Flow xmlns="http://soap.sforce.com/2006/04/metadata">
    <apiVersion>64.0</apiVersion>
    <decisions>
        <name>checkevent</name>
        <label>checkevent</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <defaultConnector>
            <targetReference>IsChangedOwner</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>Default Outcome</defaultConnectorLabel>
        <rules>
            <name>isInsert</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>$Record__Prior.Id</leftValueReference>
                <operator>IsNull</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>get_trattativa</targetReference>
            </connector>
            <label>isInsert</label>
        </rules>
    </decisions>
    <decisions>
        <name>InCharge</name>
        <label>InCharge</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <defaultConnectorLabel>Default Outcome</defaultConnectorLabel>
        <rules>
            <name>In_Gestione</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>$Record.ActualCalls__c</leftValueReference>
                <operator>GreaterThanOrEqualTo</operator>
                <rightValue>
                    <numberValue>1.0</numberValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.Esito__c</leftValueReference>
                <operator>IsBlank</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.Intermediate_Outcome__c</leftValueReference>
                <operator>IsBlank</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>UpdateStatusInGestione</targetReference>
            </connector>
            <label>In Gestione</label>
        </rules>
        <rules>
            <name>ChiusoDaEsitoIntermedio</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>$Record.ActualCalls__c</leftValueReference>
                <operator>GreaterThan</operator>
                <rightValue>
                    <numberValue>1.0</numberValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.Esito__c</leftValueReference>
                <operator>IsBlank</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.intermediate_outcome__c</leftValueReference>
                <operator>IsBlank</operator>
                <rightValue>
                    <booleanValue>false</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>UpdateStatusChiusoEsitoIntermedio</targetReference>
            </connector>
            <label>Chiuso Da Esito Intermedio</label>
        </rules>
        <rules>
            <name>Chiusura_Da_Esito_Finale</name>
            <conditionLogic>or</conditionLogic>
            <conditions>
                <leftValueReference>$Record.Esito__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Interessato</stringValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.Esito__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Non contattabile</stringValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.Esito__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Non interessato</stringValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.Esito__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Fuori SLA</stringValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.Esito__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Da richiamare Agenzia</stringValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.Esito__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Mancata risposta -limite chiamate</stringValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>UpdateStatusChiusoEsitoFinale</targetReference>
            </connector>
            <label>Chiusura Da Esito Finale</label>
        </rules>
    </decisions>
    <decisions>
        <name>IsChangedOwner</name>
        <label>IsChangedOwner</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <defaultConnector>
            <targetReference>InCharge</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>Default Outcome</defaultConnectorLabel>
        <rules>
            <name>Assegnato</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>$Record.OwnerId</leftValueReference>
                <operator>IsChanged</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.ActualCalls__c</leftValueReference>
                <operator>LessThan</operator>
                <rightValue>
                    <numberValue>1.0</numberValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>UpdateStatusAsseganto</targetReference>
            </connector>
            <label>Assegnato</label>
        </rules>
    </decisions>
    <environments>Default</environments>
    <interviewLabel>CC_ {!$Flow.CurrentDateTime}</interviewLabel>
    <label>CC_Case_UpdatePathStatus</label>
    <processMetadataValues>
        <name>BuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>CanvasMode</name>
        <value>
            <stringValue>AUTO_LAYOUT_CANVAS</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>OriginBuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processType>AutoLaunchedFlow</processType>
    <recordLookups>
        <name>get_trattativa</name>
        <label>get trattativa</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>update_ambito_case</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Id</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>$Record.Opportunity__c</elementReference>
            </value>
        </filters>
        <getFirstRecordOnly>true</getFirstRecordOnly>
        <object>Opportunity</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordUpdates>
        <name>update_ambito_case</name>
        <label>update ambito case</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <inputAssignments>
            <field>CommercialAreasOfNeed__c</field>
            <value>
                <elementReference>get_trattativa.AreaOfNeed__c</elementReference>
            </value>
        </inputAssignments>
        <inputReference>$Record</inputReference>
    </recordUpdates>
    <recordUpdates>
        <name>UpdateStatusAsseganto</name>
        <label>UpdateStatusAsseganto</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <inputAssignments>
            <field>Status</field>
            <value>
                <stringValue>Assegnato</stringValue>
            </value>
        </inputAssignments>
        <inputReference>$Record</inputReference>
    </recordUpdates>
    <recordUpdates>
        <name>UpdateStatusChiusoEsitoFinale</name>
        <label>UpdateStatusChiusoEsitoFinale</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <inputAssignments>
            <field>Status</field>
            <value>
                <stringValue>Chiuso</stringValue>
            </value>
        </inputAssignments>
        <inputReference>$Record</inputReference>
    </recordUpdates>
    <recordUpdates>
        <name>UpdateStatusChiusoEsitoIntermedio</name>
        <label>UpdateStatusChiusoEsitoIntermedio</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <inputAssignments>
            <field>Status</field>
            <value>
                <stringValue>Chiuso</stringValue>
            </value>
        </inputAssignments>
        <inputReference>$Record</inputReference>
    </recordUpdates>
    <recordUpdates>
        <name>UpdateStatusInGestione</name>
        <label>UpdateStatusInGestione</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <inputAssignments>
            <field>Status</field>
            <value>
                <stringValue>In gestione</stringValue>
            </value>
        </inputAssignments>
        <inputReference>$Record</inputReference>
    </recordUpdates>
    <start>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <connector>
            <targetReference>checkevent</targetReference>
        </connector>
        <filterFormula>{!$Record.RecordType.DeveloperName} = &apos;CC_Contact_Center&apos;</filterFormula>
        <object>Case</object>
        <recordTriggerType>CreateAndUpdate</recordTriggerType>
        <triggerType>RecordAfterSave</triggerType>
    </start>
    <status>Active</status>
    <variables>
        <name>RecordTypeId</name>
        <dataType>String</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
    </variables>
</Flow>
