<?xml version="1.0" encoding="UTF-8"?>
<Flow xmlns="http://soap.sforce.com/2006/04/metadata">
    <apiVersion>64.0</apiVersion>
    <areMetricsLoggedToDataCloud>false</areMetricsLoggedToDataCloud>
    <constants>
        <name>FEI_AUTO</name>
        <dataType>String</dataType>
        <value>
            <stringValue>RA.VARIAZIONE.POLIZZA</stringValue>
        </value>
    </constants>
    <constants>
        <name>FEI_RAMI</name>
        <dataType>String</dataType>
        <value>
            <stringValue>RE.VARIAZIONE.POLIZZA</stringValue>
        </value>
    </constants>
    <description>UNF-802 - Gabriele Pirola - Fix Parametri richiamo FEI</description>
    <environments>Default</environments>
    <formulas>
        <name>SelectFEIID</name>
        <dataType>String</dataType>
        <expression>CASE({!GetInsurancePolicyInfo.PolicyType},
&apos;1&apos;, {!FEI_AUTO},
&apos;2&apos;, {!FEI_RAMI},
&apos;undefined policytype&apos;)</expression>
    </formulas>
    <interviewLabel>CaseButton_CertificaModifica {!$Flow.CurrentDateTime}</interviewLabel>
    <label>CaseButton_CertificaModifica</label>
    <processMetadataValues>
        <name>BuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>CanvasMode</name>
        <value>
            <stringValue>AUTO_LAYOUT_CANVAS</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>OriginBuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processType>Flow</processType>
    <recordLookups>
        <name>GetAccountSociety</name>
        <label>GetAccountSociety</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>callFei</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Id</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>GetInsurancePolicyInfo.Society__c</elementReference>
            </value>
        </filters>
        <getFirstRecordOnly>true</getFirstRecordOnly>
        <object>Account</object>
        <queriedFields>Id</queriedFields>
        <queriedFields>Identifier__c</queriedFields>
        <queriedFields>ExternalId__c</queriedFields>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordLookups>
        <name>GetCaseInfo</name>
        <label>GetCaseInfo</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>GetInsurancePolicyInfo</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Id</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>recordId</elementReference>
            </value>
        </filters>
        <getFirstRecordOnly>true</getFirstRecordOnly>
        <object>Case</object>
        <queriedFields>Id</queriedFields>
        <queriedFields>AccountId</queriedFields>
        <queriedFields>Insurance_Policy__c</queriedFields>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordLookups>
        <name>GetInsurancePolicyInfo</name>
        <label>GetInsurancePolicyInfo</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>GetAccountSociety</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Id</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>GetCaseInfo.Insurance_Policy__c</elementReference>
            </value>
        </filters>
        <getFirstRecordOnly>true</getFirstRecordOnly>
        <object>InsurancePolicy</object>
        <queriedFields>Id</queriedFields>
        <queriedFields>PolicyType</queriedFields>
        <queriedFields>Society__c</queriedFields>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <start>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <connector>
            <targetReference>GetCaseInfo</targetReference>
        </connector>
    </start>
    <status>Active</status>
    <subflows>
        <name>callFei</name>
        <label>callFei</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <flowName>FEIQuickAction</flowName>
        <inputAssignments>
            <name>FEIID</name>
            <value>
                <elementReference>SelectFEIID</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <name>recordId</name>
            <value>
                <elementReference>GetInsurancePolicyInfo.Id</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <name>society</name>
            <value>
                <elementReference>GetAccountSociety.ExternalId__c</elementReference>
            </value>
        </inputAssignments>
    </subflows>
    <variables>
        <name>recordId</name>
        <dataType>String</dataType>
        <isCollection>false</isCollection>
        <isInput>true</isInput>
        <isOutput>false</isOutput>
    </variables>
</Flow>
