<?xml version="1.0" encoding="UTF-8"?>
<Flow xmlns="http://soap.sforce.com/2006/04/metadata" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
    <apiVersion>61.0</apiVersion>
    <assignments>
        <name>updateCase</name>
        <label>updateCase</label>
        <locationX>182</locationX>
        <locationY>350</locationY>
        <assignmentItems>
            <assignToReference>getRecord.Outcome__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>EsitoFinale</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>getRecord.SubOutcome__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>PicklistDep.middleValue</elementReference>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>updateRecord</targetReference>
        </connector>
    </assignments>
    <choices>
        <name>EseguitaCliente</name>
        <choiceText>Eseguita dal Cliente</choiceText>
        <dataType>String</dataType>
        <value>
            <stringValue>Eseguita dal Cliente</stringValue>
        </value>
    </choices>
    <choices>
        <name>EseguitaIntermediario</name>
        <choiceText>Eseguita dall&apos;intermediario</choiceText>
        <dataType>String</dataType>
        <value>
            <stringValue>Eseguita dall&apos;intermediario</stringValue>
        </value>
    </choices>
    <choices>
        <name>Indisponibile</name>
        <choiceText>Indisponibile</choiceText>
        <dataType>String</dataType>
        <value>
            <stringValue>Indisponibile</stringValue>
        </value>
    </choices>
    <decisions>
        <name>notesCheck</name>
        <label>notesCheck</label>
        <locationX>182</locationX>
        <locationY>566</locationY>
        <defaultConnector>
            <targetReference>Case_Handling_Activity_History_Flow_1</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>notesBlank</defaultConnectorLabel>
        <rules>
            <name>notesNotBlank</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>Note</leftValueReference>
                <operator>IsBlank</operator>
                <rightValue>
                    <booleanValue>false</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>createNote</targetReference>
            </connector>
            <label>notesNotBlank</label>
        </rules>
    </decisions>
    <description>Update Screen di conferma and Add Note</description>
    <dynamicChoiceSets>
        <name>Outcome</name>
        <dataType>Picklist</dataType>
        <displayField xsi:nil="true"/>
        <object xsi:nil="true"/>
        <picklistField>Outcome__c</picklistField>
        <picklistObject>Case</picklistObject>
    </dynamicChoiceSets>
    <dynamicChoiceSets>
        <name>SubOutcome</name>
        <dataType>Picklist</dataType>
        <displayField xsi:nil="true"/>
        <object xsi:nil="true"/>
        <picklistField>SubOutcome__c</picklistField>
        <picklistObject>Case</picklistObject>
    </dynamicChoiceSets>
    <environments>Default</environments>
    <formulas>
        <name>EsitoFinale</name>
        <dataType>String</dataType>
        <expression>IF(
    NOT(ISBLANK({!PicklistDep.topValue})),
    {!PicklistDep.topValue},
    {!EsitoSpecial}
)</expression>
    </formulas>
    <interviewLabel>ChiusuraAttivitaFlow {!$Flow.CurrentDateTime}</interviewLabel>
    <label>Case Handling - Chiusura</label>
    <processMetadataValues>
        <name>BuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>CanvasMode</name>
        <value>
            <stringValue>AUTO_LAYOUT_CANVAS</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>OriginBuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processType>Flow</processType>
    <recordCreates>
        <name>createContentDocumentLink</name>
        <label>createContentDocumentLink</label>
        <locationX>50</locationX>
        <locationY>782</locationY>
        <connector>
            <targetReference>Case_Handling_Activity_History_Flow_1</targetReference>
        </connector>
        <inputAssignments>
            <field>ContentDocumentId</field>
            <value>
                <elementReference>createNote</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>LinkedEntityId</field>
            <value>
                <elementReference>recordId</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>ShareType</field>
            <value>
                <stringValue>V</stringValue>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Visibility</field>
            <value>
                <stringValue>AllUsers</stringValue>
            </value>
        </inputAssignments>
        <object>ContentDocumentLink</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordCreates>
    <recordCreates>
        <name>createNote</name>
        <label>createNote</label>
        <locationX>50</locationX>
        <locationY>674</locationY>
        <connector>
            <targetReference>createContentDocumentLink</targetReference>
        </connector>
        <inputAssignments>
            <field>Content</field>
            <value>
                <elementReference>Note</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>OwnerId</field>
            <value>
                <elementReference>getRecord.AssignedTo__c</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Title</field>
            <value>
                <stringValue>Note Chiusura</stringValue>
            </value>
        </inputAssignments>
        <object>ContentNote</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordCreates>
    <recordLookups>
        <name>getRecord</name>
        <label>getRecord</label>
        <locationX>182</locationX>
        <locationY>134</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>ChiusuraAttivitaFlow</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Id</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>recordId</elementReference>
            </value>
        </filters>
        <getFirstRecordOnly>true</getFirstRecordOnly>
        <object>Case</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordUpdates>
        <name>updateRecord</name>
        <label>updateRecord</label>
        <locationX>182</locationX>
        <locationY>458</locationY>
        <connector>
            <targetReference>notesCheck</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Id</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>recordId</elementReference>
            </value>
        </filters>
        <inputAssignments>
            <field>Description</field>
            <value>
                <elementReference>getRecord.Description</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Outcome__c</field>
            <value>
                <elementReference>getRecord.Outcome__c</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Status</field>
            <value>
                <stringValue>Closed</stringValue>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>SubOutcome__c</field>
            <value>
                <elementReference>getRecord.SubOutcome__c</elementReference>
            </value>
        </inputAssignments>
        <object>Case</object>
    </recordUpdates>
    <screens>
        <name>ChiusuraAttivitaFlow</name>
        <label>ChiusuraAttivitaFlow</label>
        <locationX>182</locationX>
        <locationY>242</locationY>
        <allowBack>false</allowBack>
        <allowFinish>true</allowFinish>
        <allowPause>false</allowPause>
        <connector>
            <targetReference>updateCase</targetReference>
        </connector>
        <fields>
            <name>conferma</name>
            <fieldText>&lt;p style=&quot;text-align: center;&quot;&gt;&lt;span style=&quot;font-size: 14px;&quot;&gt;Sei sicuro di chiudere il case?&lt;/span&gt;&lt;/p&gt;</fieldText>
            <fieldType>DisplayText</fieldType>
        </fields>
        <fields>
            <name>PicklistDep</name>
            <extensionName>flowruntime:dependentPicklists</extensionName>
            <fieldType>ComponentInstance</fieldType>
            <inputParameters>
                <name>dependencyWrapperApiName</name>
                <value>
                    <stringValue>Case</stringValue>
                </value>
            </inputParameters>
            <inputParameters>
                <name>topPicklistApiName</name>
                <value>
                    <stringValue>Outcome__c</stringValue>
                </value>
            </inputParameters>
            <inputParameters>
                <name>middlePicklistApiName</name>
                <value>
                    <stringValue>SubOutcome__c</stringValue>
                </value>
            </inputParameters>
            <inputParameters>
                <name>topLabel</name>
                <value>
                    <stringValue>Esito</stringValue>
                </value>
            </inputParameters>
            <inputParameters>
                <name>topRequired</name>
                <value>
                    <booleanValue>true</booleanValue>
                </value>
            </inputParameters>
            <inputParameters>
                <name>middleLabel</name>
                <value>
                    <stringValue>Sottoesito</stringValue>
                </value>
            </inputParameters>
            <inputParameters>
                <name>middleRequired</name>
                <value>
                    <booleanValue>true</booleanValue>
                </value>
            </inputParameters>
            <inputsOnNextNavToAssocScrn>UseStoredValues</inputsOnNextNavToAssocScrn>
            <isRequired>true</isRequired>
            <storeOutputAutomatically>true</storeOutputAutomatically>
            <visibilityRule>
                <conditionLogic>and</conditionLogic>
                <conditions>
                    <leftValueReference>getRecord.TECH_ShowOutcome__c</leftValueReference>
                    <operator>EqualTo</operator>
                    <rightValue>
                        <booleanValue>true</booleanValue>
                    </rightValue>
                </conditions>
                <conditions>
                    <leftValueReference>getRecord.LeoActivityCode__c</leftValueReference>
                    <operator>NotEqualTo</operator>
                    <rightValue>
                        <stringValue>10011059</stringValue>
                    </rightValue>
                </conditions>
            </visibilityRule>
        </fields>
        <fields>
            <name>EsitoSpecial</name>
            <choiceReferences>EseguitaCliente</choiceReferences>
            <choiceReferences>EseguitaIntermediario</choiceReferences>
            <choiceReferences>Indisponibile</choiceReferences>
            <dataType>String</dataType>
            <fieldText>Esito</fieldText>
            <fieldType>DropdownBox</fieldType>
            <inputsOnNextNavToAssocScrn>UseStoredValues</inputsOnNextNavToAssocScrn>
            <isRequired>true</isRequired>
            <visibilityRule>
                <conditionLogic>and</conditionLogic>
                <conditions>
                    <leftValueReference>getRecord.TECH_ShowOutcome__c</leftValueReference>
                    <operator>EqualTo</operator>
                    <rightValue>
                        <booleanValue>true</booleanValue>
                    </rightValue>
                </conditions>
                <conditions>
                    <leftValueReference>getRecord.LeoActivityCode__c</leftValueReference>
                    <operator>EqualTo</operator>
                    <rightValue>
                        <stringValue>10011059</stringValue>
                    </rightValue>
                </conditions>
            </visibilityRule>
        </fields>
        <fields>
            <name>Note</name>
            <fieldText>Inserisci altra motivazione</fieldText>
            <fieldType>LargeTextArea</fieldType>
            <inputsOnNextNavToAssocScrn>UseStoredValues</inputsOnNextNavToAssocScrn>
            <isRequired>false</isRequired>
            <visibilityRule>
                <conditionLogic>and</conditionLogic>
                <conditions>
                    <leftValueReference>getRecord.TECH_ShowOutcome__c</leftValueReference>
                    <operator>EqualTo</operator>
                    <rightValue>
                        <booleanValue>true</booleanValue>
                    </rightValue>
                </conditions>
            </visibilityRule>
        </fields>
        <nextOrFinishButtonLabel>Conferma</nextOrFinishButtonLabel>
        <showFooter>true</showFooter>
        <showHeader>false</showHeader>
    </screens>
    <start>
        <locationX>56</locationX>
        <locationY>0</locationY>
        <connector>
            <targetReference>getRecord</targetReference>
        </connector>
    </start>
    <status>Active</status>
    <subflows>
        <name>Case_Handling_Activity_History_Flow_1</name>
        <label>Case Handling - Activity History Flow 1</label>
        <locationX>182</locationX>
        <locationY>974</locationY>
        <flowName>Case_Handling_Activity_History</flowName>
        <inputAssignments>
            <name>ActionType</name>
            <value>
                <stringValue>Close</stringValue>
            </value>
        </inputAssignments>
        <inputAssignments>
            <name>RecordCase</name>
            <value>
                <elementReference>getRecord</elementReference>
            </value>
        </inputAssignments>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </subflows>
    <variables>
        <name>recordId</name>
        <dataType>String</dataType>
        <isCollection>false</isCollection>
        <isInput>true</isInput>
        <isOutput>true</isOutput>
    </variables>
</Flow>
