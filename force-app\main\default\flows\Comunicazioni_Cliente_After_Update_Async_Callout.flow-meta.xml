<?xml version="1.0" encoding="UTF-8"?>
<Flow xmlns="http://soap.sforce.com/2006/04/metadata">
    <actionCalls>
        <name>Get_Converted_DateTime</name>
        <label>Get Converted DateTime</label>
        <locationX>275</locationX>
        <locationY>492</locationY>
        <actionName>InvocableDateTimeWithTimezone</actionName>
        <actionType>apex</actionType>
        <connector>
            <targetReference>GetHousehold</targetReference>
        </connector>
        <flowTransactionModel>CurrentTransaction</flowTransactionModel>
        <inputParameters>
            <name>dateTimeToConvert</name>
            <value>
                <elementReference>$Record.StartDateTime</elementReference>
            </value>
        </inputParameters>
        <nameSegment>InvocableDateTimeWithTimezone</nameSegment>
        <offset>0</offset>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </actionCalls>
    <apiVersion>63.0</apiVersion>
    <assignments>
        <name>SetAgenziaLocation</name>
        <label>SetAgenziaLocation</label>
        <locationX>143</locationX>
        <locationY>924</locationY>
        <assignmentItems>
            <assignToReference>location</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>GetServiceTerritory.Address__c</elementReference>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Check_Prior</targetReference>
        </connector>
    </assignments>
    <assignments>
        <name>SetDefaultLocation</name>
        <label>SetDefaultLocation</label>
        <locationX>407</locationX>
        <locationY>816</locationY>
        <assignmentItems>
            <assignToReference>location</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>$Record.Location</elementReference>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Check_Prior</targetReference>
        </connector>
    </assignments>
    <decisions>
        <name>Check_Email</name>
        <label>Check Email</label>
        <locationX>473</locationX>
        <locationY>384</locationY>
        <defaultConnectorLabel>Default Outcome</defaultConnectorLabel>
        <rules>
            <name>Email_exists</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>Get_Agency_Informations.eventEmail</leftValueReference>
                <operator>IsBlank</operator>
                <rightValue>
                    <booleanValue>false</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Get_Converted_DateTime</targetReference>
            </connector>
            <label>Email exists</label>
        </rules>
    </decisions>
    <decisions>
        <name>Check_Prior</name>
        <label>Check Prior</label>
        <locationX>275</locationX>
        <locationY>1116</locationY>
        <defaultConnector>
            <targetReference>Callout_Mulesoft</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>Default Outcome</defaultConnectorLabel>
        <rules>
            <name>Link_changed</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>$Record.ApptBookingInfoUrl__c</leftValueReference>
                <operator>IsBlank</operator>
                <rightValue>
                    <booleanValue>false</booleanValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.Modalit_di_esecuzione_incontro__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Videochiamata</stringValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.TECH_First_Callout_Appuntamenti__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <booleanValue>false</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Callout_Mulesoft_link_google_meet</targetReference>
            </connector>
            <label>Link changed</label>
        </rules>
    </decisions>
    <decisions>
        <name>CheckMobilita</name>
        <label>CheckMobilita</label>
        <locationX>275</locationX>
        <locationY>708</locationY>
        <defaultConnector>
            <targetReference>SetDefaultLocation</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>Default Outcome</defaultConnectorLabel>
        <rules>
            <name>Mobilita</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>$Record.Modalit_di_esecuzione_incontro__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Agenzia</stringValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.Service_Territory__c</leftValueReference>
                <operator>IsBlank</operator>
                <rightValue>
                    <booleanValue>false</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>GetServiceTerritory</targetReference>
            </connector>
            <label>Mobilita</label>
        </rules>
    </decisions>
    <environments>Default</environments>
    <formulas>
        <name>StartDateFormula</name>
        <dataType>String</dataType>
        <expression>SUBSTITUTE(TEXT({!$Record.StartDateTime}),&apos; &apos;,&apos;T&apos;)</expression>
    </formulas>
    <interviewLabel>Comunicazioni Cliente - After Update Async Callout {!$Flow.CurrentDateTime}</interviewLabel>
    <label>Comunicazioni Cliente - After Update Async Callout</label>
    <processMetadataValues>
        <name>BuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>CanvasMode</name>
        <value>
            <stringValue>AUTO_LAYOUT_CANVAS</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>OriginBuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processType>AutoLaunchedFlow</processType>
    <recordLookups>
        <name>GetHousehold</name>
        <label>GetHousehold</label>
        <locationX>275</locationX>
        <locationY>600</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>CheckMobilita</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Id</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>$Record.FinServ__Household__c</elementReference>
            </value>
        </filters>
        <getFirstRecordOnly>true</getFirstRecordOnly>
        <object>Account</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordLookups>
        <name>GetServiceTerritory</name>
        <label>GetServiceTerritory</label>
        <locationX>143</locationX>
        <locationY>816</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>SetAgenziaLocation</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Id</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>$Record.Service_Territory__c</elementReference>
            </value>
        </filters>
        <getFirstRecordOnly>true</getFirstRecordOnly>
        <object>ServiceTerritory</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordUpdates>
        <name>Update_TECH_field</name>
        <label>Update TECH field</label>
        <locationX>143</locationX>
        <locationY>1332</locationY>
        <inputAssignments>
            <field>TECH_First_Callout_Appuntamenti__c</field>
            <value>
                <booleanValue>true</booleanValue>
            </value>
        </inputAssignments>
        <inputReference>$Record</inputReference>
    </recordUpdates>
    <start>
        <locationX>50</locationX>
        <locationY>0</locationY>
        <filterLogic>1 AND 2 AND (3 OR 4 OR 5 OR 6 OR 7 OR 8 OR 9 OR 11) AND 10 AND ((12 AND 13) OR NOT 12)</filterLogic>
        <filters>
            <field>IsChild</field>
            <operator>EqualTo</operator>
            <value>
                <booleanValue>false</booleanValue>
            </value>
        </filters>
        <filters>
            <field>FinServ__Household__c</field>
            <operator>IsNull</operator>
            <value>
                <booleanValue>false</booleanValue>
            </value>
        </filters>
        <filters>
            <field>OwnerId</field>
            <operator>IsChanged</operator>
            <value>
                <booleanValue>true</booleanValue>
            </value>
        </filters>
        <filters>
            <field>StartDateTime</field>
            <operator>IsChanged</operator>
            <value>
                <booleanValue>true</booleanValue>
            </value>
        </filters>
        <filters>
            <field>EndDateTime</field>
            <operator>IsChanged</operator>
            <value>
                <booleanValue>true</booleanValue>
            </value>
        </filters>
        <filters>
            <field>Location</field>
            <operator>IsChanged</operator>
            <value>
                <booleanValue>true</booleanValue>
            </value>
        </filters>
        <filters>
            <field>ApptBookingInfoUrl__c</field>
            <operator>IsChanged</operator>
            <value>
                <booleanValue>true</booleanValue>
            </value>
        </filters>
        <filters>
            <field>Modalit_di_esecuzione_incontro__c</field>
            <operator>IsChanged</operator>
            <value>
                <booleanValue>true</booleanValue>
            </value>
        </filters>
        <filters>
            <field>Subject</field>
            <operator>IsChanged</operator>
            <value>
                <booleanValue>true</booleanValue>
            </value>
        </filters>
        <filters>
            <field>Mail__c</field>
            <operator>EqualTo</operator>
            <value>
                <booleanValue>true</booleanValue>
            </value>
        </filters>
        <filters>
            <field>Service_Territory__c</field>
            <operator>IsChanged</operator>
            <value>
                <booleanValue>true</booleanValue>
            </value>
        </filters>
        <filters>
            <field>Modalit_di_esecuzione_incontro__c</field>
            <operator>EqualTo</operator>
            <value>
                <stringValue>Videochiamata</stringValue>
            </value>
        </filters>
        <filters>
            <field>ApptBookingInfoUrl__c</field>
            <operator>IsNull</operator>
            <value>
                <booleanValue>false</booleanValue>
            </value>
        </filters>
        <object>Event</object>
        <recordTriggerType>Update</recordTriggerType>
        <scheduledPaths>
            <connector>
                <targetReference>Get_Agency_Informations</targetReference>
            </connector>
            <pathType>AsyncAfterCommit</pathType>
        </scheduledPaths>
        <triggerType>RecordAfterSave</triggerType>
    </start>
    <status>Active</status>
    <subflows>
        <name>Callout_Mulesoft</name>
        <label>Callout Mulesoft</label>
        <locationX>407</locationX>
        <locationY>1224</locationY>
        <flowName>Comunicazioni_Cliente_Callout_Appuntamenti</flowName>
        <inputAssignments>
            <name>Agenzia</name>
            <value>
                <elementReference>Get_Agency_Informations.agenzia</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <name>Argomento</name>
            <value>
                <elementReference>$Record.Subject</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <name>CF_PIVA_Cliente</name>
            <value>
                <elementReference>GetHousehold.ExternalId__c</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <name>Consulente_Di_Riferimento</name>
            <value>
                <elementReference>$Record.AssignedName__c</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <name>Email</name>
            <value>
                <elementReference>Get_Agency_Informations.eventEmail</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <name>Email_Agenzia</name>
            <value>
                <elementReference>Get_Agency_Informations.emailAgenzia</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <name>Link_Video_Call</name>
            <value>
                <elementReference>$Record.ApptBookingInfoUrl__c</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <name>Luogo</name>
            <value>
                <elementReference>location</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <name>Modalita_Di_Esecuzione</name>
            <value>
                <elementReference>$Record.Modalit_di_esecuzione_incontro__c</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <name>Nome_Cliente</name>
            <value>
                <elementReference>GetHousehold.Name</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <name>Ora_Appuntamento</name>
            <value>
                <elementReference>Get_Converted_DateTime.convertedDateTime</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <name>Telefono_Agenzia</name>
            <value>
                <elementReference>Get_Agency_Informations.telefonoAgenzia</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <name>Tipologia_Cliente</name>
            <value>
                <elementReference>Get_Agency_Informations.tipologiaCliente</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <name>type</name>
            <value>
                <stringValue>modifica</stringValue>
            </value>
        </inputAssignments>
    </subflows>
    <subflows>
        <name>Callout_Mulesoft_link_google_meet</name>
        <label>Callout Mulesoft link google meet</label>
        <locationX>143</locationX>
        <locationY>1224</locationY>
        <connector>
            <targetReference>Update_TECH_field</targetReference>
        </connector>
        <flowName>Comunicazioni_Cliente_Callout_Appuntamenti</flowName>
        <inputAssignments>
            <name>Agenzia</name>
            <value>
                <elementReference>Get_Agency_Informations.agenzia</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <name>Argomento</name>
            <value>
                <elementReference>$Record.Subject</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <name>CF_PIVA_Cliente</name>
            <value>
                <elementReference>GetHousehold.ExternalId__c</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <name>Consulente_Di_Riferimento</name>
            <value>
                <elementReference>$Record.AssignedName__c</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <name>Email</name>
            <value>
                <elementReference>Get_Agency_Informations.eventEmail</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <name>Email_Agenzia</name>
            <value>
                <elementReference>Get_Agency_Informations.emailAgenzia</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <name>Link_Video_Call</name>
            <value>
                <elementReference>$Record.ApptBookingInfoUrl__c</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <name>Luogo</name>
            <value>
                <elementReference>location</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <name>Modalita_Di_Esecuzione</name>
            <value>
                <elementReference>$Record.Modalit_di_esecuzione_incontro__c</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <name>Nome_Cliente</name>
            <value>
                <elementReference>GetHousehold.Name</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <name>Ora_Appuntamento</name>
            <value>
                <elementReference>Get_Converted_DateTime.convertedDateTime</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <name>Telefono_Agenzia</name>
            <value>
                <elementReference>Get_Agency_Informations.telefonoAgenzia</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <name>Tipologia_Cliente</name>
            <value>
                <elementReference>Get_Agency_Informations.tipologiaCliente</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <name>type</name>
            <value>
                <stringValue>creazione</stringValue>
            </value>
        </inputAssignments>
    </subflows>
    <subflows>
        <name>Get_Agency_Informations</name>
        <label>Get Agency Informations</label>
        <locationX>473</locationX>
        <locationY>276</locationY>
        <connector>
            <targetReference>Check_Email</targetReference>
        </connector>
        <flowName>Comunicazioni_Cliente_Get_Agency_Informations</flowName>
        <inputAssignments>
            <name>householdId</name>
            <value>
                <elementReference>$Record.FinServ__Household__c</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <name>ownerId</name>
            <value>
                <elementReference>$Record.OwnerId</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <name>serviceAppointmentId</name>
            <value>
                <elementReference>$Record.ServiceAppointmentId</elementReference>
            </value>
        </inputAssignments>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </subflows>
    <variables>
        <name>eventEmail</name>
        <dataType>String</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
    </variables>
    <variables>
        <name>location</name>
        <dataType>String</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
    </variables>
</Flow>
