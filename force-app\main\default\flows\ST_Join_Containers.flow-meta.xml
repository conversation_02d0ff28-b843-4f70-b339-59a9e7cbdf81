<?xml version="1.0" encoding="UTF-8"?>
<Flow xmlns="http://soap.sforce.com/2006/04/metadata">
    <apiVersion>62.0</apiVersion>
    <assignments>
        <name>Add_Id_to_Collection</name>
        <label>Add Id to Collection</label>
        <locationX>666</locationX>
        <locationY>782</locationY>
        <assignmentItems>
            <assignToReference>containerIds</assignToReference>
            <operator>Add</operator>
            <value>
                <elementReference>Loop_Open_Containers_for_Id.Id</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>Containernames</assignToReference>
            <operator>Add</operator>
            <value>
                <elementReference>Loop_Open_Containers_for_Id.Name</elementReference>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Loop_Open_Containers_for_Id</targetReference>
        </connector>
    </assignments>
    <assignments>
        <name>Assign_Container_Count_First_Check</name>
        <label>Assign Container Count First Check</label>
        <locationX>314</locationX>
        <locationY>458</locationY>
        <assignmentItems>
            <assignToReference>containerCount</assignToReference>
            <operator>AssignCount</operator>
            <value>
                <elementReference>Get_Other_Open_Containers</elementReference>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>First_Check_Container_Count</targetReference>
        </connector>
    </assignments>
    <assignments>
        <name>Assign_Current_Id_Selected</name>
        <label>Assign Current Id Selected</label>
        <locationX>666</locationX>
        <locationY>1898</locationY>
        <assignmentItems>
            <assignToReference>containerIdChoised</assignToReference>
            <operator>Add</operator>
            <value>
                <elementReference>Save_Id_Container_Selected.Id</elementReference>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Save_Id_Container_Selected</targetReference>
        </connector>
    </assignments>
    <assignments>
        <name>Assign_Products_to_Bundle</name>
        <label>Assign Products to Bundle</label>
        <locationX>666</locationX>
        <locationY>3662</locationY>
        <assignmentItems>
            <assignToReference>Loop_Over_Containers.Products__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>ProductsVar</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>OpportunityContainerstoUpdateToUse</assignToReference>
            <operator>Add</operator>
            <value>
                <elementReference>Loop_Over_Containers</elementReference>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Loop_Over_Containers</targetReference>
        </connector>
    </assignments>
    <assignments>
        <name>Assign_Valid_Container_Count</name>
        <label>Assign Valid Container Count</label>
        <locationX>578</locationX>
        <locationY>1574</locationY>
        <assignmentItems>
            <assignToReference>validContainerCount</assignToReference>
            <operator>AssignCount</operator>
            <value>
                <elementReference>containerIds</elementReference>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Select_Opportunities</targetReference>
        </connector>
    </assignments>
    <assignments>
        <name>ConcHistory</name>
        <label>ConcHistory</label>
        <locationX>666</locationX>
        <locationY>2738</locationY>
        <assignmentItems>
            <assignToReference>tempHistoryConc</assignToReference>
            <operator>Add</operator>
            <value>
                <stringValue> {!Loop_Selected_Opportunities_Record.Name} </stringValue>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>itemTemplateStoricoAzioni.Opportunity__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>Loop_Selected_Opportunities_Record.Id</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>contactHistoryList</assignToReference>
            <operator>Add</operator>
            <value>
                <elementReference>itemTemplateStoricoAzioni</elementReference>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Loop_Selected_Opportunities_Record</targetReference>
        </connector>
    </assignments>
    <assignments>
        <name>create_History_Collection_Master</name>
        <label>create History Collection Master</label>
        <locationX>314</locationX>
        <locationY>242</locationY>
        <assignmentItems>
            <assignToReference>itemTemplateStoricoAzioni.Description__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <stringValue>{!$User.FirstName} {!$User.LastName}</stringValue>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>itemTemplateStoricoAzioni.Executor__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <stringValue>{!$User.FirstName} {!$User.LastName}</stringValue>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Get_Other_Open_Containers</targetReference>
        </connector>
    </assignments>
    <assignments>
        <name>Create_History_Collection_To_Products</name>
        <label>Create History Collection To Products</label>
        <locationX>578</locationX>
        <locationY>2306</locationY>
        <assignmentItems>
            <assignToReference>itemTemplateStoricoAzioni.Name</assignToReference>
            <operator>Assign</operator>
            <value>
                <stringValue>Trattativa Accorpata in {!Get_Target_Container.Name} </stringValue>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>tempHistoryConc</assignToReference>
            <operator>Assign</operator>
            <value>
                <stringValue>Trattatativa accorpata da </stringValue>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Loop_Selected_Opportunities_Record</targetReference>
        </connector>
    </assignments>
    <assignments>
        <name>Reparent_Products</name>
        <label>Reparent Products</label>
        <locationX>666</locationX>
        <locationY>3038</locationY>
        <assignmentItems>
            <assignToReference>Loop_Opportunity_Product_Retrived.Parent__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>Get_Target_Container.Id</elementReference>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Save_Reparented_in_Collections</targetReference>
        </connector>
    </assignments>
    <assignments>
        <name>Save_Container_Child_Updates</name>
        <label>Save Container Child Updates</label>
        <locationX>666</locationX>
        <locationY>2630</locationY>
        <assignmentItems>
            <assignToReference>OpportunityContainerstoUpdate</assignToReference>
            <operator>Add</operator>
            <value>
                <elementReference>Loop_Selected_Opportunities_Record</elementReference>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>ConcHistory</targetReference>
        </connector>
    </assignments>
    <assignments>
        <name>Save_Product_to_Reparent</name>
        <label>Save Product to Reparent</label>
        <locationX>578</locationX>
        <locationY>2198</locationY>
        <assignmentItems>
            <assignToReference>opportunitiesProductsCollectiontoReparent</assignToReference>
            <operator>Add</operator>
            <value>
                <elementReference>Retrieve_Products_to_Reparents</elementReference>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Create_History_Collection_To_Products</targetReference>
        </connector>
    </assignments>
    <assignments>
        <name>Save_Reparented_in_Collections</name>
        <label>Save Reparented in Collections</label>
        <locationX>666</locationX>
        <locationY>3146</locationY>
        <assignmentItems>
            <assignToReference>OpportunityReparentedCollectiontoUpdate</assignToReference>
            <operator>Add</operator>
            <value>
                <elementReference>Loop_Opportunity_Product_Retrived</elementReference>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Loop_Opportunity_Product_Retrived</targetReference>
        </connector>
    </assignments>
    <assignments>
        <name>Update_Container_Child</name>
        <label>Update Container Child</label>
        <locationX>666</locationX>
        <locationY>2522</locationY>
        <assignmentItems>
            <assignToReference>Loop_Selected_Opportunities_Record.StageName</assignToReference>
            <operator>Assign</operator>
            <value>
                <stringValue>Chiuso</stringValue>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Save_Container_Child_Updates</targetReference>
        </connector>
    </assignments>
    <assignments>
        <name>updateContactHistoryfinal</name>
        <label>updateContactHistoryfinal</label>
        <locationX>578</locationX>
        <locationY>3962</locationY>
        <assignmentItems>
            <assignToReference>itemTemplateStoricoAzioni.Name</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>tempHistoryConc</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>itemTemplateStoricoAzioni.Opportunity__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>Get_Target_Container.Id</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>contactHistoryList</assignToReference>
            <operator>Add</operator>
            <value>
                <elementReference>itemTemplateStoricoAzioni</elementReference>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>CreateContactHistory</targetReference>
        </connector>
    </assignments>
    <choices>
        <name>Containername</name>
        <choiceText>Containername</choiceText>
        <dataType>String</dataType>
        <value>
            <stringValue>Containername</stringValue>
        </value>
    </choices>
    <collectionProcessors>
        <name>Remove_Parent_from_Collection</name>
        <elementSubtype>FilterCollectionProcessor</elementSubtype>
        <label>Remove Parent from Collection</label>
        <locationX>842</locationX>
        <locationY>1298</locationY>
        <assignNextValueToReference>currentItem_Remove_Parent_from_Collection</assignNextValueToReference>
        <collectionProcessorType>FilterCollectionProcessor</collectionProcessorType>
        <collectionReference>containerIds</collectionReference>
        <conditionLogic>and</conditionLogic>
        <conditions>
            <leftValueReference>currentItem_Remove_Parent_from_Collection</leftValueReference>
            <operator>EqualTo</operator>
            <rightValue>
                <elementReference>Loop_Products.Parent__c</elementReference>
            </rightValue>
        </conditions>
        <connector>
            <targetReference>Loop_Products</targetReference>
        </connector>
    </collectionProcessors>
    <decisions>
        <name>Check_if_Product_Open</name>
        <label>Check if Product Open</label>
        <locationX>754</locationX>
        <locationY>1190</locationY>
        <defaultConnector>
            <targetReference>Remove_Parent_from_Collection</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>Is Closed</defaultConnectorLabel>
        <rules>
            <name>Is_Open</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>Loop_Products.StageName</leftValueReference>
                <operator>NotEqualTo</operator>
                <rightValue>
                    <stringValue>Chiuso</stringValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Loop_Products</targetReference>
            </connector>
            <label>Is Open</label>
        </rules>
    </decisions>
    <decisions>
        <name>First_Check_Container_Count</name>
        <label>First Check Container Count</label>
        <locationX>314</locationX>
        <locationY>566</locationY>
        <defaultConnector>
            <targetReference>Loop_Open_Containers_for_Id</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>Check Products</defaultConnectorLabel>
        <rules>
            <name>No_Other_Open_Container</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>containerCount</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <numberValue>0.0</numberValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>NoContainerScreen</targetReference>
            </connector>
            <label>No Other Open Container</label>
        </rules>
    </decisions>
    <description>add RT != Prodotto x scelta container</description>
    <dynamicChoiceSets>
        <name>CollectionOpt</name>
        <collectionReference>Get_Products_for_Containers</collectionReference>
        <dataType>String</dataType>
        <displayField>Name</displayField>
        <object>Opportunity</object>
        <valueField>Name</valueField>
    </dynamicChoiceSets>
    <environments>Default</environments>
    <interviewLabel>ST - Join Containers {!$Flow.CurrentDateTime}</interviewLabel>
    <label>ST - Join Containers</label>
    <loops>
        <name>Loop_Open_Containers_for_Id</name>
        <label>Loop Open Containers for Id</label>
        <locationX>578</locationX>
        <locationY>674</locationY>
        <collectionReference>Get_Other_Open_Containers</collectionReference>
        <iterationOrder>Asc</iterationOrder>
        <nextValueConnector>
            <targetReference>Add_Id_to_Collection</targetReference>
        </nextValueConnector>
        <noMoreValuesConnector>
            <targetReference>Get_Products_for_Containers</targetReference>
        </noMoreValuesConnector>
    </loops>
    <loops>
        <name>Loop_Opportunity_Product_Retrived</name>
        <label>Loop Opportunity Product Retrived</label>
        <locationX>578</locationX>
        <locationY>2930</locationY>
        <collectionReference>opportunitiesProductsCollectiontoReparent</collectionReference>
        <iterationOrder>Asc</iterationOrder>
        <nextValueConnector>
            <targetReference>Reparent_Products</targetReference>
        </nextValueConnector>
        <noMoreValuesConnector>
            <targetReference>Update_Reparents_Products</targetReference>
        </noMoreValuesConnector>
    </loops>
    <loops>
        <name>Loop_Over_Containers</name>
        <label>Loop Over Containers</label>
        <locationX>578</locationX>
        <locationY>3446</locationY>
        <collectionReference>OpportunityContainerstoUpdate</collectionReference>
        <iterationOrder>Asc</iterationOrder>
        <nextValueConnector>
            <targetReference>Evaluate_Container_Products</targetReference>
        </nextValueConnector>
        <noMoreValuesConnector>
            <targetReference>Update_Containers_Child</targetReference>
        </noMoreValuesConnector>
    </loops>
    <loops>
        <name>Loop_Products</name>
        <label>Loop Products</label>
        <locationX>578</locationX>
        <locationY>1082</locationY>
        <collectionReference>Get_Products_for_Containers</collectionReference>
        <iterationOrder>Asc</iterationOrder>
        <nextValueConnector>
            <targetReference>Check_if_Product_Open</targetReference>
        </nextValueConnector>
        <noMoreValuesConnector>
            <targetReference>Assign_Valid_Container_Count</targetReference>
        </noMoreValuesConnector>
    </loops>
    <loops>
        <name>Loop_Selected_Opportunities_Record</name>
        <label>Loop Selected Opportunities Record</label>
        <locationX>578</locationX>
        <locationY>2414</locationY>
        <collectionReference>choicesOpportunityRecord</collectionReference>
        <iterationOrder>Asc</iterationOrder>
        <nextValueConnector>
            <targetReference>Update_Container_Child</targetReference>
        </nextValueConnector>
        <noMoreValuesConnector>
            <targetReference>Loop_Opportunity_Product_Retrived</targetReference>
        </noMoreValuesConnector>
    </loops>
    <loops>
        <name>Save_Id_Container_Selected</name>
        <label>Save Id Container Selected</label>
        <locationX>578</locationX>
        <locationY>1790</locationY>
        <collectionReference>choicesOpportunityRecord</collectionReference>
        <iterationOrder>Asc</iterationOrder>
        <nextValueConnector>
            <targetReference>Assign_Current_Id_Selected</targetReference>
        </nextValueConnector>
        <noMoreValuesConnector>
            <targetReference>Retrieve_Products_to_Reparents</targetReference>
        </noMoreValuesConnector>
    </loops>
    <processMetadataValues>
        <name>BuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>CanvasMode</name>
        <value>
            <stringValue>AUTO_LAYOUT_CANVAS</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>OriginBuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processType>Flow</processType>
    <recordLookups>
        <name>Get_Other_Open_Containers</name>
        <label>Get Other Open Containers</label>
        <locationX>314</locationX>
        <locationY>350</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Assign_Container_Count_First_Check</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>AccountId</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>Get_Target_Container.AccountId</elementReference>
            </value>
        </filters>
        <filters>
            <field>Agency__c</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>Get_Target_Container.Agency__c</elementReference>
            </value>
        </filters>
        <filters>
            <field>StageName</field>
            <operator>NotEqualTo</operator>
            <value>
                <stringValue>Chiuso</stringValue>
            </value>
        </filters>
        <filters>
            <field>Id</field>
            <operator>NotEqualTo</operator>
            <value>
                <elementReference>Get_Target_Container.Id</elementReference>
            </value>
        </filters>
        <filters>
            <field>Record_Type_Name__c</field>
            <operator>NotEqualTo</operator>
            <value>
                <stringValue>Prodotto</stringValue>
            </value>
        </filters>
        <getFirstRecordOnly>false</getFirstRecordOnly>
        <object>Opportunity</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordLookups>
        <name>Get_Products_for_Containers</name>
        <label>Get Products for Containers</label>
        <locationX>578</locationX>
        <locationY>974</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Loop_Products</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Parent__c</field>
            <operator>In</operator>
            <value>
                <elementReference>containerIds</elementReference>
            </value>
        </filters>
        <getFirstRecordOnly>false</getFirstRecordOnly>
        <object>Opportunity</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordLookups>
        <name>Get_Target_Container</name>
        <label>Get Target Container</label>
        <locationX>314</locationX>
        <locationY>134</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>create_History_Collection_Master</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Id</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>recordId</elementReference>
            </value>
        </filters>
        <getFirstRecordOnly>true</getFirstRecordOnly>
        <object>Opportunity</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordLookups>
        <name>Retrieve_Products_to_Reparents</name>
        <label>Retrieve Products to Reparents</label>
        <locationX>578</locationX>
        <locationY>2090</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Save_Product_to_Reparent</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Parent__c</field>
            <operator>In</operator>
            <value>
                <elementReference>containerIdChoised</elementReference>
            </value>
        </filters>
        <getFirstRecordOnly>false</getFirstRecordOnly>
        <object>Opportunity</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordUpdates>
        <name>Update_Containers_Child</name>
        <label>Update Containers Child</label>
        <locationX>578</locationX>
        <locationY>3854</locationY>
        <connector>
            <targetReference>updateContactHistoryfinal</targetReference>
        </connector>
        <inputReference>OpportunityContainerstoUpdateToUse</inputReference>
    </recordUpdates>
    <recordUpdates>
        <name>Update_Reparents_Products</name>
        <label>Update Reparents Products</label>
        <locationX>578</locationX>
        <locationY>3338</locationY>
        <connector>
            <targetReference>Loop_Over_Containers</targetReference>
        </connector>
        <inputReference>OpportunityReparentedCollectiontoUpdate</inputReference>
    </recordUpdates>
    <runInMode>SystemModeWithSharing</runInMode>
    <screens>
        <name>NoContainerScreen</name>
        <label>No Other Container Screen</label>
        <locationX>50</locationX>
        <locationY>674</locationY>
        <allowBack>false</allowBack>
        <allowFinish>true</allowFinish>
        <allowPause>false</allowPause>
        <fields>
            <name>NoContainerTitle</name>
            <fieldText>&lt;p&gt;&lt;strong style=&quot;font-size: 16px;&quot;&gt;Accorpa Trattative&lt;/strong&gt;&lt;/p&gt;</fieldText>
            <fieldType>DisplayText</fieldType>
        </fields>
        <fields>
            <name>NoContainerBody</name>
            <fieldText>&lt;p&gt;Non ci sono altre trattative aperte sul cliente.&lt;/p&gt;</fieldText>
            <fieldType>DisplayText</fieldType>
        </fields>
        <nextOrFinishButtonLabel>Chiudi</nextOrFinishButtonLabel>
        <showFooter>true</showFooter>
        <showHeader>false</showHeader>
    </screens>
    <screens>
        <name>Select_Opportunities</name>
        <label>Select Opportunities</label>
        <locationX>578</locationX>
        <locationY>1682</locationY>
        <allowBack>true</allowBack>
        <allowFinish>true</allowFinish>
        <allowPause>false</allowPause>
        <backButtonLabel>Annulla</backButtonLabel>
        <connector>
            <targetReference>Save_Id_Container_Selected</targetReference>
        </connector>
        <fields>
            <name>selectedOpportunity</name>
            <dataTypeMappings>
                <typeName>T</typeName>
                <typeValue>Opportunity</typeValue>
            </dataTypeMappings>
            <extensionName>flowruntime:datatable</extensionName>
            <fieldType>ComponentInstance</fieldType>
            <inputParameters>
                <name>label</name>
                <value>
                    <stringValue>Scegli la trattativa da accorpare</stringValue>
                </value>
            </inputParameters>
            <inputParameters>
                <name>selectionMode</name>
                <value>
                    <stringValue>MULTI_SELECT</stringValue>
                </value>
            </inputParameters>
            <inputParameters>
                <name>minRowSelection</name>
                <value>
                    <numberValue>1.0</numberValue>
                </value>
            </inputParameters>
            <inputParameters>
                <name>tableData</name>
                <value>
                    <elementReference>Get_Other_Open_Containers</elementReference>
                </value>
            </inputParameters>
            <inputParameters>
                <name>columns</name>
                <value>
                    <stringValue>[{&quot;apiName&quot;:&quot;Name&quot;,&quot;guid&quot;:&quot;column-d8b0&quot;,&quot;editable&quot;:false,&quot;hasCustomHeaderLabel&quot;:true,&quot;customHeaderLabel&quot;:&quot;Nome trattativa&quot;,&quot;wrapText&quot;:true,&quot;order&quot;:0,&quot;label&quot;:&quot;Name&quot;,&quot;type&quot;:&quot;text&quot;}]</stringValue>
                </value>
            </inputParameters>
            <inputParameters>
                <name>shouldDisplayLabel</name>
                <value>
                    <booleanValue>true</booleanValue>
                </value>
            </inputParameters>
            <inputParameters>
                <name>maxRowSelection</name>
                <value>
                    <numberValue>20.0</numberValue>
                </value>
            </inputParameters>
            <inputsOnNextNavToAssocScrn>UseStoredValues</inputsOnNextNavToAssocScrn>
            <isRequired>true</isRequired>
            <outputParameters>
                <assignToReference>choicesOpportunityRecord</assignToReference>
                <name>selectedRows</name>
            </outputParameters>
        </fields>
        <nextOrFinishButtonLabel>Conferma</nextOrFinishButtonLabel>
        <showFooter>true</showFooter>
        <showHeader>false</showHeader>
    </screens>
    <start>
        <locationX>188</locationX>
        <locationY>0</locationY>
        <connector>
            <targetReference>Get_Target_Container</targetReference>
        </connector>
    </start>
    <status>Active</status>
    <subflows>
        <name>CreateContactHistory</name>
        <label>CreateContactHistory</label>
        <locationX>578</locationX>
        <locationY>4070</locationY>
        <connector>
            <targetReference>Update_Container</targetReference>
        </connector>
        <flowName>HandleOpportunity_InsertContactHistory</flowName>
        <inputAssignments>
            <name>ContactHistoryList</name>
            <value>
                <elementReference>contactHistoryList</elementReference>
            </value>
        </inputAssignments>
    </subflows>
    <subflows>
        <name>Evaluate_Container_Products</name>
        <label>Evaluate Container Products</label>
        <locationX>666</locationX>
        <locationY>3554</locationY>
        <connector>
            <targetReference>Assign_Products_to_Bundle</targetReference>
        </connector>
        <flowName>Evaluate_Bundle_Products</flowName>
        <inputAssignments>
            <name>BundleOpportunity</name>
            <value>
                <elementReference>Loop_Over_Containers</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <name>ProductsList</name>
            <value>
                <elementReference>OpportunityReparentedCollectiontoUpdate</elementReference>
            </value>
        </inputAssignments>
        <outputAssignments>
            <assignToReference>ProductsVar</assignToReference>
            <name>ProductsOutput</name>
        </outputAssignments>
    </subflows>
    <subflows>
        <name>Update_Container</name>
        <label>Update Container</label>
        <locationX>578</locationX>
        <locationY>4178</locationY>
        <flowName>Container_Opportunity_Evaluation</flowName>
        <inputAssignments>
            <name>recordId</name>
            <value>
                <elementReference>recordId</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <name>updateRequested</name>
            <value>
                <booleanValue>true</booleanValue>
            </value>
        </inputAssignments>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </subflows>
    <variables>
        <name>choicesOpportunityRecord</name>
        <dataType>SObject</dataType>
        <isCollection>true</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
        <objectType>Opportunity</objectType>
    </variables>
    <variables>
        <name>contactHistoryList</name>
        <dataType>SObject</dataType>
        <isCollection>true</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
        <objectType>ContactHistory__c</objectType>
    </variables>
    <variables>
        <name>containerCount</name>
        <dataType>Number</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
        <scale>0</scale>
        <value>
            <numberValue>0.0</numberValue>
        </value>
    </variables>
    <variables>
        <name>containerIdChoised</name>
        <dataType>String</dataType>
        <isCollection>true</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
    </variables>
    <variables>
        <name>containerIds</name>
        <dataType>String</dataType>
        <isCollection>true</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
    </variables>
    <variables>
        <name>Containernames</name>
        <dataType>String</dataType>
        <isCollection>true</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
    </variables>
    <variables>
        <name>currentItem_Remove_Parent_from_Collection</name>
        <dataType>String</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
    </variables>
    <variables>
        <name>currentItemFromSourceCollection</name>
        <dataType>String</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
    </variables>
    <variables>
        <name>itemHistoryOldContainers</name>
        <dataType>SObject</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
        <objectType>ContactHistory__c</objectType>
    </variables>
    <variables>
        <name>itemTemplateStoricoAzioni</name>
        <dataType>SObject</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
        <objectType>ContactHistory__c</objectType>
    </variables>
    <variables>
        <name>opportunitiesProductsCollectiontoReparent</name>
        <dataType>SObject</dataType>
        <isCollection>true</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
        <objectType>Opportunity</objectType>
    </variables>
    <variables>
        <name>OpportunityContainerstoUpdate</name>
        <dataType>SObject</dataType>
        <isCollection>true</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
        <objectType>Opportunity</objectType>
    </variables>
    <variables>
        <name>OpportunityContainerstoUpdateToUse</name>
        <dataType>SObject</dataType>
        <isCollection>true</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
        <objectType>Opportunity</objectType>
    </variables>
    <variables>
        <name>OpportunityReparentedCollectiontoUpdate</name>
        <dataType>SObject</dataType>
        <isCollection>true</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
        <objectType>Opportunity</objectType>
    </variables>
    <variables>
        <name>ProductsVar</name>
        <dataType>String</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
    </variables>
    <variables>
        <name>recordId</name>
        <dataType>String</dataType>
        <isCollection>false</isCollection>
        <isInput>true</isInput>
        <isOutput>false</isOutput>
    </variables>
    <variables>
        <name>tempHistoryConc</name>
        <dataType>String</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
    </variables>
    <variables>
        <name>validContainerCount</name>
        <dataType>Number</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
        <scale>0</scale>
        <value>
            <numberValue>0.0</numberValue>
        </value>
    </variables>
    <variables>
        <name>validContainers</name>
        <dataType>SObject</dataType>
        <isCollection>true</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
        <objectType>Opportunity</objectType>
    </variables>
    <variables>
        <name>var_OpportunitiesRecord</name>
        <dataType>SObject</dataType>
        <isCollection>false</isCollection>
        <isInput>true</isInput>
        <isOutput>false</isOutput>
        <objectType>Opportunity</objectType>
    </variables>
    <variables>
        <name>var_OpportuntiesRecordCollecton</name>
        <dataType>SObject</dataType>
        <isCollection>true</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
        <objectType>Opportunity</objectType>
    </variables>
</Flow>
