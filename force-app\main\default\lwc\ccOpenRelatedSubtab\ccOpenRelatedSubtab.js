import { LightningElement, api, wire } from 'lwc';
import { openSubtab, EnclosingTabId, getTabInfo } from 'lightning/platformWorkspaceApi';
import { getRecord, getFieldValue } from 'lightning/uiRecordApi';
import getCustomerByCaseId from '@salesforce/apex/CustomerUtils.getCustomerByCaseId';
import RELATED_RECORD_ID from '@salesforce/schema/VoiceCall.RelatedRecordId';

const FIELDS = [RELATED_RECORD_ID];

export default class ccOpenRelatedSubTab extends LightningElement {
    @api recordId;

    @wire(EnclosingTabId) enclosingTabId;


    @wire(getRecord, { recordId: '$recordId', fields: FIELDS })
    async wiredRecord({ data, error }) {
        if (!data) return;

        const relatedId = getFieldValue(data, RELATED_RECORD_ID);
        if (!relatedId) {
            return;
        }

        const dataCustomer = await getCustomerByCaseId({ caseId: relatedId});
        const accountId = dataCustomer?.account?.Id;

        const tabId = this.enclosingTabId;
        if (!tabId){
            return;
        } 

        const tabInfo = await getTabInfo(tabId);
        const primaryTabId = tabInfo.isSubtab ? tabInfo.parentTabId : tabInfo.tabId;

        await openSubtab(primaryTabId, { recordId: relatedId, focus: true });
        await openSubtab(primaryTabId, { recordId: accountId, focus: false });
    }
}