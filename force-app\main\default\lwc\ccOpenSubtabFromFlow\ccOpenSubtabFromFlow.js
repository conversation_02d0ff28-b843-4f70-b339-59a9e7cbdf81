// ccOpenSubtabFromFlow.js
import { LightningElement, api } from 'lwc';
import { getFocusedTabInfo, openSubtab } from 'lightning/platformWorkspaceApi';
import { CloseActionScreenEvent } from 'lightning/actions';

export default class CcOpenSubtabFromFlow extends LightningElement {
  @api recordId;       // passato dal Flow/QuickAction
  @api componentName;  // nome del target dinamico (es. "c__ccDisambiguationRootWrapper")

  async connectedCallback() {
    try {
      const focused = await getFocusedTabInfo();
      const parentId = focused.isSubtab ? focused.parentTabId : focused.tabId;

      const pageRef = {
        type: 'standard__component',
        attributes: { componentName: this.componentName },
        state: { c__recordId: this.recordId }
      };

      console.log('componentName', this.componentName);
      console.log("recordId", this.recordId);
      console.log("parentId", parentId);

      await openSubtab(parentId, {
        recordId: this.recordId,
        pageReference: pageRef,
        focus: true
      });
    } catch (e) {
      // eslint-disable-next-line no-console
      console.error('Errore apertura subtab:', e);
    } finally {
      this.dispatchEvent(new CloseActionScreenEvent());
    }
  }
}
