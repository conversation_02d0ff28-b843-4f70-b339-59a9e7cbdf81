<template>
    <c-loader lwc:ref="loader"></c-loader>
    <div if:false={conferma.mostra}>
        <div class="slds-col slds-border_bottom slds-p-around_xx-small slds-size_12-of-12" style="border-bottom: 2px solid rgb(204, 204, 204);">
            <div class="slds-grid slds-wrap">
                <div class="slds-col slds-size_12-of-12" style="text-align: center">
                    <span style="font-size: 14pt">Adesione FEA</span>
                </div>
            </div>
        </div>
        <div class="slds-col slds-p-around_xx-small slds-size_12-of-12">
            <div class="slds-col slds-p-around_x-small slds-size_12-of-12" style="background-color: rgb(243, 243, 243);">
                <span style="font-size: 10pt; font-weight: bold">Gestione Firma Elettronica Avanzata</span>
            </div>
        </div>
        <div class="slds-grid slds-wrap slds-is-relative" data-id="dettaglio" if:true={pannelloDettaglio}>
            <div class="slds-col slds-p-around_xx-small slds-size_6-of-12">
                <label class="slds-form-element__label slds-no-flex" part="label">Adesione FEA</label>
                <div>
                    {data.adesione}
                </div>
            </div>
            <div class="slds-col slds-p-around_xx-small slds-size_6-of-12">
                <label class="slds-form-element__label slds-no-flex" part="label">Data inizio</label>
                <div>
                    {data.dataInizio}
                </div>
            </div>
            <div class="slds-col slds-p-around_xx-small slds-size_6-of-12">
                <label class="slds-form-element__label slds-no-flex" part="label">Cellulare</label>
                <div>
                    <div class="icon-container icon-success" if:true={showIconSuccessCellulare}></div> {data.cellulare}
                </div>
            </div>
            <div class="slds-col slds-p-around_xx-small slds-size_6-of-12">
                <label class="slds-form-element__label slds-no-flex" part="label">Email</label>
                <div>
                    <div class="icon-container icon-success" if:true={showIconSuccess}></div>
                    <div class="icon-container icon-warning" if:true={showIconWarning}></div>
                    <span>{data.email}</span>
                    <lightning-button
                        if:true={btnRefreshStatoEmail}
                        label="Refresh Stato Email"
                        variant="neutral"
                        class="slds-m-right_small pull-right"
                        onclick={handleRefreshStatoEmail}>
                    </lightning-button>
                </div>
            </div>            
        </div>
        <div class="slds-grid slds-wrap slds-is-relative" data-id="modifica" if:true={pannelloModifica}>
            <div class="slds-col slds-p-around_xx-small slds-size_6-of-12">
                <lightning-radio-group
                    data-id="adesioneFea"
                    label="Adesione FEA"
                    options={options}
                    value={form.adesioneFea}
                    onchange={handleForm}>
                </lightning-radio-group>
                <div class="slds-form-element__help slds-text-color_error" if:true={errore.aggiornamentoFEA}>La FEA non è ancora stata aggiornata</div>
            </div>
            <div class="slds-col slds-p-around_xx-small slds-size_6-of-12">
                <lightning-input
                    data-id="dataInizio"
                    type="text"
                    label="Data inizio"
                    value={form.dataInizio}
                    readonly>
                </lightning-input>
            </div>
            <div class="slds-col slds-p-around_xx-small slds-size_6-of-12">
                <lightning-input
                    data-id="cellulare"
                    type="tel"
                    label="* Cellulare"
                    value={form.cellulare}
                    readonly={form.readonly.cellulare}
                    disabled={form.disabled.cellulare}
                    onchange={handleForm}>
                </lightning-input>
                <div class="slds-form-element__help" if:true={errore.cellulareObbligatorio}>Campo obbligatorio</div>
                <div class="slds-form-element__help" if:true={errore.formatoCellulare}>Formato cellulare errato</div>
            </div>
            <div class="slds-col slds-p-around_xx-small slds-size_6-of-12">
                <lightning-input
                    data-id="email"
                    type="email"
                    label="* Email"
                    value={form.email}
                    readonly={form.readonly.email}
                    disabled={form.disabled.email}
                    onchange={handleForm}
                    oncopy={handleCopy}>
                </lightning-input>
                <div class="slds-form-element__help" if:true={errore.emailObbligatoria}>Campo obbligatorio</div>
                <div class="slds-form-element__help" if:true={errore.formatoEmail}>Formato email non valido</div>
                <lightning-input
                    if:true={form.mostraEmailConfirm}
                    data-id="emailConfirm"
                    type="email"
                    label="* Conferma Email"
                    value={form.emailConfirm}
                    readonly={form.readonly.emailConfirm}
                    disabled={form.disabled.emailConfirm}
                    onchange={handleForm}>
                </lightning-input>
                <div class="slds-form-element__help" if:true={errore.emailConfermaObbligatoria}>Campo obbligatorio</div>
                <div class="slds-form-element__help" if:true={errore.formatoConfermaEmail}>Formato email non valido</div>
                <div class="slds-form-element__help" if:true={errore.emailDiverse}>Email e conferma email devono essere uguali</div>
            </div>
        </div>
        <div class="slds-grid slds-wrap slds-is-relative" data-id="riepilogo" if:true={pannelloRiepilogo}>
            <c-elenco-stampe-contatti stampe={stampe} contatti={contatti} process-type="FEA" ciu={ciu} compagnia={compagnia}></c-elenco-stampe-contatti>
            <!--<div class="slds-col slds-p-around_xx-small slds-size_12-of-12">
                <div class="accordion-header accordion" onclick={toggleSection} data-id="stampe">
                    <lightning-icon icon-name={iconNameStampe} alternative-text="toggle" size="x-small"></lightning-icon>
                    <span class="accordion-label">Elenco Stampe FEA</span>
                </div>
                <div class="accordion-content" if:true={stampe.isOpen}>
                    <lightning-datatable
                        key-field="id"
                        data={stampe.rows}
                        columns={stampe.columns}
                        hide-checkbox-column
                        resize-column-disabled
                        onrowaction={handleRowAction}>
                    </lightning-datatable>
                </div>
            </div>
            <div class="slds-col slds-p-around_xx-small slds-size_12-of-12">
                <div class="accordion-header accordion" onclick={toggleSection} data-id="contatti">
                    <lightning-icon icon-name={iconNameContatti} alternative-text="toggle" size="x-small"></lightning-icon>
                    <span class="accordion-label">Elenco Contatti FEA</span>
                </div>
                <div class="accordion-content" if:true={contatti.isOpen}>
                    <lightning-datatable
                        key-field="id"
                        data={contatti.rows}
                        columns={contatti.columns}
                        hide-checkbox-column
                        resize-column-disabled
                        onrowaction={handleRowAction}>
                    </lightning-datatable>
                </div>
            </div>-->
        </div>
        <div class="slds-col slds-border_top slds-p-around_xx-small slds-size_12-of-12" style="background-color: rgb(243, 243, 243); border-top: 2px solid rgb(204, 204, 204);">
            <div class="slds-col slds-p-around_xx-small slds-size_12-of-12" style="text-align: center">
                <lightning-button
                    label={labelAnnulla}
                    variant="neutral"
                    class="slds-m-right_small"
                    onclick={handleAnnulla}>
                </lightning-button>
                <template if:false={hideConfirmButton}>
                    <lightning-button
                        if:true={btnConferma}
                        label="Conferma"
                        variant="brand"
                        class="slds-m-left_small"
                        onclick={handleConferma}>
                    </lightning-button>
                </template>
                <lightning-button
                    if:true={btnAggiorna}
                    label="Aggiorna"
                    variant="brand"
                    class="slds-m-left_small"
                    onclick={handleAggiorna}>
                </lightning-button>
                <lightning-button
                    if:true={btnModifica}
                    label="Modifica"
                    variant="brand"
                    class="slds-m-left_small"
                    onclick={handleModifica}>
                </lightning-button>
                <lightning-button
                    if:true={btnRevoca}
                    label="Revoca"
                    variant="destructive-text"
                    class="slds-m-left_small"
                    onclick={handleRevoca}>
                </lightning-button>
                <lightning-button
                    if:true={btnInviaRichiesta}
                    label="Invia nuova richiesta di validazione"
                    variant="brand" 
                    class="slds-m-left_small"
                    onclick={handleInviaRichiesta}>
                </lightning-button>
            </div>
        </div>
    </div>
    <div if:true={conferma.mostra}>
        <div class="slds-col slds-border_bottom slds-p-around_xx-small slds-size_12-of-12" style="border-bottom: 2px solid rgb(204, 204, 204);">
            <div class="slds-grid slds-wrap">
                <div class="slds-col slds-size_12-of-12 font-14" style="text-align: center">
                    {conferma.titolo}
                </div>
            </div>
        </div>
        <div class="slds-col slds-p-around_x-small slds-size_12-of-12 font-14">
            {conferma.testo}
        </div>
        <div class="slds-col slds-border_top slds-p-around_xx-small slds-size_12-of-12" style="background-color: rgb(243, 243, 243); border-top: 2px solid rgb(204, 204, 204);">
            <div class="slds-col slds-p-around_xx-small slds-size_12-of-12" style="text-align: center">
                <!--<lightning-button
                    label="Annulla"
                    variant="neutral"
                    class="slds-m-right_small"
                    onclick={handleAnnullaEsegui}
                </lightning-button>-->
                <lightning-button
                    if:true={btnConfermaEsegui}
                    label="Conferma"
                    variant="brand"
                    class="slds-m-left_small"
                    onclick={handleConfermaEsegui}>
                </lightning-button>
            </div>
        </div>
    </div>
    <div if:true={feiData.mostraFeiContainer}>
        <c-fei-container permission-set-name={feiData.permissionSet} -fiscal-code={feiData.fiscalCode} feiid={feiData.feiId} record-id={recordId} fei-request-payload={feiData.payload} society="SOC_1"></c-fei-container>
    </div>
</template>