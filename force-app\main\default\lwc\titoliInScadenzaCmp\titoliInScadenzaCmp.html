<template>
    <lightning-card title="Titoli in scadenza" icon-name="standard:recent">
        <template lwc:if={showUnipolButton}>
            <lightning-button label="Gestione Stampe Unipol" slot="actions" data-fei-id="GESTIONE.STAMPE" data-society-id="SOC_1" onclick={handleStampe}></lightning-button>
        </template>
        <template lwc:if={showUniSaluteButton}>
            <lightning-button label="Gestione Stampe UniSalute" slot="actions" data-fei-id="GESTIONE.STAMPE" data-society-id="SOC_4" onclick={handleStampe}></lightning-button>
        </template>

        <template lwc:if={datatableRendered}>
        <lightning-datatable
                key-field="rowId"
            data={titoliInScadenzaData}
            columns={titoliInScadenzaColumn}
            onrowaction={handleRowAction}
            hide-checkbox-column
        ></lightning-datatable>    
        </template>  
    </lightning-card>  

    <template lwc:if={isFlowModalOpened}>
        <section role="dialog" tabindex="-1" class="slds-modal slds-fade-in-open slds-modal_small">
            <div class="slds-modal__container">
                <header class="slds-modal__header">
                    <h2 class="slds-text-heading_medium">FEI</h2>
                    <button class="slds-button slds-button_icon slds-modal__close slds-button_icon-inverse" title="Chiudi" onclick={toggleFlowModal}>
                        <lightning-icon icon-name="utility:close" alternative-text="close" size="small"></lightning-icon>
                    </button>
                </header>        
                <div class="slds-modal__content slds-p-around_medium">    
                    <lightning-flow 
                        flow-api-name="FEIQuickActionByAccount" 
                        flow-input-variables={flowInputs} 
                        onstatuschange={handleFlowStatusChange}>
                    </lightning-flow>
                    </div>
                </div>
            </section>
            <div class="slds-backdrop slds-backdrop_open"></div>
    </template>

    <template lwc:if={isLWCModalOpened}>
        <section role="dialog" tabindex="-1" class="slds-modal slds-fade-in-open slds-modal_small">
            <div class="slds-modal__container">
                <header class="slds-modal__header">
                    <h2 class="slds-text-heading_medium">FEI</h2>
                    <button class="slds-button slds-button_icon slds-modal__close slds-button_icon-inverse" title="Chiudi" onclick={toggleLWCModal}>
                        <lightning-icon icon-name="utility:close" alternative-text="close" size="small"></lightning-icon>
                    </button>
                </header>        
                <div class="slds-modal__content slds-p-around_medium">
                    <p>FEI Container attivo per: {clickedAction}</p>
                    <c-fei-container
                        feiid={params.feiId}
                        fiscalcode={params.fiscalCode}
                        fei-request-payload={params.feiRequestPayload}
                        permission-set-name={params.permissionSetName}
                        >
                    </c-fei-container>
                </div>
            </div>
        </section>
            
        <div class="slds-backdrop slds-backdrop_open"></div>
    </template>

    <template lwc:if={showSpinner}>
        <lightning-spinner alternative-text={labelLoadingTitoli} size="large"></lightning-spinner> 
    </template>
    



</template>