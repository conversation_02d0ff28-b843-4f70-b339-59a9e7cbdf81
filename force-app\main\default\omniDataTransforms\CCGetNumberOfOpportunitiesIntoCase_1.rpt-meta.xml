<?xml version="1.0" encoding="UTF-8"?>
<OmniDataTransform xmlns="http://soap.sforce.com/2006/04/metadata">
    <active>true</active>
    <assignmentRulesUsed>false</assignmentRulesUsed>
    <deletedOnSuccess>false</deletedOnSuccess>
    <errorIgnored>false</errorIgnored>
    <fieldLevelSecurityEnabled>false</fieldLevelSecurityEnabled>
    <inputType>JSON</inputType>
    <isManagedUsingStdDesigner>false</isManagedUsingStdDesigner>
    <name>CCGetNumberOfOpportunitiesIntoCase</name>
    <nullInputsIncludedInOutput>false</nullInputsIncludedInOutput>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>CCGetNumberOfOpportunitiesIntoCaseCustom0jI9O000000xAEfUAMItem4</globalKey>
        <inputFieldName>Opportunity:Id</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>CCGetNumberOfOpportunitiesIntoCase</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldName>Opportunity:Id</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>CCGetNumberOfOpportunitiesIntoCaseCustom0jI9O000000xAEfUAMItem3</globalKey>
        <inputFieldName>OpportunityCount</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>CCGetNumberOfOpportunitiesIntoCase</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>String</outputFieldFormat>
        <outputFieldName>OpportunityCount</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>CCGetNumberOfOpportunitiesIntoCaseCustom0jI9O000000xAEfUAMItem2</globalKey>
        <inputFieldName>Opportunity:AccountId</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>CCGetNumberOfOpportunitiesIntoCase</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>String</outputFieldFormat>
        <outputFieldName>AccountId</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <formulaConverted>| &quot;SELECT/\/\/COUNT()/\/\/FROM/\/\/Opportunity/\/\/WHERE/\/\/AccountId/\/\/=/\/\/&apos;{0}&apos;/\/\/AND/\/\/Id/\/\/!=/\/\/&apos;{1}&apos;/\/\/AND/\/\/StageName/\/\/!=/\/\/&apos;Closed&apos;/\/\/AND/\/\/StageName/\/\/!=/\/\/&apos;Converted&apos;AND/\/\/StageName/\/\/!=/\/\/&apos;Chiuso&apos;/\/\/AND/\/\/RecordType.DeveloperName/\/\/=/\/\/&apos;Omnicanale&apos;&quot; var:Opportunity:AccountId var:Opportunity:Id COUNTQUERY</formulaConverted>
        <formulaExpression>COUNTQUERY(&quot;SELECT COUNT() FROM Opportunity WHERE AccountId = &apos;{0}&apos; AND Id != &apos;{1}&apos; AND StageName != &apos;Closed&apos; AND StageName != &apos;Converted&apos;AND StageName != &apos;Chiuso&apos; AND RecordType.DeveloperName = &apos;Omnicanale&apos;&quot;, Opportunity:AccountId, Opportunity:Id)</formulaExpression>
        <formulaResultPath>OpportunityCount</formulaResultPath>
        <formulaSequence>1.0</formulaSequence>
        <globalKey>CCGetNumberOfOpportunitiesIntoCaseCustom0jI9O000000xAEfUAMItem1</globalKey>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>CCGetNumberOfOpportunitiesIntoCase</name>
        <outputCreationSequence>0.0</outputCreationSequence>
        <outputFieldName>Formula</outputFieldName>
        <outputObjectName>Formula</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <filterOperator>=</filterOperator>
        <filterValue>recordId</filterValue>
        <globalKey>CCGetNumberOfOpportunitiesIntoCaseCustom8494</globalKey>
        <inputFieldName>Id</inputFieldName>
        <inputObjectName>Case</inputObjectName>
        <inputObjectQuerySequence>1.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>CCGetNumberOfOpportunitiesIntoCase</name>
        <outputCreationSequence>0.0</outputCreationSequence>
        <outputFieldName>Case</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <filterOperator>=</filterOperator>
        <filterValue>Case:Opportunity__c</filterValue>
        <globalKey>CCGetNumberOfOpportunitiesIntoCaseCustom2166</globalKey>
        <inputFieldName>Id</inputFieldName>
        <inputObjectName>Opportunity</inputObjectName>
        <inputObjectQuerySequence>2.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>CCGetNumberOfOpportunitiesIntoCase</name>
        <outputCreationSequence>0.0</outputCreationSequence>
        <outputFieldName>Opportunity</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <outputType>JSON</outputType>
    <previewJsonData>{
  &quot;recordId&quot; : &quot;5009O00000c0YAHQA2&quot;
}</previewJsonData>
    <processSuperBulk>false</processSuperBulk>
    <responseCacheTtlMinutes>0.0</responseCacheTtlMinutes>
    <rollbackOnError>false</rollbackOnError>
    <sourceObject>json</sourceObject>
    <sourceObjectDefault>false</sourceObjectDefault>
    <synchronousProcessThreshold>0.0</synchronousProcessThreshold>
    <type>Extract</type>
    <uniqueName>CCGetNumberOfOpportunitiesIntoCase_1</uniqueName>
    <versionNumber>1.0</versionNumber>
    <xmlDeclarationRemoved>false</xmlDeclarationRemoved>
</OmniDataTransform>
