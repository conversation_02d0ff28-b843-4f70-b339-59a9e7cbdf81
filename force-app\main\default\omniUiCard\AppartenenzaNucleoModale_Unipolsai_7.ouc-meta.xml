<?xml version="1.0" encoding="UTF-8"?>
<OmniUiCard xmlns="http://soap.sforce.com/2006/04/metadata">
    <authorName>Unipolsai</authorName>
    <clonedFromOmniUiCardKey>UltimoAccessModale/Unipolsai/6.0</clonedFromOmniUiCardKey>
    <dataSourceConfig>{&quot;dataSource&quot;:{&quot;type&quot;:&quot;Custom&quot;,&quot;value&quot;:{&quot;dsDelay&quot;:&quot;&quot;,&quot;body&quot;:&quot; [\n    {\n      \&quot;birthDate\&quot;: ************,\n      \&quot;personType\&quot;: \&quot;P\&quot;,\n      \&quot;id\&quot;: \&quot;***************\&quot;,\n      \&quot;name\&quot;: \&quot;OLIVIERI MARINA\&quot;,\n      \&quot;groupingId\&quot;: \&quot;****************\&quot;,\n      \&quot;phone\&quot;: \&quot;*********\&quot;,\n      \&quot;cf\&quot;: \&quot;****************\&quot;,\n      \&quot;role\&quot;: \&quot;FAMIGLIARE\&quot;,\n      \&quot;address\&quot;: \&quot;VIA MARCONI 1 GRONTARDO CR\&quot;\n    },\n    {\n      \&quot;birthDate\&quot;: ***********,\n      \&quot;personType\&quot;: \&quot;P\&quot;,\n      \&quot;id\&quot;: \&quot;****************\&quot;,\n      \&quot;name\&quot;: \&quot;BONI CORRADO\&quot;,\n      \&quot;groupingId\&quot;: \&quot;****************\&quot;,\n      \&quot;phone\&quot;: \&quot;*********\&quot;,\n      \&quot;cf\&quot;: \&quot;****************\&quot;,\n      \&quot;role\&quot;: \&quot;CAPOFAMIGLIA\&quot;,\n      \&quot;address\&quot;: \&quot;VIA DELLA CONCILIAZIONE 1 ROMA RM\&quot;\n    },\n    {\n      \&quot;personType\&quot;: \&quot;P\&quot;,\n      \&quot;id\&quot;: \&quot;7741288392777695\&quot;,\n      \&quot;name\&quot;: \&quot;BONI CORRADO,OLIVIERI MARINA \&quot;,\n      \&quot;groupingId\&quot;: \&quot;****************\&quot;,\n      \&quot;role\&quot;: \&quot;COINTESTAZIONE\&quot;,\n      \&quot;address\&quot;: \&quot;V. MARCONI 1/C GRONTARDO CR\&quot;\n    },\n    {\n      \&quot;birthDate\&quot;: 1333404000000,\n      \&quot;personType\&quot;: \&quot;P\&quot;,\n      \&quot;id\&quot;: \&quot;327437879682948101\&quot;,\n      \&quot;name\&quot;: \&quot;BONI ALMA\&quot;,\n      \&quot;groupingId\&quot;: \&quot;****************\&quot;,\n      \&quot;phone\&quot;: \&quot;0372801601\&quot;,\n      \&quot;cf\&quot;: \&quot;****************\&quot;,\n      \&quot;role\&quot;: \&quot;FAMIGLIARE\&quot;,\n      \&quot;address\&quot;: \&quot;VIA MARCONI 1/C GRONTARDO CR\&quot;\n    },\n    {\n      \&quot;birthDate\&quot;: 1393714800000,\n      \&quot;personType\&quot;: \&quot;P\&quot;,\n      \&quot;id\&quot;: \&quot;965639454047518202\&quot;,\n      \&quot;name\&quot;: \&quot;BONI AGATA\&quot;,\n      \&quot;groupingId\&quot;: \&quot;****************\&quot;,\n      \&quot;cf\&quot;: \&quot;****************\&quot;,\n      \&quot;role\&quot;: \&quot;FAMIGLIARE\&quot;,\n      \&quot;address\&quot;: \&quot;VIA MARCONI 1/C GRONTARDO CR\&quot;\n    },\n    {\n      \&quot;birthDate\&quot;: 1208988000000,\n      \&quot;personType\&quot;: \&quot;P\&quot;,\n      \&quot;id\&quot;: \&quot;233239454937800101\&quot;,\n      \&quot;name\&quot;: \&quot;BONI ENEA\&quot;,\n      \&quot;groupingId\&quot;: \&quot;****************\&quot;,\n      \&quot;phone\&quot;: \&quot;3919113954\&quot;,\n      \&quot;cf\&quot;: \&quot;****************\&quot;,\n      \&quot;role\&quot;: \&quot;FAMIGLIARE\&quot;,\n      \&quot;address\&quot;: \&quot;VIA MATTIA BATTISTINI 176 ROMA RM\&quot;\n    }\n  ]&quot;,&quot;resultVar&quot;:&quot;&quot;},&quot;orderBy&quot;:{&quot;name&quot;:&quot;&quot;,&quot;isReverse&quot;:&quot;&quot;},&quot;contextVariables&quot;:[{&quot;name&quot;:&quot;cf&quot;,&quot;val&quot;:&quot;****************&quot;,&quot;id&quot;:3}]}}</dataSourceConfig>
    <description>MARK CHIPEI DC1104-726</description>
    <isActive>true</isActive>
    <isManagedUsingStdDesigner>false</isManagedUsingStdDesigner>
    <name>AppartenenzaNucleoModale</name>
    <omniUiCardType>Child</omniUiCardType>
    <propertySetConfig>{&quot;states&quot;:[{&quot;fields&quot;:[],&quot;conditions&quot;:{&quot;id&quot;:&quot;state-condition-object&quot;,&quot;isParent&quot;:true,&quot;group&quot;:[]},&quot;definedActions&quot;:{&quot;actions&quot;:[]},&quot;name&quot;:&quot;Appartenenza Nuclei&quot;,&quot;isSmartAction&quot;:false,&quot;smartAction&quot;:{},&quot;styleObject&quot;:{&quot;padding&quot;:[{&quot;type&quot;:&quot;around&quot;,&quot;size&quot;:&quot;x-small&quot;}],&quot;margin&quot;:[{&quot;type&quot;:&quot;around&quot;,&quot;size&quot;:&quot;none&quot;}],&quot;container&quot;:{&quot;class&quot;:&quot;slds-card&quot;},&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;12&quot;},&quot;sizeClass&quot;:&quot;slds-size_12-of-12&quot;,&quot;class&quot;:&quot;slds-card slds-p-around_x-small slds-m-bottom_x-small&quot;},&quot;components&quot;:{&quot;layer-0&quot;:{&quot;children&quot;:[{&quot;name&quot;:&quot;Text&quot;,&quot;element&quot;:&quot;outputField&quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;12&quot;,&quot;large&quot;:&quot;12&quot;,&quot;medium&quot;:&quot;12&quot;,&quot;small&quot;:&quot;12&quot;},&quot;stateIndex&quot;:0,&quot;class&quot;:&quot;slds-col &quot;,&quot;property&quot;:{&quot;record&quot;:&quot;{record}&quot;,&quot;mergeField&quot;:&quot;%3Cdiv%20class=%22slds-text-align_center%22%3E%3Cspan%20style=%22font-size:%2018pt;%22%3EAppartenenza%20Nuclei%3C/span%3E%3C/div%3E&quot;,&quot;card&quot;:&quot;{card}&quot;},&quot;type&quot;:&quot;text&quot;,&quot;styleObject&quot;:{&quot;sizeClass&quot;:&quot;slds-size_12-of-12 &quot;,&quot;padding&quot;:[{&quot;type&quot;:&quot;top&quot;,&quot;size&quot;:&quot;small&quot;,&quot;label&quot;:&quot;top:small&quot;}],&quot;margin&quot;:[],&quot;background&quot;:{&quot;color&quot;:&quot;&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;12&quot;,&quot;large&quot;:&quot;12&quot;,&quot;medium&quot;:&quot;12&quot;,&quot;small&quot;:&quot;12&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:[],&quot;width&quot;:&quot;&quot;,&quot;color&quot;:&quot;#cccccc&quot;,&quot;radius&quot;:&quot;&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{},&quot;text&quot;:{&quot;align&quot;:&quot;center&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;&quot;,&quot;class&quot;:&quot;slds-theme_default slds-text-align_center  slds-p-top_small &quot;,&quot;style&quot;:&quot;      \n         &quot;,&quot;theme&quot;:&quot;theme_default&quot;,&quot;minHeight&quot;:&quot;&quot;},&quot;elementLabel&quot;:&quot;Text-0&quot;,&quot;styleObjects&quot;:[{&quot;key&quot;:0,&quot;conditions&quot;:&quot;default&quot;,&quot;styleObject&quot;:{&quot;sizeClass&quot;:&quot;slds-size_12-of-12 &quot;,&quot;padding&quot;:[{&quot;type&quot;:&quot;top&quot;,&quot;size&quot;:&quot;small&quot;,&quot;label&quot;:&quot;top:small&quot;}],&quot;margin&quot;:[],&quot;background&quot;:{&quot;color&quot;:&quot;&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;12&quot;,&quot;large&quot;:&quot;12&quot;,&quot;medium&quot;:&quot;12&quot;,&quot;small&quot;:&quot;12&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:[],&quot;width&quot;:&quot;&quot;,&quot;color&quot;:&quot;#cccccc&quot;,&quot;radius&quot;:&quot;&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{},&quot;text&quot;:{&quot;align&quot;:&quot;center&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;&quot;,&quot;class&quot;:&quot;slds-theme_default slds-text-align_center  slds-p-top_small &quot;,&quot;style&quot;:&quot;      \n         &quot;,&quot;theme&quot;:&quot;theme_default&quot;,&quot;minHeight&quot;:&quot;&quot;},&quot;label&quot;:&quot;Default&quot;,&quot;name&quot;:&quot;Default&quot;,&quot;conditionString&quot;:&quot;&quot;,&quot;draggable&quot;:false}]},{&quot;name&quot;:&quot;Text&quot;,&quot;element&quot;:&quot;outputField&quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;12&quot;},&quot;stateIndex&quot;:0,&quot;class&quot;:&quot;slds-col &quot;,&quot;property&quot;:{&quot;record&quot;:&quot;{record}&quot;,&quot;mergeField&quot;:&quot;&quot;,&quot;card&quot;:&quot;{card}&quot;},&quot;type&quot;:&quot;text&quot;,&quot;styleObject&quot;:{&quot;sizeClass&quot;:&quot;slds-size_12-of-12 &quot;,&quot;padding&quot;:[{&quot;type&quot;:&quot;top&quot;,&quot;size&quot;:&quot;medium&quot;,&quot;label&quot;:&quot;top:medium&quot;}],&quot;margin&quot;:[],&quot;background&quot;:{&quot;color&quot;:&quot;&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;12&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:&quot;border_bottom&quot;,&quot;width&quot;:&quot;&quot;,&quot;color&quot;:&quot;#C7C3C3&quot;,&quot;radius&quot;:&quot;&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{},&quot;text&quot;:{&quot;align&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;&quot;,&quot;height&quot;:&quot;4px&quot;,&quot;class&quot;:&quot;slds-border_bottom slds-p-top_medium &quot;,&quot;style&quot;:&quot;     border-bottom: #C7C3C3 1px solid; \n     height:4px;    &quot;},&quot;elementLabel&quot;:&quot;Text-1&quot;,&quot;styleObjects&quot;:[{&quot;key&quot;:0,&quot;conditions&quot;:&quot;default&quot;,&quot;styleObject&quot;:{&quot;sizeClass&quot;:&quot;slds-size_12-of-12 &quot;,&quot;padding&quot;:[{&quot;type&quot;:&quot;top&quot;,&quot;size&quot;:&quot;medium&quot;,&quot;label&quot;:&quot;top:medium&quot;}],&quot;margin&quot;:[],&quot;background&quot;:{&quot;color&quot;:&quot;&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;12&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:&quot;border_bottom&quot;,&quot;width&quot;:&quot;&quot;,&quot;color&quot;:&quot;#C7C3C3&quot;,&quot;radius&quot;:&quot;&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{},&quot;text&quot;:{&quot;align&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;&quot;,&quot;height&quot;:&quot;4px&quot;,&quot;class&quot;:&quot;slds-border_bottom slds-p-top_medium &quot;,&quot;style&quot;:&quot;     border-bottom: #C7C3C3 1px solid; \n     height:4px;    &quot;},&quot;label&quot;:&quot;Default&quot;,&quot;name&quot;:&quot;Default&quot;,&quot;conditionString&quot;:&quot;&quot;,&quot;draggable&quot;:false}]},{&quot;name&quot;:&quot;Datatable&quot;,&quot;element&quot;:&quot;flexDatatable&quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;12&quot;},&quot;stateIndex&quot;:0,&quot;class&quot;:&quot;slds-col &quot;,&quot;property&quot;:{&quot;record&quot;:&quot;{record}&quot;,&quot;card&quot;:&quot;{card}&quot;,&quot;issearchavailable&quot;:false,&quot;issortavailable&quot;:true,&quot;cellLevelEdit&quot;:true,&quot;pagelimit&quot;:3,&quot;groupOrder&quot;:&quot;asc&quot;,&quot;searchDatatable&quot;:&quot;&quot;,&quot;data-conditions&quot;:{&quot;id&quot;:&quot;state-condition-object&quot;,&quot;isParent&quot;:true,&quot;group&quot;:[]},&quot;styles&quot;:{&quot;cellMargin&quot;:[],&quot;cellPadding&quot;:[]},&quot;data-preloadConditionalElement&quot;:false,&quot;columns&quot;:[{&quot;fieldName&quot;:&quot;name&quot;,&quot;label&quot;:&quot;Nome&quot;,&quot;searchable&quot;:true,&quot;sortable&quot;:true,&quot;editable&quot;:&quot;false&quot;,&quot;type&quot;:&quot;text&quot;},{&quot;fieldName&quot;:&quot;personType&quot;,&quot;label&quot;:&quot;Tipo Persona&quot;,&quot;searchable&quot;:true,&quot;sortable&quot;:true,&quot;editable&quot;:&quot;false&quot;,&quot;userSelectable&quot;:&quot;false&quot;,&quot;visible&quot;:&quot;false&quot;},{&quot;fieldName&quot;:&quot;phone&quot;,&quot;label&quot;:&quot;Telefono&quot;,&quot;searchable&quot;:true,&quot;sortable&quot;:true,&quot;editable&quot;:&quot;false&quot;,&quot;visible&quot;:&quot;false&quot;},{&quot;fieldName&quot;:&quot;address&quot;,&quot;label&quot;:&quot;Indirizzo&quot;,&quot;searchable&quot;:true,&quot;sortable&quot;:true,&quot;editable&quot;:&quot;false&quot;},{&quot;fieldName&quot;:&quot;birthDate&quot;,&quot;label&quot;:&quot;Data di Nascita&quot;,&quot;searchable&quot;:true,&quot;sortable&quot;:true,&quot;editable&quot;:&quot;false&quot;,&quot;visible&quot;:&quot;false&quot;},{&quot;fieldName&quot;:&quot;role&quot;,&quot;label&quot;:&quot;Ruolo&quot;,&quot;searchable&quot;:true,&quot;sortable&quot;:true,&quot;editable&quot;:&quot;false&quot;},{&quot;fieldName&quot;:&quot;cf&quot;,&quot;label&quot;:&quot;Codice Fiscale&quot;,&quot;searchable&quot;:true,&quot;sortable&quot;:true,&quot;userSelectable&quot;:&quot;false&quot;,&quot;editable&quot;:&quot;false&quot;},{&quot;fieldName&quot;:&quot;groupingId&quot;,&quot;label&quot;:&quot;Id Group&quot;,&quot;searchable&quot;:true,&quot;sortable&quot;:true,&quot;visible&quot;:&quot;false&quot;,&quot;editable&quot;:&quot;false&quot;},{&quot;fieldName&quot;:&quot;id&quot;,&quot;label&quot;:&quot;Id&quot;,&quot;searchable&quot;:true,&quot;sortable&quot;:true,&quot;visible&quot;:&quot;false&quot;,&quot;editable&quot;:&quot;false&quot;}],&quot;records&quot;:&quot;{records}&quot;},&quot;type&quot;:&quot;element&quot;,&quot;styleObject&quot;:{&quot;sizeClass&quot;:&quot;slds-size_12-of-12 &quot;,&quot;padding&quot;:[],&quot;margin&quot;:[],&quot;background&quot;:{&quot;color&quot;:&quot;&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;12&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:&quot;&quot;,&quot;width&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;,&quot;radius&quot;:&quot;&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{&quot;styles&quot;:{&quot;cellMargin&quot;:[],&quot;cellPadding&quot;:[]}},&quot;text&quot;:{&quot;align&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;&quot;,&quot;class&quot;:&quot;&quot;,&quot;style&quot;:&quot;      \n         &quot;},&quot;elementLabel&quot;:&quot;Datatable-2&quot;,&quot;styleObjects&quot;:[{&quot;key&quot;:0,&quot;conditions&quot;:&quot;default&quot;,&quot;styleObject&quot;:{&quot;sizeClass&quot;:&quot;slds-size_12-of-12 &quot;,&quot;padding&quot;:[],&quot;margin&quot;:[],&quot;background&quot;:{&quot;color&quot;:&quot;&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;12&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:&quot;&quot;,&quot;width&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;,&quot;radius&quot;:&quot;&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{&quot;styles&quot;:{&quot;cellMargin&quot;:[],&quot;cellPadding&quot;:[]}},&quot;text&quot;:{&quot;align&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;&quot;,&quot;class&quot;:&quot;&quot;,&quot;style&quot;:&quot;      \n         &quot;},&quot;label&quot;:&quot;Default&quot;,&quot;name&quot;:&quot;Default&quot;,&quot;conditionString&quot;:&quot;&quot;,&quot;draggable&quot;:false}]},{&quot;name&quot;:&quot;Block&quot;,&quot;element&quot;:&quot;block&quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;12&quot;},&quot;stateIndex&quot;:0,&quot;class&quot;:&quot;slds-col &quot;,&quot;property&quot;:{&quot;label&quot;:&quot;Block&quot;,&quot;collapsible&quot;:false,&quot;record&quot;:&quot;{record}&quot;,&quot;collapsedByDefault&quot;:false,&quot;card&quot;:&quot;{card}&quot;},&quot;type&quot;:&quot;block&quot;,&quot;styleObject&quot;:{&quot;padding&quot;:[{&quot;type&quot;:&quot;around&quot;,&quot;size&quot;:&quot;x-small&quot;}],&quot;class&quot;:&quot;slds-p-around_x-small&quot;,&quot;sizeClass&quot;:&quot;slds-size_12-of-12&quot;},&quot;children&quot;:[{&quot;key&quot;:&quot;element_element_block_7_0_block_0_0&quot;,&quot;name&quot;:&quot;Block&quot;,&quot;element&quot;:&quot;block&quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;12&quot;},&quot;stateIndex&quot;:0,&quot;class&quot;:&quot;slds-col &quot;,&quot;property&quot;:{&quot;label&quot;:&quot;Block&quot;,&quot;collapsible&quot;:false,&quot;record&quot;:&quot;{record}&quot;,&quot;collapsedByDefault&quot;:false,&quot;card&quot;:&quot;{card}&quot;},&quot;type&quot;:&quot;block&quot;,&quot;styleObject&quot;:{&quot;padding&quot;:[{&quot;type&quot;:&quot;around&quot;,&quot;size&quot;:&quot;x-small&quot;}],&quot;class&quot;:&quot;slds-p-around_x-small&quot;,&quot;sizeClass&quot;:&quot;slds-size_12-of-12&quot;},&quot;children&quot;:[],&quot;parentElementKey&quot;:&quot;element_block_7_0&quot;,&quot;elementLabel&quot;:&quot;Block-2-Block-1&quot;}],&quot;elementLabel&quot;:&quot;Block-4&quot;},{&quot;name&quot;:&quot;Action&quot;,&quot;element&quot;:&quot;action&quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;6&quot;},&quot;stateIndex&quot;:0,&quot;class&quot;:&quot;slds-col &quot;,&quot;property&quot;:{&quot;label&quot;:&quot;Chiudi&quot;,&quot;iconName&quot;:&quot;action:close&quot;,&quot;record&quot;:&quot;{record}&quot;,&quot;card&quot;:&quot;{card}&quot;,&quot;stateObj&quot;:&quot;{record}&quot;,&quot;actionList&quot;:[{&quot;stateAction&quot;:{&quot;id&quot;:&quot;flex-action-1736429502015&quot;,&quot;type&quot;:&quot;Event&quot;,&quot;openUrlIn&quot;:&quot;Current Window&quot;,&quot;hasExtraParams&quot;:false,&quot;subType&quot;:&quot;Custom&quot;,&quot;eventName&quot;:&quot;closemodal&quot;,&quot;message&quot;:&quot;closemodal&quot;,&quot;composed&quot;:true,&quot;bubbles&quot;:true},&quot;key&quot;:&quot;*************-gmbaaelec&quot;,&quot;label&quot;:&quot;Action&quot;,&quot;draggable&quot;:true,&quot;isOpen&quot;:true,&quot;actionIndex&quot;:0,&quot;reRenderFlyout&quot;:true,&quot;isTrackingDisabled&quot;:false}],&quot;showSpinner&quot;:&quot;true&quot;,&quot;flyoutDetails&quot;:{},&quot;displayAsButton&quot;:true,&quot;buttonVariant&quot;:&quot;brand&quot;,&quot;hideActionIcon&quot;:true,&quot;flyoutChannel&quot;:&quot;close&quot;,&quot;disabled&quot;:&quot;&quot;,&quot;reRenderFlyout&quot;:true,&quot;iconOnly&quot;:false,&quot;styles&quot;:{&quot;label&quot;:{&quot;color&quot;:&quot;#EAE6E6&quot;,&quot;textAlign&quot;:&quot;center&quot;,&quot;textDecoration&quot;:&quot;&quot;,&quot;fontSize&quot;:&quot;large&quot;}},&quot;iconColor&quot;:&quot;#73A2D0&quot;,&quot;iconSize&quot;:&quot;medium&quot;,&quot;ariaLabel&quot;:&quot;Chiudi&quot;},&quot;type&quot;:&quot;element&quot;,&quot;styleObject&quot;:{&quot;sizeClass&quot;:&quot;slds-size_6-of-12 &quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;6&quot;},&quot;padding&quot;:[{&quot;type&quot;:&quot;top&quot;,&quot;size&quot;:&quot;x-small&quot;,&quot;label&quot;:&quot;top:x-small&quot;}],&quot;margin&quot;:[],&quot;background&quot;:{&quot;color&quot;:&quot;#EFF3F4&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;space&quot;,&quot;position&quot;:&quot;&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:&quot;&quot;,&quot;width&quot;:&quot;&quot;,&quot;color&quot;:&quot;#6686DA&quot;,&quot;radius&quot;:&quot;&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{&quot;styles&quot;:{&quot;label&quot;:{&quot;color&quot;:&quot;#EAE6E6&quot;,&quot;textAlign&quot;:&quot;center&quot;,&quot;textDecoration&quot;:&quot;&quot;,&quot;fontSize&quot;:&quot;large&quot;}},&quot;iconColor&quot;:&quot;#73A2D0&quot;,&quot;iconSize&quot;:&quot;medium&quot;},&quot;text&quot;:{&quot;align&quot;:&quot;center&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;&quot;,&quot;class&quot;:&quot;slds-theme_shade slds-text-align_center slds-p-top_x-small &quot;,&quot;style&quot;:&quot;background-color:#EFF3F4;  background-repeat:space;    \n     height:45px;    &quot;,&quot;theme&quot;:&quot;theme_shade&quot;,&quot;height&quot;:&quot;45px&quot;,&quot;minHeight&quot;:&quot;&quot;,&quot;maxHeight&quot;:&quot;&quot;},&quot;elementLabel&quot;:&quot;Action-5&quot;,&quot;styleObjects&quot;:[{&quot;key&quot;:0,&quot;conditions&quot;:&quot;default&quot;,&quot;styleObject&quot;:{&quot;sizeClass&quot;:&quot;slds-size_6-of-12 &quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;6&quot;},&quot;padding&quot;:[{&quot;type&quot;:&quot;top&quot;,&quot;size&quot;:&quot;x-small&quot;,&quot;label&quot;:&quot;top:x-small&quot;}],&quot;margin&quot;:[],&quot;background&quot;:{&quot;color&quot;:&quot;#EFF3F4&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;space&quot;,&quot;position&quot;:&quot;&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:&quot;&quot;,&quot;width&quot;:&quot;&quot;,&quot;color&quot;:&quot;#6686DA&quot;,&quot;radius&quot;:&quot;&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{&quot;styles&quot;:{&quot;label&quot;:{&quot;color&quot;:&quot;#EAE6E6&quot;,&quot;textAlign&quot;:&quot;center&quot;,&quot;textDecoration&quot;:&quot;&quot;,&quot;fontSize&quot;:&quot;large&quot;}},&quot;iconColor&quot;:&quot;#73A2D0&quot;,&quot;iconSize&quot;:&quot;medium&quot;},&quot;text&quot;:{&quot;align&quot;:&quot;center&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;&quot;,&quot;class&quot;:&quot;slds-theme_shade slds-text-align_center slds-p-top_x-small &quot;,&quot;style&quot;:&quot;background-color:#EFF3F4;  background-repeat:space;    \n     height:45px;    &quot;,&quot;theme&quot;:&quot;theme_shade&quot;,&quot;height&quot;:&quot;45px&quot;,&quot;minHeight&quot;:&quot;&quot;,&quot;maxHeight&quot;:&quot;&quot;},&quot;label&quot;:&quot;Default&quot;,&quot;name&quot;:&quot;Default&quot;,&quot;conditionString&quot;:&quot;&quot;,&quot;draggable&quot;:false}]},{&quot;name&quot;:&quot;Action&quot;,&quot;element&quot;:&quot;action&quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;6&quot;},&quot;stateIndex&quot;:0,&quot;class&quot;:&quot;slds-col &quot;,&quot;property&quot;:{&quot;label&quot;:&quot;Gestione Nuclei&quot;,&quot;iconName&quot;:&quot;utility:down&quot;,&quot;record&quot;:&quot;{record}&quot;,&quot;card&quot;:&quot;{card}&quot;,&quot;stateObj&quot;:&quot;{record}&quot;,&quot;actionList&quot;:[{&quot;stateAction&quot;:{&quot;id&quot;:&quot;flex-action-*************&quot;,&quot;type&quot;:&quot;Custom&quot;,&quot;openUrlIn&quot;:&quot;New Tab/Window&quot;,&quot;hasExtraParams&quot;:false,&quot;targetType&quot;:&quot;Web Page&quot;,&quot;Web Page&quot;:{&quot;targetName&quot;:&quot;/flow/FEIQuickActionByAccount?FEIID=ANLE.RICERCA.SOGGETT.ANAG&amp;recordId={recordId}&amp;society=SOC_4&quot;}},&quot;key&quot;:&quot;*************-gmbaaelec&quot;,&quot;label&quot;:&quot;Action&quot;,&quot;draggable&quot;:false,&quot;isOpen&quot;:true,&quot;actionIndex&quot;:0,&quot;reRenderFlyout&quot;:true,&quot;isTrackingDisabled&quot;:false}],&quot;showSpinner&quot;:&quot;true&quot;,&quot;flyoutDetails&quot;:{},&quot;displayAsButton&quot;:true,&quot;buttonVariant&quot;:&quot;brand&quot;,&quot;hideActionIcon&quot;:true,&quot;flyoutChannel&quot;:&quot;close&quot;,&quot;disabled&quot;:&quot;&quot;,&quot;reRenderFlyout&quot;:true,&quot;iconOnly&quot;:false,&quot;styles&quot;:{&quot;label&quot;:{&quot;color&quot;:&quot;#EAE6E6&quot;,&quot;textAlign&quot;:&quot;center&quot;,&quot;textDecoration&quot;:&quot;&quot;,&quot;fontSize&quot;:&quot;large&quot;}},&quot;iconColor&quot;:&quot;#73A2D0&quot;,&quot;iconSize&quot;:&quot;medium&quot;,&quot;ariaLabel&quot;:&quot;Chiudi&quot;},&quot;type&quot;:&quot;element&quot;,&quot;styleObject&quot;:{&quot;sizeClass&quot;:&quot;slds-size_6-of-12 &quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;6&quot;},&quot;padding&quot;:[{&quot;type&quot;:&quot;top&quot;,&quot;size&quot;:&quot;x-small&quot;,&quot;label&quot;:&quot;top:x-small&quot;}],&quot;margin&quot;:[],&quot;background&quot;:{&quot;color&quot;:&quot;#EFF3F4&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;space&quot;,&quot;position&quot;:&quot;&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:&quot;&quot;,&quot;width&quot;:&quot;&quot;,&quot;color&quot;:&quot;#6686DA&quot;,&quot;radius&quot;:&quot;&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{&quot;styles&quot;:{&quot;label&quot;:{&quot;color&quot;:&quot;#EAE6E6&quot;,&quot;textAlign&quot;:&quot;center&quot;,&quot;textDecoration&quot;:&quot;&quot;,&quot;fontSize&quot;:&quot;large&quot;}},&quot;iconColor&quot;:&quot;#73A2D0&quot;,&quot;iconSize&quot;:&quot;medium&quot;},&quot;text&quot;:{&quot;align&quot;:&quot;center&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;&quot;,&quot;class&quot;:&quot;slds-theme_shade slds-text-align_center slds-p-top_x-small &quot;,&quot;style&quot;:&quot;background-color:#EFF3F4;  background-repeat:space;    \n     height:45px;    &quot;,&quot;theme&quot;:&quot;theme_shade&quot;,&quot;height&quot;:&quot;45px&quot;,&quot;minHeight&quot;:&quot;&quot;,&quot;maxHeight&quot;:&quot;&quot;},&quot;elementLabel&quot;:&quot;Action-4-clone-0&quot;,&quot;styleObjects&quot;:[{&quot;key&quot;:0,&quot;conditions&quot;:&quot;default&quot;,&quot;styleObject&quot;:{&quot;sizeClass&quot;:&quot;slds-size_6-of-12 &quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;6&quot;},&quot;padding&quot;:[{&quot;type&quot;:&quot;top&quot;,&quot;size&quot;:&quot;x-small&quot;,&quot;label&quot;:&quot;top:x-small&quot;}],&quot;margin&quot;:[],&quot;background&quot;:{&quot;color&quot;:&quot;#EFF3F4&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;space&quot;,&quot;position&quot;:&quot;&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:&quot;&quot;,&quot;width&quot;:&quot;&quot;,&quot;color&quot;:&quot;#6686DA&quot;,&quot;radius&quot;:&quot;&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{&quot;styles&quot;:{&quot;label&quot;:{&quot;color&quot;:&quot;#EAE6E6&quot;,&quot;textAlign&quot;:&quot;center&quot;,&quot;textDecoration&quot;:&quot;&quot;,&quot;fontSize&quot;:&quot;large&quot;}},&quot;iconColor&quot;:&quot;#73A2D0&quot;,&quot;iconSize&quot;:&quot;medium&quot;},&quot;text&quot;:{&quot;align&quot;:&quot;center&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;&quot;,&quot;class&quot;:&quot;slds-theme_shade slds-text-align_center slds-p-top_x-small &quot;,&quot;style&quot;:&quot;background-color:#EFF3F4;  background-repeat:space;    \n     height:45px;    &quot;,&quot;theme&quot;:&quot;theme_shade&quot;,&quot;height&quot;:&quot;45px&quot;,&quot;minHeight&quot;:&quot;&quot;,&quot;maxHeight&quot;:&quot;&quot;},&quot;label&quot;:&quot;Default&quot;,&quot;name&quot;:&quot;Default&quot;,&quot;conditionString&quot;:&quot;&quot;,&quot;draggable&quot;:false}]}]}},&quot;childCards&quot;:[],&quot;actions&quot;:[],&quot;omniscripts&quot;:[],&quot;documents&quot;:[],&quot;blankCardState&quot;:true}],&quot;dataSource&quot;:{&quot;type&quot;:&quot;Custom&quot;,&quot;value&quot;:{&quot;dsDelay&quot;:&quot;&quot;,&quot;body&quot;:&quot; [\n    {\n      \&quot;birthDate\&quot;: ************,\n      \&quot;personType\&quot;: \&quot;P\&quot;,\n      \&quot;id\&quot;: \&quot;***************\&quot;,\n      \&quot;name\&quot;: \&quot;OLIVIERI MARINA\&quot;,\n      \&quot;groupingId\&quot;: \&quot;****************\&quot;,\n      \&quot;phone\&quot;: \&quot;*********\&quot;,\n      \&quot;cf\&quot;: \&quot;****************\&quot;,\n      \&quot;role\&quot;: \&quot;FAMIGLIARE\&quot;,\n      \&quot;address\&quot;: \&quot;VIA MARCONI 1 GRONTARDO CR\&quot;\n    },\n    {\n      \&quot;birthDate\&quot;: ***********,\n      \&quot;personType\&quot;: \&quot;P\&quot;,\n      \&quot;id\&quot;: \&quot;****************\&quot;,\n      \&quot;name\&quot;: \&quot;BONI CORRADO\&quot;,\n      \&quot;groupingId\&quot;: \&quot;****************\&quot;,\n      \&quot;phone\&quot;: \&quot;*********\&quot;,\n      \&quot;cf\&quot;: \&quot;****************\&quot;,\n      \&quot;role\&quot;: \&quot;CAPOFAMIGLIA\&quot;,\n      \&quot;address\&quot;: \&quot;VIA DELLA CONCILIAZIONE 1 ROMA RM\&quot;\n    },\n    {\n      \&quot;personType\&quot;: \&quot;P\&quot;,\n      \&quot;id\&quot;: \&quot;7741288392777695\&quot;,\n      \&quot;name\&quot;: \&quot;BONI CORRADO,OLIVIERI MARINA \&quot;,\n      \&quot;groupingId\&quot;: \&quot;****************\&quot;,\n      \&quot;role\&quot;: \&quot;COINTESTAZIONE\&quot;,\n      \&quot;address\&quot;: \&quot;V. MARCONI 1/C GRONTARDO CR\&quot;\n    },\n    {\n      \&quot;birthDate\&quot;: 1333404000000,\n      \&quot;personType\&quot;: \&quot;P\&quot;,\n      \&quot;id\&quot;: \&quot;327437879682948101\&quot;,\n      \&quot;name\&quot;: \&quot;BONI ALMA\&quot;,\n      \&quot;groupingId\&quot;: \&quot;****************\&quot;,\n      \&quot;phone\&quot;: \&quot;0372801601\&quot;,\n      \&quot;cf\&quot;: \&quot;****************\&quot;,\n      \&quot;role\&quot;: \&quot;FAMIGLIARE\&quot;,\n      \&quot;address\&quot;: \&quot;VIA MARCONI 1/C GRONTARDO CR\&quot;\n    },\n    {\n      \&quot;birthDate\&quot;: 1393714800000,\n      \&quot;personType\&quot;: \&quot;P\&quot;,\n      \&quot;id\&quot;: \&quot;965639454047518202\&quot;,\n      \&quot;name\&quot;: \&quot;BONI AGATA\&quot;,\n      \&quot;groupingId\&quot;: \&quot;****************\&quot;,\n      \&quot;cf\&quot;: \&quot;****************\&quot;,\n      \&quot;role\&quot;: \&quot;FAMIGLIARE\&quot;,\n      \&quot;address\&quot;: \&quot;VIA MARCONI 1/C GRONTARDO CR\&quot;\n    },\n    {\n      \&quot;birthDate\&quot;: 1208988000000,\n      \&quot;personType\&quot;: \&quot;P\&quot;,\n      \&quot;id\&quot;: \&quot;233239454937800101\&quot;,\n      \&quot;name\&quot;: \&quot;BONI ENEA\&quot;,\n      \&quot;groupingId\&quot;: \&quot;****************\&quot;,\n      \&quot;phone\&quot;: \&quot;3919113954\&quot;,\n      \&quot;cf\&quot;: \&quot;****************\&quot;,\n      \&quot;role\&quot;: \&quot;FAMIGLIARE\&quot;,\n      \&quot;address\&quot;: \&quot;VIA MATTIA BATTISTINI 176 ROMA RM\&quot;\n    }\n  ]&quot;,&quot;resultVar&quot;:&quot;&quot;},&quot;orderBy&quot;:{&quot;name&quot;:&quot;&quot;,&quot;isReverse&quot;:&quot;&quot;},&quot;contextVariables&quot;:[{&quot;name&quot;:&quot;cf&quot;,&quot;val&quot;:&quot;****************&quot;,&quot;id&quot;:3}]},&quot;title&quot;:&quot;AppartenenzaNucleoModale&quot;,&quot;enableLwc&quot;:true,&quot;isFlex&quot;:true,&quot;theme&quot;:&quot;slds&quot;,&quot;selectableMode&quot;:&quot;Multi&quot;,&quot;lwc&quot;:{&quot;DeveloperName&quot;:&quot;cfAppartenenzaNucleoModale_7_Unipolsai&quot;,&quot;Id&quot;:&quot;0Rb9V000003Xf7VSAS&quot;,&quot;MasterLabel&quot;:&quot;cfAppartenenzaNucleoModale_7_Unipolsai&quot;,&quot;NamespacePrefix&quot;:&quot;c&quot;,&quot;ManageableState&quot;:&quot;unmanaged&quot;},&quot;multilanguageSupport&quot;:true,&quot;jsonEscapeSupport&quot;:true,&quot;xmlObject&quot;:{&quot;masterLabel&quot;:&quot;AppartenenzaNucleoModale&quot;,&quot;isExposed&quot;:true,&quot;apiVersion&quot;:56},&quot;isRepeatable&quot;:false,&quot;events&quot;:[{&quot;eventname&quot;:&quot;rowclick&quot;,&quot;channelname&quot;:&quot;AppartenenzaNucleoModale&quot;,&quot;element&quot;:&quot;action&quot;,&quot;eventtype&quot;:&quot;event&quot;,&quot;recordIndex&quot;:&quot;0&quot;,&quot;actionList&quot;:[{&quot;key&quot;:&quot;*************-w89bqlv4b&quot;,&quot;label&quot;:&quot;Action&quot;,&quot;draggable&quot;:false,&quot;isOpen&quot;:true,&quot;card&quot;:&quot;{card}&quot;,&quot;stateAction&quot;:{&quot;id&quot;:&quot;flex-action-*************&quot;,&quot;type&quot;:&quot;Custom&quot;,&quot;displayName&quot;:&quot;Action&quot;,&quot;vlocityIcon&quot;:&quot;standard-default&quot;,&quot;openUrlIn&quot;:&quot;Current Window&quot;,&quot;targetType&quot;:&quot;Record&quot;,&quot;Record&quot;:{&quot;targetName&quot;:&quot;Account&quot;,&quot;targetAction&quot;:&quot;view&quot;,&quot;targetId&quot;:&quot;{action.result.AccountId}&quot;}},&quot;actionIndex&quot;:0}],&quot;showSpinner&quot;:&quot;false&quot;,&quot;key&quot;:&quot;event-0&quot;,&quot;displayLabel&quot;:&quot;rowclick&quot;,&quot;eventLabel&quot;:&quot;custom event&quot;}],&quot;sessionVars&quot;:[]}</propertySetConfig>
    <sampleDataSourceResponse>[{&quot;birthDate&quot;:************,&quot;personType&quot;:&quot;P&quot;,&quot;id&quot;:&quot;***************&quot;,&quot;name&quot;:&quot;OLIVIERI MARINA&quot;,&quot;groupingId&quot;:&quot;****************&quot;,&quot;phone&quot;:&quot;*********&quot;,&quot;cf&quot;:&quot;****************&quot;,&quot;role&quot;:&quot;FAMIGLIARE&quot;,&quot;address&quot;:&quot;VIA MARCONI 1 GRONTARDO CR&quot;},{&quot;birthDate&quot;:***********,&quot;personType&quot;:&quot;P&quot;,&quot;id&quot;:&quot;****************&quot;,&quot;name&quot;:&quot;BONI CORRADO&quot;,&quot;groupingId&quot;:&quot;****************&quot;,&quot;phone&quot;:&quot;*********&quot;,&quot;cf&quot;:&quot;****************&quot;,&quot;role&quot;:&quot;CAPOFAMIGLIA&quot;,&quot;address&quot;:&quot;VIA DELLA CONCILIAZIONE 1 ROMA RM&quot;},{&quot;personType&quot;:&quot;P&quot;,&quot;id&quot;:&quot;7741288392777695&quot;,&quot;name&quot;:&quot;BONI CORRADO,OLIVIERI MARINA &quot;,&quot;groupingId&quot;:&quot;****************&quot;,&quot;role&quot;:&quot;COINTESTAZIONE&quot;,&quot;address&quot;:&quot;V. MARCONI 1/C GRONTARDO CR&quot;},{&quot;birthDate&quot;:1333404000000,&quot;personType&quot;:&quot;P&quot;,&quot;id&quot;:&quot;327437879682948101&quot;,&quot;name&quot;:&quot;BONI ALMA&quot;,&quot;groupingId&quot;:&quot;****************&quot;,&quot;phone&quot;:&quot;0372801601&quot;,&quot;cf&quot;:&quot;****************&quot;,&quot;role&quot;:&quot;FAMIGLIARE&quot;,&quot;address&quot;:&quot;VIA MARCONI 1/C GRONTARDO CR&quot;},{&quot;birthDate&quot;:1393714800000,&quot;personType&quot;:&quot;P&quot;,&quot;id&quot;:&quot;965639454047518202&quot;,&quot;name&quot;:&quot;BONI AGATA&quot;,&quot;groupingId&quot;:&quot;****************&quot;,&quot;cf&quot;:&quot;****************&quot;,&quot;role&quot;:&quot;FAMIGLIARE&quot;,&quot;address&quot;:&quot;VIA MARCONI 1/C GRONTARDO CR&quot;},{&quot;birthDate&quot;:1208988000000,&quot;personType&quot;:&quot;P&quot;,&quot;id&quot;:&quot;233239454937800101&quot;,&quot;name&quot;:&quot;BONI ENEA&quot;,&quot;groupingId&quot;:&quot;****************&quot;,&quot;phone&quot;:&quot;3919113954&quot;,&quot;cf&quot;:&quot;****************&quot;,&quot;role&quot;:&quot;FAMIGLIARE&quot;,&quot;address&quot;:&quot;VIA MATTIA BATTISTINI 176 ROMA RM&quot;}]</sampleDataSourceResponse>
    <versionNumber>7</versionNumber>
</OmniUiCard>
