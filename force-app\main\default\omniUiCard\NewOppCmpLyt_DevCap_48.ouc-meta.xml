<?xml version="1.0" encoding="UTF-8"?>
<OmniUiCard xmlns="http://soap.sforce.com/2006/04/metadata">
    <authorName>DevCap</authorName>
    <dataSourceConfig>{&quot;dataSource&quot;:{&quot;type&quot;:&quot;IntegrationProcedures&quot;,&quot;value&quot;:{&quot;dsDelay&quot;:&quot;&quot;,&quot;ipMethod&quot;:&quot;LeadManagement_GetAccountOppData&quot;,&quot;vlocityAsync&quot;:false,&quot;inputMap&quot;:{&quot;recordId&quot;:&quot;{recordId}&quot;},&quot;jsonMap&quot;:&quot;{\&quot;recordId\&quot;:\&quot;{recordId}\&quot;,\&quot;accountId\&quot;:\&quot;{accountId}\&quot;}&quot;,&quot;resultVar&quot;:&quot;&quot;},&quot;orderBy&quot;:{&quot;name&quot;:&quot;&quot;,&quot;isReverse&quot;:&quot;&quot;},&quot;contextVariables&quot;:[{&quot;name&quot;:&quot;recordId&quot;,&quot;val&quot;:&quot;0069O00000UL5TFQA1&quot;,&quot;id&quot;:2},{&quot;name&quot;:&quot;accountId&quot;,&quot;val&quot;:&quot;0019X00001CrPH2QAN&quot;,&quot;id&quot;:14}]}}</dataSourceConfig>
    <description>Rimosso TEMP filtro su Accorpa</description>
    <isActive>true</isActive>
    <isManagedUsingStdDesigner>false</isManagedUsingStdDesigner>
    <name>NewOppCmpLyt</name>
    <omniUiCardType>Parent</omniUiCardType>
    <propertySetConfig>{&quot;states&quot;:[{&quot;fields&quot;:[],&quot;conditions&quot;:{&quot;id&quot;:&quot;state-condition-object&quot;,&quot;isParent&quot;:true,&quot;group&quot;:[]},&quot;definedActions&quot;:{&quot;actions&quot;:[]},&quot;name&quot;:&quot;Active&quot;,&quot;isSmartAction&quot;:false,&quot;smartAction&quot;:{},&quot;styleObject&quot;:{&quot;padding&quot;:[{&quot;type&quot;:&quot;around&quot;,&quot;size&quot;:&quot;x-small&quot;,&quot;label&quot;:&quot;around:x-small&quot;}],&quot;margin&quot;:[{&quot;type&quot;:&quot;bottom&quot;,&quot;size&quot;:&quot;x-small&quot;,&quot;label&quot;:&quot;bottom:x-small&quot;}],&quot;container&quot;:{&quot;class&quot;:&quot;slds-card&quot;},&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;12&quot;},&quot;sizeClass&quot;:&quot;slds-size_12-of-12 &quot;,&quot;class&quot;:&quot;slds-card slds-p-around_x-small slds-m-bottom_x-small &quot;,&quot;background&quot;:{&quot;color&quot;:&quot;&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:&quot;&quot;,&quot;width&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;,&quot;radius&quot;:&quot;&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{},&quot;text&quot;:{&quot;align&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;&quot;,&quot;customClass&quot;:&quot;&quot;,&quot;style&quot;:&quot;      \n         &quot;},&quot;components&quot;:{&quot;layer-0&quot;:{&quot;children&quot;:[{&quot;name&quot;:&quot;Block&quot;,&quot;element&quot;:&quot;block&quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;12&quot;},&quot;stateIndex&quot;:0,&quot;class&quot;:&quot;slds-col &quot;,&quot;property&quot;:{&quot;label&quot;:&quot;Block&quot;,&quot;collapsible&quot;:false,&quot;record&quot;:&quot;{record}&quot;,&quot;collapsedByDefault&quot;:false,&quot;card&quot;:&quot;{card}&quot;},&quot;type&quot;:&quot;block&quot;,&quot;styleObject&quot;:{&quot;padding&quot;:[{&quot;type&quot;:&quot;around&quot;,&quot;size&quot;:&quot;x-small&quot;,&quot;label&quot;:&quot;around:x-small&quot;}],&quot;class&quot;:&quot;slds-p-around_x-small &quot;,&quot;sizeClass&quot;:&quot;slds-size_12-of-12 &quot;,&quot;margin&quot;:[],&quot;background&quot;:{&quot;color&quot;:&quot;#F7F7F7&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;12&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:&quot;&quot;,&quot;width&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;,&quot;radius&quot;:&quot;&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{},&quot;text&quot;:{&quot;align&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;&quot;,&quot;style&quot;:&quot;background-color:#F7F7F7;      \n         &quot;},&quot;children&quot;:[{&quot;name&quot;:&quot;Block&quot;,&quot;element&quot;:&quot;block&quot;,&quot;size&quot;:{&quot;isResponsive&quot;:true,&quot;default&quot;:&quot;12&quot;,&quot;large&quot;:&quot;12&quot;,&quot;medium&quot;:&quot;12&quot;,&quot;small&quot;:&quot;12&quot;},&quot;stateIndex&quot;:0,&quot;class&quot;:&quot;slds-col &quot;,&quot;property&quot;:{&quot;label&quot;:&quot;Azioni&quot;,&quot;collapsible&quot;:false,&quot;record&quot;:&quot;{record}&quot;,&quot;collapsedByDefault&quot;:false,&quot;card&quot;:&quot;{card}&quot;,&quot;data-conditions&quot;:{&quot;id&quot;:&quot;state-condition-object&quot;,&quot;isParent&quot;:true,&quot;group&quot;:[]}},&quot;type&quot;:&quot;block&quot;,&quot;styleObject&quot;:{&quot;size&quot;:{&quot;isResponsive&quot;:true,&quot;default&quot;:&quot;12&quot;,&quot;large&quot;:&quot;12&quot;,&quot;medium&quot;:&quot;12&quot;,&quot;small&quot;:&quot;12&quot;},&quot;padding&quot;:[{&quot;type&quot;:&quot;around&quot;,&quot;size&quot;:&quot;x-small&quot;,&quot;label&quot;:&quot;around:x-small&quot;}],&quot;class&quot;:&quot;slds-p-around_x-small &quot;,&quot;sizeClass&quot;:&quot;slds-large-size_12-of-12 slds-medium-size_12-of-12 slds-small-size_12-of-12 slds-size_12-of-12 &quot;,&quot;margin&quot;:[],&quot;background&quot;:{&quot;color&quot;:&quot;&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:&quot;&quot;,&quot;width&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;,&quot;radius&quot;:&quot;&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{},&quot;text&quot;:{&quot;align&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;&quot;,&quot;style&quot;:&quot;      \n         &quot;},&quot;children&quot;:[{&quot;name&quot;:&quot;Block&quot;,&quot;element&quot;:&quot;block&quot;,&quot;size&quot;:{&quot;isResponsive&quot;:true,&quot;default&quot;:&quot;1&quot;,&quot;large&quot;:&quot;1&quot;,&quot;medium&quot;:&quot;1&quot;,&quot;small&quot;:&quot;1&quot;},&quot;stateIndex&quot;:0,&quot;class&quot;:&quot;slds-col &quot;,&quot;property&quot;:{&quot;label&quot;:&quot;Block&quot;,&quot;collapsible&quot;:false,&quot;record&quot;:&quot;{record}&quot;,&quot;collapsedByDefault&quot;:false,&quot;card&quot;:&quot;{card}&quot;},&quot;type&quot;:&quot;block&quot;,&quot;styleObject&quot;:{&quot;padding&quot;:[{&quot;type&quot;:&quot;around&quot;,&quot;size&quot;:&quot;x-small&quot;,&quot;label&quot;:&quot;around:x-small&quot;}],&quot;class&quot;:&quot;slds-p-around_x-small &quot;,&quot;sizeClass&quot;:&quot;slds-large-size_1-of-12 slds-medium-size_1-of-12 slds-small-size_1-of-12 slds-size_1-of-12 &quot;,&quot;size&quot;:{&quot;isResponsive&quot;:true,&quot;default&quot;:&quot;1&quot;,&quot;large&quot;:&quot;1&quot;,&quot;medium&quot;:&quot;1&quot;,&quot;small&quot;:&quot;1&quot;},&quot;margin&quot;:[],&quot;background&quot;:{&quot;color&quot;:&quot;&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:&quot;&quot;,&quot;width&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;,&quot;radius&quot;:&quot;&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{},&quot;text&quot;:{&quot;align&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;&quot;,&quot;style&quot;:&quot;      \n         &quot;},&quot;children&quot;:[{&quot;name&quot;:&quot;Icon&quot;,&quot;element&quot;:&quot;flexIcon&quot;,&quot;size&quot;:{&quot;isResponsive&quot;:true,&quot;default&quot;:&quot;12&quot;,&quot;large&quot;:&quot;12&quot;,&quot;medium&quot;:&quot;12&quot;,&quot;small&quot;:&quot;12&quot;},&quot;stateIndex&quot;:0,&quot;class&quot;:&quot;slds-col &quot;,&quot;property&quot;:{&quot;record&quot;:&quot;{record}&quot;,&quot;card&quot;:&quot;{card}&quot;,&quot;iconType&quot;:&quot;Salesforce SVG&quot;,&quot;iconName&quot;:&quot;standard:opportunity&quot;,&quot;size&quot;:&quot;large&quot;,&quot;extraclass&quot;:&quot;slds-icon_container slds-icon-standard-opportunity&quot;,&quot;variant&quot;:&quot;inverse&quot;,&quot;imgsrc&quot;:&quot;&quot;},&quot;type&quot;:&quot;element&quot;,&quot;styleObject&quot;:{&quot;sizeClass&quot;:&quot;slds-large-size_12-of-12 slds-medium-size_12-of-12 slds-small-size_12-of-12 slds-size_12-of-12 &quot;,&quot;padding&quot;:[],&quot;margin&quot;:[],&quot;background&quot;:{&quot;color&quot;:&quot;&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;size&quot;:{&quot;isResponsive&quot;:true,&quot;default&quot;:&quot;12&quot;,&quot;large&quot;:&quot;12&quot;,&quot;medium&quot;:&quot;12&quot;,&quot;small&quot;:&quot;12&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:&quot;&quot;,&quot;width&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;,&quot;radius&quot;:&quot;&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{},&quot;text&quot;:{&quot;align&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;&quot;,&quot;class&quot;:&quot;&quot;,&quot;style&quot;:&quot;      \n         &quot;},&quot;elementLabel&quot;:&quot;Block-0-Block-0-Icon-0&quot;,&quot;styleObjects&quot;:[{&quot;key&quot;:0,&quot;conditions&quot;:&quot;default&quot;,&quot;styleObject&quot;:{&quot;sizeClass&quot;:&quot;slds-large-size_12-of-12 slds-medium-size_12-of-12 slds-small-size_12-of-12 slds-size_12-of-12 &quot;,&quot;padding&quot;:[],&quot;margin&quot;:[],&quot;background&quot;:{&quot;color&quot;:&quot;&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;size&quot;:{&quot;isResponsive&quot;:true,&quot;default&quot;:&quot;12&quot;,&quot;large&quot;:&quot;12&quot;,&quot;medium&quot;:&quot;12&quot;,&quot;small&quot;:&quot;12&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:&quot;&quot;,&quot;width&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;,&quot;radius&quot;:&quot;&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{},&quot;text&quot;:{&quot;align&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;&quot;,&quot;class&quot;:&quot;&quot;,&quot;style&quot;:&quot;      \n         &quot;},&quot;label&quot;:&quot;Default&quot;,&quot;name&quot;:&quot;Default&quot;,&quot;conditionString&quot;:&quot;&quot;,&quot;draggable&quot;:false}],&quot;key&quot;:&quot;element_element_element_element_block_0_0_block_0_0_block_0_0_flexIcon_0_0&quot;}],&quot;elementLabel&quot;:&quot;Block-0-Block-2-Block-0&quot;,&quot;styleObjects&quot;:[{&quot;key&quot;:0,&quot;conditions&quot;:&quot;default&quot;,&quot;styleObject&quot;:{&quot;padding&quot;:[{&quot;type&quot;:&quot;around&quot;,&quot;size&quot;:&quot;x-small&quot;,&quot;label&quot;:&quot;around:x-small&quot;}],&quot;class&quot;:&quot;slds-p-around_x-small &quot;,&quot;sizeClass&quot;:&quot;slds-large-size_1-of-12 slds-medium-size_1-of-12 slds-small-size_1-of-12 slds-size_1-of-12 &quot;,&quot;size&quot;:{&quot;isResponsive&quot;:true,&quot;default&quot;:&quot;1&quot;,&quot;large&quot;:&quot;1&quot;,&quot;medium&quot;:&quot;1&quot;,&quot;small&quot;:&quot;1&quot;},&quot;margin&quot;:[],&quot;background&quot;:{&quot;color&quot;:&quot;&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:&quot;&quot;,&quot;width&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;,&quot;radius&quot;:&quot;&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{},&quot;text&quot;:{&quot;align&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;&quot;,&quot;style&quot;:&quot;      \n         &quot;},&quot;label&quot;:&quot;Default&quot;,&quot;name&quot;:&quot;Default&quot;,&quot;conditionString&quot;:&quot;&quot;,&quot;draggable&quot;:false}],&quot;key&quot;:&quot;element_element_element_block_0_0_block_0_0_block_0_0&quot;,&quot;parentElementKey&quot;:&quot;element_element_block_0_0_block_0_0&quot;},{&quot;name&quot;:&quot;Block&quot;,&quot;element&quot;:&quot;block&quot;,&quot;size&quot;:{&quot;isResponsive&quot;:true,&quot;default&quot;:&quot;3&quot;,&quot;large&quot;:&quot;2&quot;,&quot;medium&quot;:&quot;3&quot;,&quot;small&quot;:&quot;3&quot;},&quot;stateIndex&quot;:0,&quot;class&quot;:&quot;slds-col &quot;,&quot;property&quot;:{&quot;label&quot;:&quot;Block&quot;,&quot;collapsible&quot;:false,&quot;record&quot;:&quot;{record}&quot;,&quot;collapsedByDefault&quot;:false,&quot;card&quot;:&quot;{card}&quot;},&quot;type&quot;:&quot;block&quot;,&quot;styleObject&quot;:{&quot;size&quot;:{&quot;isResponsive&quot;:true,&quot;default&quot;:&quot;3&quot;,&quot;large&quot;:&quot;2&quot;,&quot;medium&quot;:&quot;3&quot;,&quot;small&quot;:&quot;3&quot;},&quot;padding&quot;:[{&quot;type&quot;:&quot;around&quot;,&quot;size&quot;:&quot;x-small&quot;,&quot;label&quot;:&quot;around:x-small&quot;}],&quot;class&quot;:&quot;slds-p-around_x-small &quot;,&quot;sizeClass&quot;:&quot;slds-large-size_2-of-12  slds-medium-size_3-of-12  slds-small-size_3-of-12  slds-size_3-of-12 &quot;,&quot;margin&quot;:[],&quot;background&quot;:{&quot;color&quot;:&quot;&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:&quot;&quot;,&quot;width&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;,&quot;radius&quot;:&quot;&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{},&quot;text&quot;:{&quot;align&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;&quot;,&quot;style&quot;:&quot;      \n         &quot;},&quot;children&quot;:[{&quot;name&quot;:&quot;Text&quot;,&quot;element&quot;:&quot;outputField&quot;,&quot;size&quot;:{&quot;isResponsive&quot;:true,&quot;default&quot;:&quot;12&quot;,&quot;large&quot;:&quot;12&quot;,&quot;medium&quot;:&quot;12&quot;,&quot;small&quot;:&quot;12&quot;},&quot;stateIndex&quot;:0,&quot;class&quot;:&quot;slds-col &quot;,&quot;property&quot;:{&quot;record&quot;:&quot;{record}&quot;,&quot;mergeField&quot;:&quot;%3Cdiv%3E%3Cspan%20style=%22font-size:%2010pt;%22%3ETrattativa%3C/span%3E%3C/div%3E&quot;,&quot;card&quot;:&quot;{card}&quot;},&quot;type&quot;:&quot;text&quot;,&quot;styleObject&quot;:{&quot;sizeClass&quot;:&quot;slds-large-size_12-of-12 slds-medium-size_12-of-12 slds-small-size_12-of-12 slds-size_12-of-12 &quot;,&quot;padding&quot;:[],&quot;margin&quot;:[],&quot;background&quot;:{&quot;color&quot;:&quot;&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;size&quot;:{&quot;isResponsive&quot;:true,&quot;default&quot;:&quot;12&quot;,&quot;large&quot;:&quot;12&quot;,&quot;medium&quot;:&quot;12&quot;,&quot;small&quot;:&quot;12&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:&quot;&quot;,&quot;width&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;,&quot;radius&quot;:&quot;&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{},&quot;text&quot;:{&quot;align&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;&quot;,&quot;class&quot;:&quot;&quot;,&quot;style&quot;:&quot;      \n         &quot;},&quot;elementLabel&quot;:&quot;Block-0-Block-1-Text-0&quot;,&quot;styleObjects&quot;:[{&quot;key&quot;:0,&quot;conditions&quot;:&quot;default&quot;,&quot;styleObject&quot;:{&quot;sizeClass&quot;:&quot;slds-large-size_12-of-12 slds-medium-size_12-of-12 slds-small-size_12-of-12 slds-size_12-of-12 &quot;,&quot;padding&quot;:[],&quot;margin&quot;:[],&quot;background&quot;:{&quot;color&quot;:&quot;&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;size&quot;:{&quot;isResponsive&quot;:true,&quot;default&quot;:&quot;12&quot;,&quot;large&quot;:&quot;12&quot;,&quot;medium&quot;:&quot;12&quot;,&quot;small&quot;:&quot;12&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:&quot;&quot;,&quot;width&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;,&quot;radius&quot;:&quot;&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{},&quot;text&quot;:{&quot;align&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;&quot;,&quot;class&quot;:&quot;&quot;,&quot;style&quot;:&quot;      \n         &quot;},&quot;label&quot;:&quot;Default&quot;,&quot;name&quot;:&quot;Default&quot;,&quot;conditionString&quot;:&quot;&quot;,&quot;draggable&quot;:false}],&quot;key&quot;:&quot;element_element_element_element_block_0_0_block_0_0_block_1_0_outputField_0_0&quot;,&quot;parentElementKey&quot;:&quot;element_element_element_block_0_0_block_0_0_block_1_0&quot;},{&quot;name&quot;:&quot;Text&quot;,&quot;element&quot;:&quot;outputField&quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;12&quot;,&quot;large&quot;:&quot;12&quot;,&quot;medium&quot;:&quot;12&quot;,&quot;small&quot;:&quot;12&quot;},&quot;stateIndex&quot;:0,&quot;class&quot;:&quot;slds-col &quot;,&quot;property&quot;:{&quot;record&quot;:&quot;{record}&quot;,&quot;mergeField&quot;:&quot;%3Cdiv%20class=%22slds-text-body_regular%20slds-text-heading_medium%20slds-text-color_default%22%3E%3Cspan%20style=%22font-size:%2014pt;%22%3E%7Bname%7D%3C/span%3E%3C/div%3E&quot;,&quot;card&quot;:&quot;{card}&quot;},&quot;type&quot;:&quot;text&quot;,&quot;styleObject&quot;:{&quot;sizeClass&quot;:&quot;slds-size_12-of-12 &quot;,&quot;padding&quot;:[],&quot;margin&quot;:[],&quot;background&quot;:{&quot;color&quot;:&quot;&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;12&quot;,&quot;large&quot;:&quot;12&quot;,&quot;medium&quot;:&quot;12&quot;,&quot;small&quot;:&quot;12&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:&quot;&quot;,&quot;width&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;,&quot;radius&quot;:&quot;&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{},&quot;text&quot;:{&quot;align&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;font-weight: bold;&quot;,&quot;class&quot;:&quot;&quot;,&quot;style&quot;:&quot;      \n         font-weight: bold;&quot;},&quot;elementLabel&quot;:&quot;Block-0-Block-1-Text-1&quot;,&quot;styleObjects&quot;:[{&quot;key&quot;:0,&quot;conditions&quot;:&quot;default&quot;,&quot;styleObject&quot;:{&quot;sizeClass&quot;:&quot;slds-size_12-of-12 &quot;,&quot;padding&quot;:[],&quot;margin&quot;:[],&quot;background&quot;:{&quot;color&quot;:&quot;&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;12&quot;,&quot;large&quot;:&quot;12&quot;,&quot;medium&quot;:&quot;12&quot;,&quot;small&quot;:&quot;12&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:&quot;&quot;,&quot;width&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;,&quot;radius&quot;:&quot;&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{},&quot;text&quot;:{&quot;align&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;font-weight: bold;&quot;,&quot;class&quot;:&quot;&quot;,&quot;style&quot;:&quot;      \n         font-weight: bold;&quot;},&quot;label&quot;:&quot;Default&quot;,&quot;name&quot;:&quot;Default&quot;,&quot;conditionString&quot;:&quot;&quot;,&quot;draggable&quot;:false}],&quot;key&quot;:&quot;element_element_element_element_block_0_0_block_0_0_block_1_0_outputField_1_0&quot;,&quot;parentElementKey&quot;:&quot;element_element_element_block_0_0_block_0_0_block_1_0&quot;}],&quot;elementLabel&quot;:&quot;Block-0-Block-2-Block-1&quot;,&quot;styleObjects&quot;:[{&quot;key&quot;:0,&quot;conditions&quot;:&quot;default&quot;,&quot;styleObject&quot;:{&quot;size&quot;:{&quot;isResponsive&quot;:true,&quot;default&quot;:&quot;3&quot;,&quot;large&quot;:&quot;2&quot;,&quot;medium&quot;:&quot;3&quot;,&quot;small&quot;:&quot;3&quot;},&quot;padding&quot;:[{&quot;type&quot;:&quot;around&quot;,&quot;size&quot;:&quot;x-small&quot;,&quot;label&quot;:&quot;around:x-small&quot;}],&quot;class&quot;:&quot;slds-p-around_x-small &quot;,&quot;sizeClass&quot;:&quot;slds-large-size_2-of-12  slds-medium-size_3-of-12  slds-small-size_3-of-12  slds-size_3-of-12 &quot;,&quot;margin&quot;:[],&quot;background&quot;:{&quot;color&quot;:&quot;&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:&quot;&quot;,&quot;width&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;,&quot;radius&quot;:&quot;&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{},&quot;text&quot;:{&quot;align&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;&quot;,&quot;style&quot;:&quot;      \n         &quot;},&quot;label&quot;:&quot;Default&quot;,&quot;name&quot;:&quot;Default&quot;,&quot;conditionString&quot;:&quot;&quot;,&quot;draggable&quot;:false}],&quot;key&quot;:&quot;element_element_element_block_0_0_block_0_0_block_1_0&quot;,&quot;parentElementKey&quot;:&quot;element_element_block_0_0_block_0_0&quot;},{&quot;name&quot;:&quot;Block&quot;,&quot;element&quot;:&quot;block&quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;9&quot;},&quot;stateIndex&quot;:0,&quot;class&quot;:&quot;slds-col &quot;,&quot;property&quot;:{&quot;label&quot;:&quot;Block&quot;,&quot;collapsible&quot;:false,&quot;record&quot;:&quot;{record}&quot;,&quot;collapsedByDefault&quot;:false,&quot;card&quot;:&quot;{card}&quot;,&quot;data-conditions&quot;:{&quot;id&quot;:&quot;state-condition-object&quot;,&quot;isParent&quot;:true,&quot;group&quot;:[{&quot;id&quot;:&quot;state-new-condition-48&quot;,&quot;field&quot;:&quot;stageName&quot;,&quot;operator&quot;:&quot;==&quot;,&quot;value&quot;:&quot;In gestione&quot;,&quot;type&quot;:&quot;custom&quot;,&quot;hasMergeField&quot;:false},{&quot;id&quot;:&quot;state-new-group-1&quot;,&quot;group&quot;:[{&quot;id&quot;:&quot;state-new-condition-0&quot;,&quot;field&quot;:&quot;multiDevName&quot;,&quot;operator&quot;:&quot;==&quot;,&quot;value&quot;:&quot;Omnicanale&quot;,&quot;type&quot;:&quot;custom&quot;,&quot;hasMergeField&quot;:false},{&quot;id&quot;:&quot;state-new-condition-67&quot;,&quot;field&quot;:&quot;multiDevName&quot;,&quot;operator&quot;:&quot;==&quot;,&quot;value&quot;:&quot;APP&quot;,&quot;type&quot;:&quot;custom&quot;,&quot;hasMergeField&quot;:false,&quot;logicalOperator&quot;:&quot;||&quot;},{&quot;id&quot;:&quot;state-new-condition-7&quot;,&quot;field&quot;:&quot;multiDevName&quot;,&quot;operator&quot;:&quot;==&quot;,&quot;value&quot;:&quot;Agenziale&quot;,&quot;type&quot;:&quot;custom&quot;,&quot;hasMergeField&quot;:false,&quot;logicalOperator&quot;:&quot;||&quot;}],&quot;logicalOperator&quot;:&quot;&amp;&amp;&quot;},{&quot;id&quot;:&quot;state-new-group-149&quot;,&quot;group&quot;:[{&quot;id&quot;:&quot;state-new-condition-148&quot;,&quot;field&quot;:&quot;assignedTo&quot;,&quot;operator&quot;:&quot;==&quot;,&quot;value&quot;:&quot;true&quot;,&quot;type&quot;:&quot;custom&quot;,&quot;hasMergeField&quot;:false},{&quot;id&quot;:&quot;state-new-condition-208&quot;,&quot;field&quot;:&quot;checkCp.anyPermission&quot;,&quot;operator&quot;:&quot;==&quot;,&quot;value&quot;:&quot;true&quot;,&quot;type&quot;:&quot;custom&quot;,&quot;hasMergeField&quot;:false,&quot;logicalOperator&quot;:&quot;||&quot;}],&quot;logicalOperator&quot;:&quot;&amp;&amp;&quot;}]}},&quot;type&quot;:&quot;block&quot;,&quot;styleObject&quot;:{&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;9&quot;},&quot;padding&quot;:[{&quot;type&quot;:&quot;around&quot;,&quot;size&quot;:&quot;x-small&quot;,&quot;label&quot;:&quot;around:x-small&quot;}],&quot;class&quot;:&quot;slds-text-align_right slds-p-around_x-small &quot;,&quot;sizeClass&quot;:&quot;slds-size_9-of-12 &quot;,&quot;margin&quot;:[],&quot;background&quot;:{&quot;color&quot;:&quot;&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:&quot;&quot;,&quot;width&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;,&quot;radius&quot;:&quot;&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{},&quot;text&quot;:{&quot;align&quot;:&quot;right&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;display: flex; flex-direction: row-reverse;\n&quot;,&quot;style&quot;:&quot;      \n         display: flex; flex-direction: row-reverse;\n&quot;,&quot;customClass&quot;:&quot;&quot;},&quot;children&quot;:[{&quot;name&quot;:&quot;Custom LWC&quot;,&quot;element&quot;:&quot;customLwc&quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;2&quot;,&quot;large&quot;:&quot;11&quot;,&quot;medium&quot;:&quot;11&quot;,&quot;small&quot;:&quot;11&quot;},&quot;stateIndex&quot;:0,&quot;class&quot;:&quot;slds-col &quot;,&quot;property&quot;:{&quot;recordId&quot;:&quot;{recordId}&quot;,&quot;buttonLabel&quot;:&quot;Nuovo appuntamento&quot;,&quot;customlwcname&quot;:&quot;launchNewAppointmentFromOpp&quot;},&quot;type&quot;:&quot;element&quot;,&quot;styleObject&quot;:{&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;2&quot;,&quot;large&quot;:&quot;11&quot;,&quot;medium&quot;:&quot;11&quot;,&quot;small&quot;:&quot;11&quot;},&quot;sizeClass&quot;:&quot;slds-size_2-of-12 &quot;,&quot;padding&quot;:[],&quot;margin&quot;:[],&quot;background&quot;:{&quot;color&quot;:&quot;&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:&quot;&quot;,&quot;width&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;,&quot;radius&quot;:&quot;&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{},&quot;text&quot;:{&quot;align&quot;:&quot;right&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;&quot;,&quot;class&quot;:&quot;slds-text-align_right tight-buttons-container&quot;,&quot;style&quot;:&quot;      \n         &quot;,&quot;customClass&quot;:&quot;tight-buttons-container&quot;},&quot;elementLabel&quot;:&quot;Appuntamento LWC&quot;,&quot;styleObjects&quot;:[{&quot;key&quot;:0,&quot;conditions&quot;:&quot;default&quot;,&quot;styleObject&quot;:{&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;2&quot;,&quot;large&quot;:&quot;11&quot;,&quot;medium&quot;:&quot;11&quot;,&quot;small&quot;:&quot;11&quot;},&quot;sizeClass&quot;:&quot;slds-size_2-of-12 &quot;,&quot;padding&quot;:[],&quot;margin&quot;:[],&quot;background&quot;:{&quot;color&quot;:&quot;&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:&quot;&quot;,&quot;width&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;,&quot;radius&quot;:&quot;&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{},&quot;text&quot;:{&quot;align&quot;:&quot;right&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;&quot;,&quot;class&quot;:&quot;slds-text-align_right tight-buttons-container&quot;,&quot;style&quot;:&quot;      \n         &quot;,&quot;customClass&quot;:&quot;tight-buttons-container&quot;},&quot;label&quot;:&quot;Default&quot;,&quot;name&quot;:&quot;Default&quot;,&quot;conditionString&quot;:&quot;&quot;,&quot;draggable&quot;:false}],&quot;userUpdatedElementLabel&quot;:true,&quot;key&quot;:&quot;element_element_element_element_block_0_0_block_0_0_block_2_0_customLwc_0_0&quot;,&quot;parentElementKey&quot;:&quot;element_element_element_block_0_0_block_0_0_block_2_0&quot;},{&quot;name&quot;:&quot;Custom LWC&quot;,&quot;element&quot;:&quot;customLwc&quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;1&quot;,&quot;large&quot;:&quot;11&quot;,&quot;medium&quot;:&quot;11&quot;,&quot;small&quot;:&quot;11&quot;},&quot;stateIndex&quot;:0,&quot;class&quot;:&quot;slds-col &quot;,&quot;property&quot;:{&quot;recordId&quot;:&quot;{recordId}&quot;,&quot;buttonLabel&quot;:&quot;Ri-assegna&quot;,&quot;flowApiName&quot;:&quot;ST_Reassignment&quot;,&quot;refreshPageOnFinshedFlow&quot;:&quot;true&quot;,&quot;isFlowModal&quot;:&quot;true&quot;,&quot;customlwcname&quot;:&quot;launchFlow&quot;,&quot;data-conditions&quot;:{&quot;id&quot;:&quot;state-condition-object&quot;,&quot;isParent&quot;:true,&quot;group&quot;:[{&quot;id&quot;:&quot;state-new-condition-245&quot;,&quot;field&quot;:&quot;checkCp.customerPermissionCheck.RicezioneAssegnazioneLead&quot;,&quot;operator&quot;:&quot;==&quot;,&quot;value&quot;:&quot;true&quot;,&quot;type&quot;:&quot;custom&quot;,&quot;hasMergeField&quot;:false}]}},&quot;type&quot;:&quot;element&quot;,&quot;styleObject&quot;:{&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;1&quot;,&quot;large&quot;:&quot;11&quot;,&quot;medium&quot;:&quot;11&quot;,&quot;small&quot;:&quot;11&quot;},&quot;sizeClass&quot;:&quot;slds-size_1-of-12 &quot;,&quot;padding&quot;:[],&quot;margin&quot;:[],&quot;background&quot;:{&quot;color&quot;:&quot;&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:&quot;&quot;,&quot;width&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;,&quot;radius&quot;:&quot;&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{},&quot;text&quot;:{&quot;align&quot;:&quot;right&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;&quot;,&quot;class&quot;:&quot;slds-text-align_right tight-buttons-container&quot;,&quot;style&quot;:&quot;      \n         &quot;,&quot;customClass&quot;:&quot;tight-buttons-container&quot;},&quot;elementLabel&quot;:&quot;Riassegna&quot;,&quot;styleObjects&quot;:[{&quot;key&quot;:0,&quot;conditions&quot;:&quot;default&quot;,&quot;styleObject&quot;:{&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;1&quot;,&quot;large&quot;:&quot;11&quot;,&quot;medium&quot;:&quot;11&quot;,&quot;small&quot;:&quot;11&quot;},&quot;sizeClass&quot;:&quot;slds-size_1-of-12 &quot;,&quot;padding&quot;:[],&quot;margin&quot;:[],&quot;background&quot;:{&quot;color&quot;:&quot;&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:&quot;&quot;,&quot;width&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;,&quot;radius&quot;:&quot;&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{},&quot;text&quot;:{&quot;align&quot;:&quot;right&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;&quot;,&quot;class&quot;:&quot;slds-text-align_right tight-buttons-container&quot;,&quot;style&quot;:&quot;      \n         &quot;,&quot;customClass&quot;:&quot;tight-buttons-container&quot;},&quot;label&quot;:&quot;Default&quot;,&quot;name&quot;:&quot;Default&quot;,&quot;conditionString&quot;:&quot;&quot;,&quot;draggable&quot;:false}],&quot;userUpdatedElementLabel&quot;:true,&quot;key&quot;:&quot;element_element_element_element_block_0_0_block_0_0_block_2_0_customLwc_1_0&quot;,&quot;parentElementKey&quot;:&quot;element_element_element_block_0_0_block_0_0_block_2_0&quot;},{&quot;name&quot;:&quot;Custom LWC&quot;,&quot;element&quot;:&quot;customLwc&quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;2&quot;,&quot;large&quot;:&quot;11&quot;,&quot;medium&quot;:&quot;11&quot;,&quot;small&quot;:&quot;11&quot;},&quot;stateIndex&quot;:0,&quot;class&quot;:&quot;slds-col &quot;,&quot;property&quot;:{&quot;recordId&quot;:&quot;{recordId}&quot;,&quot;buttonLabel&quot;:&quot;Ripianifica&quot;,&quot;customlwcname&quot;:&quot;ripianifica&quot;,&quot;data-conditions&quot;:{&quot;id&quot;:&quot;state-condition-object&quot;,&quot;isParent&quot;:true,&quot;group&quot;:[{&quot;id&quot;:&quot;state-new-condition-0&quot;,&quot;field&quot;:&quot;true&quot;,&quot;operator&quot;:&quot;==&quot;,&quot;value&quot;:&quot;false&quot;,&quot;type&quot;:&quot;custom&quot;,&quot;hasMergeField&quot;:false}]}},&quot;type&quot;:&quot;element&quot;,&quot;styleObject&quot;:{&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;2&quot;,&quot;large&quot;:&quot;11&quot;,&quot;medium&quot;:&quot;11&quot;,&quot;small&quot;:&quot;11&quot;},&quot;sizeClass&quot;:&quot;slds-size_2-of-12 &quot;,&quot;padding&quot;:[],&quot;margin&quot;:[],&quot;background&quot;:{&quot;color&quot;:&quot;&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:&quot;&quot;,&quot;width&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;,&quot;radius&quot;:&quot;&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{},&quot;text&quot;:{&quot;align&quot;:&quot;right&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;&quot;,&quot;class&quot;:&quot;slds-text-align_right tight-buttons-container&quot;,&quot;style&quot;:&quot;      \n         &quot;,&quot;customClass&quot;:&quot;tight-buttons-container&quot;},&quot;elementLabel&quot;:&quot;Ripianifica&quot;,&quot;styleObjects&quot;:[{&quot;key&quot;:0,&quot;conditions&quot;:&quot;default&quot;,&quot;styleObject&quot;:{&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;2&quot;,&quot;large&quot;:&quot;11&quot;,&quot;medium&quot;:&quot;11&quot;,&quot;small&quot;:&quot;11&quot;},&quot;sizeClass&quot;:&quot;slds-size_2-of-12 &quot;,&quot;padding&quot;:[],&quot;margin&quot;:[],&quot;background&quot;:{&quot;color&quot;:&quot;&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:&quot;&quot;,&quot;width&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;,&quot;radius&quot;:&quot;&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{},&quot;text&quot;:{&quot;align&quot;:&quot;right&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;&quot;,&quot;class&quot;:&quot;slds-text-align_right tight-buttons-container&quot;,&quot;style&quot;:&quot;      \n         &quot;,&quot;customClass&quot;:&quot;tight-buttons-container&quot;},&quot;label&quot;:&quot;Default&quot;,&quot;name&quot;:&quot;Default&quot;,&quot;conditionString&quot;:&quot;&quot;,&quot;draggable&quot;:false}],&quot;userUpdatedElementLabel&quot;:true,&quot;key&quot;:&quot;element_element_element_element_block_0_0_block_0_0_block_2_0_customLwc_2_0&quot;,&quot;parentElementKey&quot;:&quot;element_element_element_block_0_0_block_0_0_block_2_0&quot;},{&quot;name&quot;:&quot;Action&quot;,&quot;element&quot;:&quot;action&quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;3&quot;},&quot;stateIndex&quot;:0,&quot;class&quot;:&quot;slds-col &quot;,&quot;property&quot;:{&quot;label&quot;:&quot;Nuovo Prodotto&quot;,&quot;iconName&quot;:&quot;standard-default&quot;,&quot;record&quot;:&quot;{record}&quot;,&quot;card&quot;:&quot;{card}&quot;,&quot;stateObj&quot;:&quot;{record}&quot;,&quot;actionList&quot;:[{&quot;stateAction&quot;:{&quot;id&quot;:&quot;flex-action-*************&quot;,&quot;type&quot;:&quot;Flyout&quot;,&quot;openUrlIn&quot;:&quot;New Tab/Window&quot;,&quot;layoutType&quot;:&quot;lightning&quot;,&quot;flyoutType&quot;:&quot;OmniScripts&quot;,&quot;openFlyoutIn&quot;:&quot;Modal&quot;,&quot;channelName&quot;:&quot;&quot;,&quot;osName&quot;:&quot;UniProdotto/Nuovo/Italian&quot;,&quot;flyoutLwc&quot;:&quot;uni-prodotto-nuovo-italian&quot;,&quot;hasExtraParams&quot;:true,&quot;flyoutParams&quot;:{&quot;ContextId&quot;:&quot;{accountId}&quot;}},&quot;key&quot;:&quot;*************-peo4vx1j1&quot;,&quot;label&quot;:&quot;Action&quot;,&quot;draggable&quot;:false,&quot;isOpen&quot;:true,&quot;actionIndex&quot;:0,&quot;contextId&quot;:&quot;\\{accountId}&quot;,&quot;isTrackingDisabled&quot;:true,&quot;preloadFlyout&quot;:false}],&quot;showSpinner&quot;:&quot;false&quot;,&quot;displayAsButton&quot;:true,&quot;hideActionIcon&quot;:true,&quot;buttonVariant&quot;:&quot;neutral&quot;,&quot;flyoutDetails&quot;:{&quot;openFlyoutIn&quot;:&quot;Modal&quot;},&quot;flyoutChannel&quot;:&quot;&quot;,&quot;preloadFlyout&quot;:false},&quot;type&quot;:&quot;element&quot;,&quot;styleObject&quot;:{&quot;sizeClass&quot;:&quot;slds-size_3-of-12 &quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;3&quot;},&quot;padding&quot;:[],&quot;margin&quot;:[],&quot;background&quot;:{&quot;color&quot;:&quot;&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:&quot;&quot;,&quot;width&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;,&quot;radius&quot;:&quot;&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{},&quot;text&quot;:{&quot;align&quot;:&quot;left&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;&quot;,&quot;class&quot;:&quot;slds-text-align_left tight-buttons-container&quot;,&quot;style&quot;:&quot;      \n         &quot;,&quot;customClass&quot;:&quot;tight-buttons-container&quot;},&quot;elementLabel&quot;:&quot;Block-0-Block-2-Block-2-Action-7&quot;,&quot;styleObjects&quot;:[{&quot;key&quot;:0,&quot;conditions&quot;:&quot;default&quot;,&quot;styleObject&quot;:{&quot;sizeClass&quot;:&quot;slds-size_3-of-12 &quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;3&quot;},&quot;padding&quot;:[],&quot;margin&quot;:[],&quot;background&quot;:{&quot;color&quot;:&quot;&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:&quot;&quot;,&quot;width&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;,&quot;radius&quot;:&quot;&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{},&quot;text&quot;:{&quot;align&quot;:&quot;left&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;&quot;,&quot;class&quot;:&quot;slds-text-align_left tight-buttons-container&quot;,&quot;style&quot;:&quot;      \n         &quot;,&quot;customClass&quot;:&quot;tight-buttons-container&quot;},&quot;label&quot;:&quot;Default&quot;,&quot;name&quot;:&quot;Default&quot;,&quot;conditionString&quot;:&quot;&quot;,&quot;draggable&quot;:false}],&quot;key&quot;:&quot;element_element_element_element_block_0_0_block_0_0_block_2_0_action_3_0&quot;,&quot;parentElementKey&quot;:&quot;element_element_element_block_0_0_block_0_0_block_2_0&quot;},{&quot;name&quot;:&quot;Custom LWC&quot;,&quot;element&quot;:&quot;customLwc&quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;2&quot;,&quot;large&quot;:&quot;11&quot;,&quot;medium&quot;:&quot;11&quot;,&quot;small&quot;:&quot;11&quot;},&quot;stateIndex&quot;:0,&quot;class&quot;:&quot;slds-col &quot;,&quot;property&quot;:{&quot;buttonLabel&quot;:&quot;Separa&quot;,&quot;recordId&quot;:&quot;{recordId}&quot;,&quot;refreshPageOnFinshedFlow&quot;:&quot;true&quot;,&quot;isFlowModal&quot;:&quot;true&quot;,&quot;flowApiName&quot;:&quot;ST_Split_Container&quot;,&quot;customlwcname&quot;:&quot;launchFlow&quot;,&quot;data-conditions&quot;:{&quot;id&quot;:&quot;state-condition-object&quot;,&quot;isParent&quot;:true,&quot;group&quot;:[{&quot;id&quot;:&quot;state-new-condition-0&quot;,&quot;field&quot;:&quot;countProductChildOpp&quot;,&quot;operator&quot;:&quot;&gt;&quot;,&quot;value&quot;:&quot;1&quot;,&quot;type&quot;:&quot;custom&quot;,&quot;hasMergeField&quot;:false},{&quot;id&quot;:&quot;state-new-condition-3&quot;,&quot;field&quot;:&quot;stageName&quot;,&quot;operator&quot;:&quot;==&quot;,&quot;value&quot;:&quot;In gestione&quot;,&quot;type&quot;:&quot;custom&quot;,&quot;hasMergeField&quot;:false,&quot;logicalOperator&quot;:&quot;&amp;&amp;&quot;}]}},&quot;type&quot;:&quot;element&quot;,&quot;styleObject&quot;:{&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;2&quot;,&quot;large&quot;:&quot;11&quot;,&quot;medium&quot;:&quot;11&quot;,&quot;small&quot;:&quot;11&quot;},&quot;sizeClass&quot;:&quot;slds-size_2-of-12 &quot;,&quot;padding&quot;:[],&quot;margin&quot;:[],&quot;background&quot;:{&quot;color&quot;:&quot;&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:&quot;&quot;,&quot;width&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;,&quot;radius&quot;:&quot;&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{},&quot;text&quot;:{&quot;align&quot;:&quot;right&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;&quot;,&quot;class&quot;:&quot;slds-text-align_right tight-buttons-container&quot;,&quot;style&quot;:&quot;      \n         &quot;,&quot;customClass&quot;:&quot;tight-buttons-container&quot;},&quot;elementLabel&quot;:&quot;Separa&quot;,&quot;styleObjects&quot;:[{&quot;key&quot;:0,&quot;conditions&quot;:&quot;default&quot;,&quot;styleObject&quot;:{&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;2&quot;,&quot;large&quot;:&quot;11&quot;,&quot;medium&quot;:&quot;11&quot;,&quot;small&quot;:&quot;11&quot;},&quot;sizeClass&quot;:&quot;slds-size_2-of-12 &quot;,&quot;padding&quot;:[],&quot;margin&quot;:[],&quot;background&quot;:{&quot;color&quot;:&quot;&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:&quot;&quot;,&quot;width&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;,&quot;radius&quot;:&quot;&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{},&quot;text&quot;:{&quot;align&quot;:&quot;right&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;&quot;,&quot;class&quot;:&quot;slds-text-align_right tight-buttons-container&quot;,&quot;style&quot;:&quot;      \n         &quot;,&quot;customClass&quot;:&quot;tight-buttons-container&quot;},&quot;label&quot;:&quot;Default&quot;,&quot;name&quot;:&quot;Default&quot;,&quot;conditionString&quot;:&quot;&quot;,&quot;draggable&quot;:false}],&quot;userUpdatedElementLabel&quot;:true,&quot;key&quot;:&quot;element_element_element_element_block_0_0_block_0_0_block_2_0_customLwc_4_0&quot;,&quot;parentElementKey&quot;:&quot;element_element_element_block_0_0_block_0_0_block_2_0&quot;},{&quot;name&quot;:&quot;Custom LWC&quot;,&quot;element&quot;:&quot;customLwc&quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;2&quot;,&quot;large&quot;:&quot;11&quot;,&quot;medium&quot;:&quot;11&quot;,&quot;small&quot;:&quot;11&quot;},&quot;stateIndex&quot;:0,&quot;class&quot;:&quot;slds-col &quot;,&quot;property&quot;:{&quot;buttonLabel&quot;:&quot;Accorpa&quot;,&quot;recordId&quot;:&quot;{recordId}&quot;,&quot;refreshPageOnFinshedFlow&quot;:&quot;true&quot;,&quot;isFlowModal&quot;:&quot;true&quot;,&quot;flowApiName&quot;:&quot;ST_Join_Containers&quot;,&quot;customlwcname&quot;:&quot;launchFlow&quot;,&quot;data-conditions&quot;:{&quot;id&quot;:&quot;state-condition-object&quot;,&quot;isParent&quot;:true,&quot;group&quot;:[{&quot;id&quot;:&quot;state-new-condition-93&quot;,&quot;field&quot;:&quot;isMultiParent&quot;,&quot;operator&quot;:&quot;==&quot;,&quot;value&quot;:&quot;true&quot;,&quot;type&quot;:&quot;custom&quot;,&quot;hasMergeField&quot;:false}]}},&quot;type&quot;:&quot;element&quot;,&quot;styleObject&quot;:{&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;2&quot;,&quot;large&quot;:&quot;11&quot;,&quot;medium&quot;:&quot;11&quot;,&quot;small&quot;:&quot;11&quot;},&quot;sizeClass&quot;:&quot;slds-size_2-of-12 &quot;,&quot;padding&quot;:[],&quot;margin&quot;:[],&quot;background&quot;:{&quot;color&quot;:&quot;&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:&quot;&quot;,&quot;width&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;,&quot;radius&quot;:&quot;&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{},&quot;text&quot;:{&quot;align&quot;:&quot;right&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;&quot;,&quot;class&quot;:&quot;slds-text-align_right tight-buttons-container&quot;,&quot;style&quot;:&quot;      \n         &quot;,&quot;customClass&quot;:&quot;tight-buttons-container&quot;},&quot;elementLabel&quot;:&quot;Accorpa&quot;,&quot;styleObjects&quot;:[{&quot;key&quot;:0,&quot;conditions&quot;:&quot;default&quot;,&quot;styleObject&quot;:{&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;2&quot;,&quot;large&quot;:&quot;11&quot;,&quot;medium&quot;:&quot;11&quot;,&quot;small&quot;:&quot;11&quot;},&quot;sizeClass&quot;:&quot;slds-size_2-of-12 &quot;,&quot;padding&quot;:[],&quot;margin&quot;:[],&quot;background&quot;:{&quot;color&quot;:&quot;&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:&quot;&quot;,&quot;width&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;,&quot;radius&quot;:&quot;&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{},&quot;text&quot;:{&quot;align&quot;:&quot;right&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;&quot;,&quot;class&quot;:&quot;slds-text-align_right tight-buttons-container&quot;,&quot;style&quot;:&quot;      \n         &quot;,&quot;customClass&quot;:&quot;tight-buttons-container&quot;},&quot;label&quot;:&quot;Default&quot;,&quot;name&quot;:&quot;Default&quot;,&quot;conditionString&quot;:&quot;&quot;,&quot;draggable&quot;:false}],&quot;userUpdatedElementLabel&quot;:true,&quot;key&quot;:&quot;element_element_element_element_block_0_0_block_0_0_block_2_0_customLwc_5_0&quot;,&quot;parentElementKey&quot;:&quot;element_element_element_block_0_0_block_0_0_block_2_0&quot;},{&quot;name&quot;:&quot;Custom LWC&quot;,&quot;element&quot;:&quot;customLwc&quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;2&quot;,&quot;large&quot;:&quot;11&quot;,&quot;medium&quot;:&quot;11&quot;,&quot;small&quot;:&quot;11&quot;},&quot;stateIndex&quot;:0,&quot;class&quot;:&quot;slds-col &quot;,&quot;property&quot;:{&quot;buttonLabel&quot;:&quot;Chiudi&quot;,&quot;recordId&quot;:&quot;{recordId}&quot;,&quot;refreshPageOnFinshedFlow&quot;:&quot;true&quot;,&quot;isFlowModal&quot;:&quot;true&quot;,&quot;flowApiName&quot;:&quot;Close_Opportunity_Container&quot;,&quot;customlwcname&quot;:&quot;launchFlow&quot;},&quot;type&quot;:&quot;element&quot;,&quot;styleObject&quot;:{&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;2&quot;,&quot;large&quot;:&quot;11&quot;,&quot;medium&quot;:&quot;11&quot;,&quot;small&quot;:&quot;11&quot;},&quot;sizeClass&quot;:&quot;slds-size_2-of-12 &quot;,&quot;padding&quot;:[],&quot;margin&quot;:[],&quot;background&quot;:{&quot;color&quot;:&quot;&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:&quot;&quot;,&quot;width&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;,&quot;radius&quot;:&quot;&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{},&quot;text&quot;:{&quot;align&quot;:&quot;right&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;&quot;,&quot;class&quot;:&quot;slds-text-align_right tight-buttons-container&quot;,&quot;style&quot;:&quot;      \n         &quot;,&quot;customClass&quot;:&quot;tight-buttons-container&quot;},&quot;elementLabel&quot;:&quot;Chiudi&quot;,&quot;styleObjects&quot;:[{&quot;key&quot;:0,&quot;conditions&quot;:&quot;default&quot;,&quot;styleObject&quot;:{&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;2&quot;,&quot;large&quot;:&quot;11&quot;,&quot;medium&quot;:&quot;11&quot;,&quot;small&quot;:&quot;11&quot;},&quot;sizeClass&quot;:&quot;slds-size_2-of-12 &quot;,&quot;padding&quot;:[],&quot;margin&quot;:[],&quot;background&quot;:{&quot;color&quot;:&quot;&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:&quot;&quot;,&quot;width&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;,&quot;radius&quot;:&quot;&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{},&quot;text&quot;:{&quot;align&quot;:&quot;right&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;&quot;,&quot;class&quot;:&quot;slds-text-align_right tight-buttons-container&quot;,&quot;style&quot;:&quot;      \n         &quot;,&quot;customClass&quot;:&quot;tight-buttons-container&quot;},&quot;label&quot;:&quot;Default&quot;,&quot;name&quot;:&quot;Default&quot;,&quot;conditionString&quot;:&quot;&quot;,&quot;draggable&quot;:false}],&quot;userUpdatedElementLabel&quot;:true,&quot;key&quot;:&quot;element_element_element_element_block_0_0_block_0_0_block_2_0_customLwc_6_0&quot;,&quot;parentElementKey&quot;:&quot;element_element_element_block_0_0_block_0_0_block_2_0&quot;}],&quot;elementLabel&quot;:&quot;Block-0-Block-2-Block-2&quot;,&quot;styleObjects&quot;:[{&quot;key&quot;:0,&quot;conditions&quot;:&quot;default&quot;,&quot;styleObject&quot;:{&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;9&quot;},&quot;padding&quot;:[{&quot;type&quot;:&quot;around&quot;,&quot;size&quot;:&quot;x-small&quot;,&quot;label&quot;:&quot;around:x-small&quot;}],&quot;class&quot;:&quot;slds-text-align_right slds-p-around_x-small &quot;,&quot;sizeClass&quot;:&quot;slds-size_9-of-12 &quot;,&quot;margin&quot;:[],&quot;background&quot;:{&quot;color&quot;:&quot;&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:&quot;&quot;,&quot;width&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;,&quot;radius&quot;:&quot;&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{},&quot;text&quot;:{&quot;align&quot;:&quot;right&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;display: flex; flex-direction: row-reverse;\n&quot;,&quot;style&quot;:&quot;      \n         display: flex; flex-direction: row-reverse;\n&quot;,&quot;customClass&quot;:&quot;&quot;},&quot;label&quot;:&quot;Default&quot;,&quot;name&quot;:&quot;Default&quot;,&quot;conditionString&quot;:&quot;&quot;,&quot;draggable&quot;:false}],&quot;key&quot;:&quot;element_element_element_block_0_0_block_0_0_block_2_0&quot;,&quot;parentElementKey&quot;:&quot;element_element_block_0_0_block_0_0&quot;},{&quot;name&quot;:&quot;Block&quot;,&quot;element&quot;:&quot;block&quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;9&quot;,&quot;large&quot;:&quot;5&quot;,&quot;medium&quot;:&quot;12&quot;,&quot;small&quot;:&quot;12&quot;},&quot;stateIndex&quot;:0,&quot;class&quot;:&quot;slds-col &quot;,&quot;property&quot;:{&quot;label&quot;:&quot;Buttons In Assegnato&quot;,&quot;collapsible&quot;:false,&quot;record&quot;:&quot;{record}&quot;,&quot;collapsedByDefault&quot;:false,&quot;card&quot;:&quot;{card}&quot;,&quot;data-conditions&quot;:{&quot;id&quot;:&quot;state-condition-object&quot;,&quot;isParent&quot;:true,&quot;group&quot;:[{&quot;id&quot;:&quot;state-new-condition-27&quot;,&quot;field&quot;:&quot;stageName&quot;,&quot;operator&quot;:&quot;==&quot;,&quot;value&quot;:&quot;Assegnato&quot;,&quot;type&quot;:&quot;custom&quot;,&quot;hasMergeField&quot;:false},{&quot;id&quot;:&quot;state-new-condition-173&quot;,&quot;field&quot;:&quot;multiDevName&quot;,&quot;operator&quot;:&quot;==&quot;,&quot;value&quot;:&quot;Omnicanale&quot;,&quot;type&quot;:&quot;custom&quot;,&quot;hasMergeField&quot;:false,&quot;logicalOperator&quot;:&quot;&amp;&amp;&quot;},{&quot;id&quot;:&quot;state-new-condition-74&quot;,&quot;field&quot;:&quot;checkCp.anyPermission&quot;,&quot;operator&quot;:&quot;==&quot;,&quot;value&quot;:&quot;true&quot;,&quot;type&quot;:&quot;custom&quot;,&quot;hasMergeField&quot;:false,&quot;logicalOperator&quot;:&quot;&amp;&amp;&quot;}]}},&quot;type&quot;:&quot;block&quot;,&quot;styleObject&quot;:{&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;9&quot;,&quot;large&quot;:&quot;5&quot;,&quot;medium&quot;:&quot;12&quot;,&quot;small&quot;:&quot;12&quot;},&quot;padding&quot;:[{&quot;type&quot;:&quot;around&quot;,&quot;size&quot;:&quot;x-small&quot;,&quot;label&quot;:&quot;around:x-small&quot;}],&quot;class&quot;:&quot;slds-text-align_right slds-p-around_x-small &quot;,&quot;sizeClass&quot;:&quot;slds-size_9-of-12 &quot;,&quot;margin&quot;:[],&quot;background&quot;:{&quot;color&quot;:&quot;&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:&quot;&quot;,&quot;width&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;,&quot;radius&quot;:&quot;&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{},&quot;text&quot;:{&quot;align&quot;:&quot;right&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;display: flex; flex-direction: row-reverse;\n&quot;,&quot;style&quot;:&quot;      \n         display: flex; flex-direction: row-reverse;\n&quot;},&quot;children&quot;:[{&quot;name&quot;:&quot;Custom LWC&quot;,&quot;element&quot;:&quot;customLwc&quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;6&quot;,&quot;large&quot;:&quot;11&quot;,&quot;medium&quot;:&quot;11&quot;,&quot;small&quot;:&quot;11&quot;},&quot;stateIndex&quot;:0,&quot;class&quot;:&quot;slds-col &quot;,&quot;property&quot;:{&quot;recordId&quot;:&quot;{recordId}&quot;,&quot;buttonLabel&quot;:&quot;Prendi in carico&quot;,&quot;flowApiName&quot;:&quot;HandleOpportunity_TakeInCharge&quot;,&quot;refreshPageOnFinshedFlow&quot;:&quot;true&quot;,&quot;customlwcname&quot;:&quot;launchFlow&quot;,&quot;data-conditions&quot;:{&quot;id&quot;:&quot;state-condition-object&quot;,&quot;isParent&quot;:true,&quot;group&quot;:[]}},&quot;type&quot;:&quot;element&quot;,&quot;styleObject&quot;:{&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;6&quot;,&quot;large&quot;:&quot;11&quot;,&quot;medium&quot;:&quot;11&quot;,&quot;small&quot;:&quot;11&quot;},&quot;sizeClass&quot;:&quot;slds-size_6-of-12 &quot;,&quot;padding&quot;:[],&quot;margin&quot;:[],&quot;background&quot;:{&quot;color&quot;:&quot;&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:&quot;&quot;,&quot;width&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;,&quot;radius&quot;:&quot;&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{},&quot;text&quot;:{&quot;align&quot;:&quot;right&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;&quot;,&quot;class&quot;:&quot;slds-text-align_right tight-buttons-container&quot;,&quot;style&quot;:&quot;      \n         &quot;,&quot;customClass&quot;:&quot;tight-buttons-container&quot;},&quot;elementLabel&quot;:&quot;Prendi In Carico&quot;,&quot;styleObjects&quot;:[{&quot;key&quot;:0,&quot;conditions&quot;:&quot;default&quot;,&quot;styleObject&quot;:{&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;6&quot;,&quot;large&quot;:&quot;11&quot;,&quot;medium&quot;:&quot;11&quot;,&quot;small&quot;:&quot;11&quot;},&quot;sizeClass&quot;:&quot;slds-size_6-of-12 &quot;,&quot;padding&quot;:[],&quot;margin&quot;:[],&quot;background&quot;:{&quot;color&quot;:&quot;&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:&quot;&quot;,&quot;width&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;,&quot;radius&quot;:&quot;&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{},&quot;text&quot;:{&quot;align&quot;:&quot;right&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;&quot;,&quot;class&quot;:&quot;slds-text-align_right tight-buttons-container&quot;,&quot;style&quot;:&quot;      \n         &quot;,&quot;customClass&quot;:&quot;tight-buttons-container&quot;},&quot;label&quot;:&quot;Default&quot;,&quot;name&quot;:&quot;Default&quot;,&quot;conditionString&quot;:&quot;&quot;,&quot;draggable&quot;:false}],&quot;key&quot;:&quot;element_element_element_element_block_0_0_block_0_0_block_3_0_customLwc_0_0&quot;,&quot;parentElementKey&quot;:&quot;element_element_element_block_0_0_block_0_0_block_3_0&quot;,&quot;userUpdatedElementLabel&quot;:true},{&quot;name&quot;:&quot;Custom LWC&quot;,&quot;element&quot;:&quot;customLwc&quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;6&quot;,&quot;large&quot;:&quot;11&quot;,&quot;medium&quot;:&quot;11&quot;,&quot;small&quot;:&quot;11&quot;},&quot;stateIndex&quot;:0,&quot;class&quot;:&quot;slds-col &quot;,&quot;property&quot;:{&quot;recordId&quot;:&quot;{recordId}&quot;,&quot;buttonLabel&quot;:&quot;Rifiuta&quot;,&quot;flowApiName&quot;:&quot;Handle_Opportunity_Reject_Opportunities&quot;,&quot;isFlowModal&quot;:&quot;true&quot;,&quot;refreshPageOnFinshedFlow&quot;:&quot;true&quot;,&quot;customlwcname&quot;:&quot;launchFlow&quot;,&quot;data-conditions&quot;:{&quot;id&quot;:&quot;state-condition-object&quot;,&quot;isParent&quot;:true,&quot;group&quot;:[{&quot;id&quot;:&quot;state-new-condition-84&quot;,&quot;field&quot;:&quot;true&quot;,&quot;operator&quot;:&quot;==&quot;,&quot;value&quot;:&quot;false&quot;,&quot;type&quot;:&quot;custom&quot;,&quot;hasMergeField&quot;:false}]}},&quot;type&quot;:&quot;element&quot;,&quot;styleObject&quot;:{&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;6&quot;,&quot;large&quot;:&quot;11&quot;,&quot;medium&quot;:&quot;11&quot;,&quot;small&quot;:&quot;11&quot;},&quot;sizeClass&quot;:&quot;slds-size_6-of-12 &quot;,&quot;padding&quot;:[],&quot;margin&quot;:[],&quot;background&quot;:{&quot;color&quot;:&quot;&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:&quot;&quot;,&quot;width&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;,&quot;radius&quot;:&quot;&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{},&quot;text&quot;:{&quot;align&quot;:&quot;right&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;&quot;,&quot;class&quot;:&quot;slds-text-align_right tight-buttons-container&quot;,&quot;style&quot;:&quot;      \n         &quot;,&quot;customClass&quot;:&quot;tight-buttons-container&quot;},&quot;elementLabel&quot;:&quot;Buttons In Assegnato-Custom LWC-0-clone-0&quot;,&quot;styleObjects&quot;:[{&quot;key&quot;:0,&quot;conditions&quot;:&quot;default&quot;,&quot;styleObject&quot;:{&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;6&quot;,&quot;large&quot;:&quot;11&quot;,&quot;medium&quot;:&quot;11&quot;,&quot;small&quot;:&quot;11&quot;},&quot;sizeClass&quot;:&quot;slds-size_6-of-12 &quot;,&quot;padding&quot;:[],&quot;margin&quot;:[],&quot;background&quot;:{&quot;color&quot;:&quot;&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:&quot;&quot;,&quot;width&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;,&quot;radius&quot;:&quot;&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{},&quot;text&quot;:{&quot;align&quot;:&quot;right&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;&quot;,&quot;class&quot;:&quot;slds-text-align_right tight-buttons-container&quot;,&quot;style&quot;:&quot;      \n         &quot;,&quot;customClass&quot;:&quot;tight-buttons-container&quot;},&quot;label&quot;:&quot;Default&quot;,&quot;name&quot;:&quot;Default&quot;,&quot;conditionString&quot;:&quot;&quot;,&quot;draggable&quot;:false}],&quot;key&quot;:&quot;element_element_element_element_block_0_0_block_0_0_block_3_0_customLwc_1_0&quot;,&quot;parentElementKey&quot;:&quot;element_element_element_block_0_0_block_0_0_block_3_0&quot;,&quot;userUpdatedElementLabel&quot;:true}],&quot;elementLabel&quot;:&quot;Buttons In Assegnato&quot;,&quot;styleObjects&quot;:[{&quot;key&quot;:0,&quot;conditions&quot;:&quot;default&quot;,&quot;styleObject&quot;:{&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;9&quot;,&quot;large&quot;:&quot;5&quot;,&quot;medium&quot;:&quot;12&quot;,&quot;small&quot;:&quot;12&quot;},&quot;padding&quot;:[{&quot;type&quot;:&quot;around&quot;,&quot;size&quot;:&quot;x-small&quot;,&quot;label&quot;:&quot;around:x-small&quot;}],&quot;class&quot;:&quot;slds-text-align_right slds-p-around_x-small &quot;,&quot;sizeClass&quot;:&quot;slds-size_9-of-12 &quot;,&quot;margin&quot;:[],&quot;background&quot;:{&quot;color&quot;:&quot;&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:&quot;&quot;,&quot;width&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;,&quot;radius&quot;:&quot;&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{},&quot;text&quot;:{&quot;align&quot;:&quot;right&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;display: flex; flex-direction: row-reverse;\n&quot;,&quot;style&quot;:&quot;      \n         display: flex; flex-direction: row-reverse;\n&quot;},&quot;label&quot;:&quot;Default&quot;,&quot;name&quot;:&quot;Default&quot;,&quot;conditionString&quot;:&quot;&quot;,&quot;draggable&quot;:false}],&quot;key&quot;:&quot;element_element_element_block_0_0_block_0_0_block_3_0&quot;,&quot;parentElementKey&quot;:&quot;element_element_block_0_0_block_0_0&quot;,&quot;userUpdatedElementLabel&quot;:true}],&quot;elementLabel&quot;:&quot;Block-0-Block-2&quot;,&quot;styleObjects&quot;:[{&quot;key&quot;:0,&quot;conditions&quot;:&quot;default&quot;,&quot;styleObject&quot;:{&quot;size&quot;:{&quot;isResponsive&quot;:true,&quot;default&quot;:&quot;12&quot;,&quot;large&quot;:&quot;12&quot;,&quot;medium&quot;:&quot;12&quot;,&quot;small&quot;:&quot;12&quot;},&quot;padding&quot;:[{&quot;type&quot;:&quot;around&quot;,&quot;size&quot;:&quot;x-small&quot;,&quot;label&quot;:&quot;around:x-small&quot;}],&quot;class&quot;:&quot;slds-p-around_x-small &quot;,&quot;sizeClass&quot;:&quot;slds-large-size_12-of-12 slds-medium-size_12-of-12 slds-small-size_12-of-12 slds-size_12-of-12 &quot;,&quot;margin&quot;:[],&quot;background&quot;:{&quot;color&quot;:&quot;&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:&quot;&quot;,&quot;width&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;,&quot;radius&quot;:&quot;&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{},&quot;text&quot;:{&quot;align&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;&quot;,&quot;style&quot;:&quot;      \n         &quot;},&quot;label&quot;:&quot;Default&quot;,&quot;name&quot;:&quot;Default&quot;,&quot;conditionString&quot;:&quot;&quot;,&quot;draggable&quot;:false}],&quot;key&quot;:&quot;element_element_block_0_0_block_0_0&quot;,&quot;parentElementKey&quot;:&quot;element_block_0_0&quot;}],&quot;elementLabel&quot;:&quot;Block-0&quot;,&quot;styleObjects&quot;:[{&quot;key&quot;:0,&quot;conditions&quot;:&quot;default&quot;,&quot;styleObject&quot;:{&quot;padding&quot;:[{&quot;type&quot;:&quot;around&quot;,&quot;size&quot;:&quot;x-small&quot;,&quot;label&quot;:&quot;around:x-small&quot;}],&quot;class&quot;:&quot;slds-p-around_x-small &quot;,&quot;sizeClass&quot;:&quot;slds-size_12-of-12 &quot;,&quot;margin&quot;:[],&quot;background&quot;:{&quot;color&quot;:&quot;#F7F7F7&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;12&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:&quot;&quot;,&quot;width&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;,&quot;radius&quot;:&quot;&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{},&quot;text&quot;:{&quot;align&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;&quot;,&quot;style&quot;:&quot;background-color:#F7F7F7;      \n         &quot;},&quot;label&quot;:&quot;Default&quot;,&quot;name&quot;:&quot;Default&quot;,&quot;conditionString&quot;:&quot;&quot;,&quot;draggable&quot;:false}]},{&quot;name&quot;:&quot;Block&quot;,&quot;element&quot;:&quot;block&quot;,&quot;size&quot;:{&quot;isResponsive&quot;:true,&quot;default&quot;:&quot;12&quot;,&quot;large&quot;:&quot;12&quot;,&quot;medium&quot;:&quot;11&quot;,&quot;small&quot;:&quot;12&quot;},&quot;stateIndex&quot;:0,&quot;class&quot;:&quot;slds-col &quot;,&quot;property&quot;:{&quot;label&quot;:&quot;Block&quot;,&quot;collapsible&quot;:false,&quot;record&quot;:&quot;{record}&quot;,&quot;collapsedByDefault&quot;:false,&quot;card&quot;:&quot;{card}&quot;},&quot;type&quot;:&quot;block&quot;,&quot;styleObject&quot;:{&quot;padding&quot;:[{&quot;type&quot;:&quot;around&quot;,&quot;size&quot;:&quot;x-small&quot;,&quot;label&quot;:&quot;around:x-small&quot;}],&quot;class&quot;:&quot;slds-p-around_x-small &quot;,&quot;sizeClass&quot;:&quot;slds-large-size_12-of-12 slds-medium-size_11-of-12 slds-small-size_12-of-12 slds-size_12-of-12 &quot;,&quot;margin&quot;:[],&quot;background&quot;:{&quot;color&quot;:&quot;&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;size&quot;:{&quot;isResponsive&quot;:true,&quot;default&quot;:&quot;12&quot;,&quot;large&quot;:&quot;12&quot;,&quot;medium&quot;:&quot;11&quot;,&quot;small&quot;:&quot;12&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:&quot;&quot;,&quot;width&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;,&quot;radius&quot;:&quot;&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{},&quot;text&quot;:{&quot;align&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;&quot;,&quot;style&quot;:&quot;      \n         &quot;,&quot;customClass&quot;:&quot;&quot;},&quot;children&quot;:[{&quot;name&quot;:&quot;Text&quot;,&quot;element&quot;:&quot;outputField&quot;,&quot;size&quot;:{&quot;isResponsive&quot;:true,&quot;default&quot;:&quot;1&quot;,&quot;large&quot;:&quot;1&quot;,&quot;medium&quot;:&quot;1&quot;,&quot;small&quot;:&quot;1&quot;},&quot;stateIndex&quot;:0,&quot;class&quot;:&quot;slds-col &quot;,&quot;property&quot;:{&quot;record&quot;:&quot;{record}&quot;,&quot;mergeField&quot;:&quot;%3Cdiv%20class=%22slds-text-color_default%22%3E%3Cspan%20style=%22font-size:%2010pt;%20font-family:%20&apos;Salesforce%20Sans&apos;,%20Arial,%20sans-serif;%20color:%20#565252;%22%3ETemperatura%3C/span%3E%3C/div%3E%0A%3Cdiv%3E%3Cspan%20style=%22font-size:%2010pt;%22%3E%7Btemperature%7D%3C/span%3E%3C/div%3E&quot;,&quot;card&quot;:&quot;{card}&quot;},&quot;type&quot;:&quot;text&quot;,&quot;styleObject&quot;:{&quot;sizeClass&quot;:&quot;slds-large-size_1-of-12 slds-medium-size_1-of-12 slds-small-size_1-of-12 slds-size_1-of-12 &quot;,&quot;size&quot;:{&quot;isResponsive&quot;:true,&quot;default&quot;:&quot;1&quot;,&quot;large&quot;:&quot;1&quot;,&quot;medium&quot;:&quot;1&quot;,&quot;small&quot;:&quot;1&quot;},&quot;padding&quot;:[],&quot;margin&quot;:[],&quot;background&quot;:{&quot;color&quot;:&quot;&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:&quot;&quot;,&quot;width&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;,&quot;radius&quot;:&quot;&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{},&quot;text&quot;:{&quot;align&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;margin: 1px&quot;,&quot;class&quot;:&quot;tight-columns-container &quot;,&quot;style&quot;:&quot;      \n         margin: 1px&quot;,&quot;customClass&quot;:&quot;tight-columns-container &quot;},&quot;elementLabel&quot;:&quot;Block-1-Text-0&quot;,&quot;styleObjects&quot;:[{&quot;key&quot;:0,&quot;conditions&quot;:&quot;default&quot;,&quot;styleObject&quot;:{&quot;sizeClass&quot;:&quot;slds-large-size_1-of-12 slds-medium-size_1-of-12 slds-small-size_1-of-12 slds-size_1-of-12 &quot;,&quot;size&quot;:{&quot;isResponsive&quot;:true,&quot;default&quot;:&quot;1&quot;,&quot;large&quot;:&quot;1&quot;,&quot;medium&quot;:&quot;1&quot;,&quot;small&quot;:&quot;1&quot;},&quot;padding&quot;:[],&quot;margin&quot;:[],&quot;background&quot;:{&quot;color&quot;:&quot;&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:&quot;&quot;,&quot;width&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;,&quot;radius&quot;:&quot;&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{},&quot;text&quot;:{&quot;align&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;margin: 1px&quot;,&quot;class&quot;:&quot;tight-columns-container &quot;,&quot;style&quot;:&quot;      \n         margin: 1px&quot;,&quot;customClass&quot;:&quot;tight-columns-container &quot;},&quot;label&quot;:&quot;Default&quot;,&quot;name&quot;:&quot;Default&quot;,&quot;conditionString&quot;:&quot;&quot;,&quot;draggable&quot;:false}],&quot;key&quot;:&quot;element_element_block_1_0_outputField_0_0&quot;,&quot;parentElementKey&quot;:&quot;element_block_1_0&quot;},{&quot;key&quot;:&quot;element_element_block_1_0_outputField_1_0&quot;,&quot;name&quot;:&quot;Field&quot;,&quot;element&quot;:&quot;outputField&quot;,&quot;size&quot;:{&quot;isResponsive&quot;:true,&quot;default&quot;:&quot;1&quot;,&quot;large&quot;:&quot;1&quot;,&quot;medium&quot;:&quot;1&quot;,&quot;small&quot;:&quot;1&quot;},&quot;stateIndex&quot;:0,&quot;class&quot;:&quot;slds-col &quot;,&quot;property&quot;:{&quot;placeholder&quot;:&quot;N/D&quot;,&quot;record&quot;:&quot;{record}&quot;,&quot;type&quot;:&quot;text&quot;,&quot;card&quot;:&quot;{card}&quot;,&quot;label&quot;:&quot;Canale origine&quot;,&quot;fieldName&quot;:&quot;engpoint&quot;,&quot;styles&quot;:{&quot;label&quot;:{&quot;fontSize&quot;:&quot;14px&quot;},&quot;value&quot;:{&quot;fontSize&quot;:&quot;14px&quot;}}},&quot;type&quot;:&quot;element&quot;,&quot;styleObject&quot;:{&quot;size&quot;:{&quot;isResponsive&quot;:true,&quot;default&quot;:&quot;1&quot;,&quot;large&quot;:&quot;1&quot;,&quot;medium&quot;:&quot;1&quot;,&quot;small&quot;:&quot;1&quot;},&quot;sizeClass&quot;:&quot;slds-large-size_1-of-12 slds-medium-size_1-of-12 slds-small-size_1-of-12 slds-size_1-of-12 &quot;,&quot;padding&quot;:[],&quot;margin&quot;:[],&quot;background&quot;:{&quot;color&quot;:&quot;&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:&quot;&quot;,&quot;width&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;,&quot;radius&quot;:&quot;&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{&quot;styles&quot;:{&quot;label&quot;:{&quot;fontSize&quot;:&quot;14px&quot;},&quot;value&quot;:{&quot;fontSize&quot;:&quot;14px&quot;}}},&quot;text&quot;:{&quot;align&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;&quot;,&quot;class&quot;:&quot;tight-columns-container &quot;,&quot;style&quot;:&quot;      \n         &quot;,&quot;customClass&quot;:&quot;tight-columns-container &quot;},&quot;parentElementKey&quot;:&quot;element_block_1_0&quot;,&quot;elementLabel&quot;:&quot;Block-1-Field-1&quot;,&quot;styleObjects&quot;:[{&quot;key&quot;:0,&quot;conditions&quot;:&quot;default&quot;,&quot;styleObject&quot;:{&quot;size&quot;:{&quot;isResponsive&quot;:true,&quot;default&quot;:&quot;1&quot;,&quot;large&quot;:&quot;1&quot;,&quot;medium&quot;:&quot;1&quot;,&quot;small&quot;:&quot;1&quot;},&quot;sizeClass&quot;:&quot;slds-large-size_1-of-12 slds-medium-size_1-of-12 slds-small-size_1-of-12 slds-size_1-of-12 &quot;,&quot;padding&quot;:[],&quot;margin&quot;:[],&quot;background&quot;:{&quot;color&quot;:&quot;&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:&quot;&quot;,&quot;width&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;,&quot;radius&quot;:&quot;&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{&quot;styles&quot;:{&quot;label&quot;:{&quot;fontSize&quot;:&quot;14px&quot;},&quot;value&quot;:{&quot;fontSize&quot;:&quot;14px&quot;}}},&quot;text&quot;:{&quot;align&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;&quot;,&quot;class&quot;:&quot;tight-columns-container &quot;,&quot;style&quot;:&quot;      \n         &quot;,&quot;customClass&quot;:&quot;tight-columns-container &quot;},&quot;label&quot;:&quot;Default&quot;,&quot;name&quot;:&quot;Default&quot;,&quot;conditionString&quot;:&quot;&quot;,&quot;draggable&quot;:false}]},{&quot;key&quot;:&quot;element_element_block_1_0_outputField_2_0&quot;,&quot;name&quot;:&quot;Field&quot;,&quot;element&quot;:&quot;outputField&quot;,&quot;size&quot;:{&quot;isResponsive&quot;:true,&quot;default&quot;:&quot;1&quot;,&quot;large&quot;:&quot;1&quot;,&quot;medium&quot;:&quot;1&quot;,&quot;small&quot;:&quot;1&quot;},&quot;stateIndex&quot;:0,&quot;class&quot;:&quot;slds-col &quot;,&quot;property&quot;:{&quot;placeholder&quot;:&quot;-&quot;,&quot;record&quot;:&quot;{record}&quot;,&quot;type&quot;:&quot;text&quot;,&quot;card&quot;:&quot;{card}&quot;,&quot;label&quot;:&quot;Nome campagna&quot;,&quot;styles&quot;:{&quot;label&quot;:{&quot;fontSize&quot;:&quot;14px&quot;},&quot;value&quot;:{&quot;fontSize&quot;:&quot;14px&quot;}}},&quot;type&quot;:&quot;element&quot;,&quot;styleObject&quot;:{&quot;size&quot;:{&quot;isResponsive&quot;:true,&quot;default&quot;:&quot;1&quot;,&quot;large&quot;:&quot;1&quot;,&quot;medium&quot;:&quot;1&quot;,&quot;small&quot;:&quot;1&quot;},&quot;sizeClass&quot;:&quot;slds-large-size_1-of-12 slds-medium-size_1-of-12 slds-small-size_1-of-12 slds-size_1-of-12 &quot;,&quot;padding&quot;:[],&quot;margin&quot;:[],&quot;background&quot;:{&quot;color&quot;:&quot;&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:&quot;&quot;,&quot;width&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;,&quot;radius&quot;:&quot;&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{&quot;styles&quot;:{&quot;label&quot;:{&quot;fontSize&quot;:&quot;14px&quot;},&quot;value&quot;:{&quot;fontSize&quot;:&quot;14px&quot;}}},&quot;text&quot;:{&quot;align&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;&quot;,&quot;customClass&quot;:&quot;tight-buttons-container&quot;,&quot;class&quot;:&quot;tight-buttons-container&quot;,&quot;style&quot;:&quot;      \n         &quot;},&quot;parentElementKey&quot;:&quot;element_block_1_0&quot;,&quot;elementLabel&quot;:&quot;Block-1-Field-2&quot;,&quot;styleObjects&quot;:[{&quot;key&quot;:0,&quot;conditions&quot;:&quot;default&quot;,&quot;styleObject&quot;:{&quot;size&quot;:{&quot;isResponsive&quot;:true,&quot;default&quot;:&quot;1&quot;,&quot;large&quot;:&quot;1&quot;,&quot;medium&quot;:&quot;1&quot;,&quot;small&quot;:&quot;1&quot;},&quot;sizeClass&quot;:&quot;slds-large-size_1-of-12 slds-medium-size_1-of-12 slds-small-size_1-of-12 slds-size_1-of-12 &quot;,&quot;padding&quot;:[],&quot;margin&quot;:[],&quot;background&quot;:{&quot;color&quot;:&quot;&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:&quot;&quot;,&quot;width&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;,&quot;radius&quot;:&quot;&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{&quot;styles&quot;:{&quot;label&quot;:{&quot;fontSize&quot;:&quot;14px&quot;},&quot;value&quot;:{&quot;fontSize&quot;:&quot;14px&quot;}}},&quot;text&quot;:{&quot;align&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;&quot;,&quot;customClass&quot;:&quot;tight-buttons-container&quot;,&quot;class&quot;:&quot;tight-buttons-container&quot;,&quot;style&quot;:&quot;      \n         &quot;},&quot;label&quot;:&quot;Default&quot;,&quot;name&quot;:&quot;Default&quot;,&quot;conditionString&quot;:&quot;&quot;,&quot;draggable&quot;:false}]},{&quot;key&quot;:&quot;element_element_block_1_0_outputField_3_0&quot;,&quot;name&quot;:&quot;Field&quot;,&quot;element&quot;:&quot;outputField&quot;,&quot;size&quot;:{&quot;isResponsive&quot;:true,&quot;default&quot;:&quot;1&quot;,&quot;large&quot;:&quot;1&quot;,&quot;medium&quot;:&quot;1&quot;,&quot;small&quot;:&quot;1&quot;},&quot;stateIndex&quot;:0,&quot;class&quot;:&quot;slds-col &quot;,&quot;property&quot;:{&quot;placeholder&quot;:&quot;N/D&quot;,&quot;record&quot;:&quot;{record}&quot;,&quot;type&quot;:&quot;date&quot;,&quot;card&quot;:&quot;{card}&quot;,&quot;label&quot;:&quot;Data creazione&quot;,&quot;format&quot;:&quot;&quot;,&quot;fieldName&quot;:&quot;createdDate&quot;,&quot;styles&quot;:{&quot;label&quot;:{&quot;fontSize&quot;:&quot;14px&quot;},&quot;value&quot;:{&quot;fontSize&quot;:&quot;14px&quot;}}},&quot;type&quot;:&quot;element&quot;,&quot;styleObject&quot;:{&quot;size&quot;:{&quot;isResponsive&quot;:true,&quot;default&quot;:&quot;1&quot;,&quot;large&quot;:&quot;1&quot;,&quot;medium&quot;:&quot;1&quot;,&quot;small&quot;:&quot;1&quot;},&quot;sizeClass&quot;:&quot;slds-large-size_1-of-12 slds-medium-size_1-of-12 slds-small-size_1-of-12 slds-size_1-of-12 &quot;,&quot;padding&quot;:[],&quot;margin&quot;:[],&quot;background&quot;:{&quot;color&quot;:&quot;&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:&quot;&quot;,&quot;width&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;,&quot;radius&quot;:&quot;&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{&quot;styles&quot;:{&quot;label&quot;:{&quot;fontSize&quot;:&quot;14px&quot;},&quot;value&quot;:{&quot;fontSize&quot;:&quot;14px&quot;}}},&quot;text&quot;:{&quot;align&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;&quot;,&quot;customClass&quot;:&quot;tight-buttons-container&quot;,&quot;class&quot;:&quot;tight-buttons-container&quot;,&quot;style&quot;:&quot;      \n         &quot;},&quot;parentElementKey&quot;:&quot;element_block_1_0&quot;,&quot;elementLabel&quot;:&quot;Block-1-Field-3&quot;,&quot;styleObjects&quot;:[{&quot;key&quot;:0,&quot;conditions&quot;:&quot;default&quot;,&quot;styleObject&quot;:{&quot;size&quot;:{&quot;isResponsive&quot;:true,&quot;default&quot;:&quot;1&quot;,&quot;large&quot;:&quot;1&quot;,&quot;medium&quot;:&quot;1&quot;,&quot;small&quot;:&quot;1&quot;},&quot;sizeClass&quot;:&quot;slds-large-size_1-of-12 slds-medium-size_1-of-12 slds-small-size_1-of-12 slds-size_1-of-12 &quot;,&quot;padding&quot;:[],&quot;margin&quot;:[],&quot;background&quot;:{&quot;color&quot;:&quot;&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:&quot;&quot;,&quot;width&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;,&quot;radius&quot;:&quot;&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{&quot;styles&quot;:{&quot;label&quot;:{&quot;fontSize&quot;:&quot;14px&quot;},&quot;value&quot;:{&quot;fontSize&quot;:&quot;14px&quot;}}},&quot;text&quot;:{&quot;align&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;&quot;,&quot;customClass&quot;:&quot;tight-buttons-container&quot;,&quot;class&quot;:&quot;tight-buttons-container&quot;,&quot;style&quot;:&quot;      \n         &quot;},&quot;label&quot;:&quot;Default&quot;,&quot;name&quot;:&quot;Default&quot;,&quot;conditionString&quot;:&quot;&quot;,&quot;draggable&quot;:false}]},{&quot;key&quot;:&quot;element_element_block_1_0_outputField_4_0&quot;,&quot;name&quot;:&quot;Field&quot;,&quot;element&quot;:&quot;outputField&quot;,&quot;size&quot;:{&quot;isResponsive&quot;:true,&quot;default&quot;:&quot;1&quot;,&quot;large&quot;:&quot;1&quot;,&quot;medium&quot;:&quot;1&quot;,&quot;small&quot;:&quot;1&quot;},&quot;stateIndex&quot;:0,&quot;class&quot;:&quot;slds-col &quot;,&quot;property&quot;:{&quot;placeholder&quot;:&quot;N/D&quot;,&quot;record&quot;:&quot;{record}&quot;,&quot;type&quot;:&quot;text&quot;,&quot;card&quot;:&quot;{card}&quot;,&quot;label&quot;:&quot;Canale di contatto&quot;,&quot;fieldName&quot;:&quot;contactChannel&quot;,&quot;styles&quot;:{&quot;label&quot;:{&quot;fontSize&quot;:&quot;14px&quot;},&quot;value&quot;:{&quot;fontSize&quot;:&quot;14px&quot;}}},&quot;type&quot;:&quot;element&quot;,&quot;styleObject&quot;:{&quot;size&quot;:{&quot;isResponsive&quot;:true,&quot;default&quot;:&quot;1&quot;,&quot;large&quot;:&quot;1&quot;,&quot;medium&quot;:&quot;1&quot;,&quot;small&quot;:&quot;1&quot;},&quot;sizeClass&quot;:&quot;slds-large-size_1-of-12 slds-medium-size_1-of-12 slds-small-size_1-of-12 slds-size_1-of-12 &quot;,&quot;padding&quot;:[],&quot;margin&quot;:[],&quot;background&quot;:{&quot;color&quot;:&quot;&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:&quot;&quot;,&quot;width&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;,&quot;radius&quot;:&quot;&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{&quot;styles&quot;:{&quot;label&quot;:{&quot;fontSize&quot;:&quot;14px&quot;},&quot;value&quot;:{&quot;fontSize&quot;:&quot;14px&quot;}}},&quot;text&quot;:{&quot;align&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;&quot;,&quot;customClass&quot;:&quot;tight-columns-container &quot;,&quot;class&quot;:&quot;tight-columns-container &quot;,&quot;style&quot;:&quot;      \n         &quot;},&quot;parentElementKey&quot;:&quot;element_block_1_0&quot;,&quot;elementLabel&quot;:&quot;Block-1-Field-4&quot;,&quot;styleObjects&quot;:[{&quot;key&quot;:0,&quot;conditions&quot;:&quot;default&quot;,&quot;styleObject&quot;:{&quot;size&quot;:{&quot;isResponsive&quot;:true,&quot;default&quot;:&quot;1&quot;,&quot;large&quot;:&quot;1&quot;,&quot;medium&quot;:&quot;1&quot;,&quot;small&quot;:&quot;1&quot;},&quot;sizeClass&quot;:&quot;slds-large-size_1-of-12 slds-medium-size_1-of-12 slds-small-size_1-of-12 slds-size_1-of-12 &quot;,&quot;padding&quot;:[],&quot;margin&quot;:[],&quot;background&quot;:{&quot;color&quot;:&quot;&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:&quot;&quot;,&quot;width&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;,&quot;radius&quot;:&quot;&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{&quot;styles&quot;:{&quot;label&quot;:{&quot;fontSize&quot;:&quot;14px&quot;},&quot;value&quot;:{&quot;fontSize&quot;:&quot;14px&quot;}}},&quot;text&quot;:{&quot;align&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;&quot;,&quot;customClass&quot;:&quot;tight-columns-container &quot;,&quot;class&quot;:&quot;tight-columns-container &quot;,&quot;style&quot;:&quot;      \n         &quot;},&quot;label&quot;:&quot;Default&quot;,&quot;name&quot;:&quot;Default&quot;,&quot;conditionString&quot;:&quot;&quot;,&quot;draggable&quot;:false}]},{&quot;key&quot;:&quot;element_element_block_1_0_outputField_5_0&quot;,&quot;name&quot;:&quot;Field&quot;,&quot;element&quot;:&quot;outputField&quot;,&quot;size&quot;:{&quot;isResponsive&quot;:true,&quot;default&quot;:&quot;1&quot;,&quot;large&quot;:&quot;1&quot;,&quot;medium&quot;:&quot;1&quot;,&quot;small&quot;:&quot;1&quot;},&quot;stateIndex&quot;:0,&quot;class&quot;:&quot;slds-col &quot;,&quot;property&quot;:{&quot;placeholder&quot;:&quot;&quot;,&quot;record&quot;:&quot;{record}&quot;,&quot;type&quot;:&quot;text&quot;,&quot;card&quot;:&quot;{card}&quot;,&quot;label&quot;:&quot;Seconda assegnazione&quot;,&quot;fieldName&quot;:&quot;secondAssignment&quot;,&quot;styles&quot;:{&quot;label&quot;:{&quot;fontSize&quot;:&quot;14px&quot;},&quot;value&quot;:{&quot;fontSize&quot;:&quot;14px&quot;}}},&quot;type&quot;:&quot;element&quot;,&quot;styleObject&quot;:{&quot;size&quot;:{&quot;isResponsive&quot;:true,&quot;default&quot;:&quot;1&quot;,&quot;large&quot;:&quot;1&quot;,&quot;medium&quot;:&quot;1&quot;,&quot;small&quot;:&quot;1&quot;},&quot;sizeClass&quot;:&quot;slds-large-size_1-of-12 slds-medium-size_1-of-12 slds-small-size_1-of-12 slds-size_1-of-12 &quot;,&quot;padding&quot;:[],&quot;margin&quot;:[],&quot;background&quot;:{&quot;color&quot;:&quot;&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:&quot;&quot;,&quot;width&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;,&quot;radius&quot;:&quot;&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{&quot;styles&quot;:{&quot;label&quot;:{&quot;fontSize&quot;:&quot;14px&quot;},&quot;value&quot;:{&quot;fontSize&quot;:&quot;14px&quot;}}},&quot;text&quot;:{&quot;align&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;&quot;,&quot;class&quot;:&quot;tight-columns-container &quot;,&quot;style&quot;:&quot;      \n         &quot;,&quot;customClass&quot;:&quot;tight-columns-container &quot;},&quot;parentElementKey&quot;:&quot;element_block_1_0&quot;,&quot;elementLabel&quot;:&quot;Block-1-Field-5&quot;,&quot;styleObjects&quot;:[{&quot;key&quot;:0,&quot;conditions&quot;:&quot;default&quot;,&quot;styleObject&quot;:{&quot;size&quot;:{&quot;isResponsive&quot;:true,&quot;default&quot;:&quot;1&quot;,&quot;large&quot;:&quot;1&quot;,&quot;medium&quot;:&quot;1&quot;,&quot;small&quot;:&quot;1&quot;},&quot;sizeClass&quot;:&quot;slds-large-size_1-of-12 slds-medium-size_1-of-12 slds-small-size_1-of-12 slds-size_1-of-12 &quot;,&quot;padding&quot;:[],&quot;margin&quot;:[],&quot;background&quot;:{&quot;color&quot;:&quot;&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:&quot;&quot;,&quot;width&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;,&quot;radius&quot;:&quot;&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{&quot;styles&quot;:{&quot;label&quot;:{&quot;fontSize&quot;:&quot;14px&quot;},&quot;value&quot;:{&quot;fontSize&quot;:&quot;14px&quot;}}},&quot;text&quot;:{&quot;align&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;&quot;,&quot;class&quot;:&quot;tight-columns-container &quot;,&quot;style&quot;:&quot;      \n         &quot;,&quot;customClass&quot;:&quot;tight-columns-container &quot;},&quot;label&quot;:&quot;Default&quot;,&quot;name&quot;:&quot;Default&quot;,&quot;conditionString&quot;:&quot;&quot;,&quot;draggable&quot;:false}]},{&quot;key&quot;:&quot;element_element_block_1_0_outputField_6_0&quot;,&quot;name&quot;:&quot;Field&quot;,&quot;element&quot;:&quot;outputField&quot;,&quot;size&quot;:{&quot;isResponsive&quot;:true,&quot;default&quot;:&quot;1&quot;,&quot;large&quot;:&quot;1&quot;,&quot;medium&quot;:&quot;1&quot;,&quot;small&quot;:&quot;1&quot;},&quot;stateIndex&quot;:0,&quot;class&quot;:&quot;slds-col &quot;,&quot;property&quot;:{&quot;placeholder&quot;:&quot;N/D&quot;,&quot;record&quot;:&quot;{record}&quot;,&quot;type&quot;:&quot;currency&quot;,&quot;card&quot;:&quot;{card}&quot;,&quot;label&quot;:&quot;Premio totale&quot;,&quot;locale&quot;:&quot;it-IT&quot;,&quot;currency&quot;:&quot;EUR&quot;,&quot;fieldName&quot;:&quot;oppAmount&quot;,&quot;data-conditions&quot;:{&quot;id&quot;:&quot;state-condition-object&quot;,&quot;isParent&quot;:true,&quot;group&quot;:[]},&quot;styles&quot;:{&quot;label&quot;:{&quot;fontSize&quot;:&quot;14px&quot;},&quot;value&quot;:{&quot;fontSize&quot;:&quot;14px&quot;}}},&quot;type&quot;:&quot;element&quot;,&quot;styleObject&quot;:{&quot;size&quot;:{&quot;isResponsive&quot;:true,&quot;default&quot;:&quot;1&quot;,&quot;large&quot;:&quot;1&quot;,&quot;medium&quot;:&quot;1&quot;,&quot;small&quot;:&quot;1&quot;},&quot;sizeClass&quot;:&quot;slds-large-size_1-of-12 slds-medium-size_1-of-12 slds-small-size_1-of-12 slds-size_1-of-12 &quot;,&quot;padding&quot;:[],&quot;margin&quot;:[],&quot;background&quot;:{&quot;color&quot;:&quot;&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:&quot;&quot;,&quot;width&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;,&quot;radius&quot;:&quot;&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{&quot;styles&quot;:{&quot;label&quot;:{&quot;fontSize&quot;:&quot;14px&quot;},&quot;value&quot;:{&quot;fontSize&quot;:&quot;14px&quot;}}},&quot;text&quot;:{&quot;align&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;&quot;,&quot;customClass&quot;:&quot;tight-columns-container &quot;,&quot;class&quot;:&quot;tight-columns-container &quot;,&quot;style&quot;:&quot;      \n         &quot;},&quot;parentElementKey&quot;:&quot;element_block_1_0&quot;,&quot;elementLabel&quot;:&quot;Block-1-Field-6&quot;,&quot;styleObjects&quot;:[{&quot;key&quot;:0,&quot;conditions&quot;:&quot;default&quot;,&quot;styleObject&quot;:{&quot;size&quot;:{&quot;isResponsive&quot;:true,&quot;default&quot;:&quot;1&quot;,&quot;large&quot;:&quot;1&quot;,&quot;medium&quot;:&quot;1&quot;,&quot;small&quot;:&quot;1&quot;},&quot;sizeClass&quot;:&quot;slds-large-size_1-of-12 slds-medium-size_1-of-12 slds-small-size_1-of-12 slds-size_1-of-12 &quot;,&quot;padding&quot;:[],&quot;margin&quot;:[],&quot;background&quot;:{&quot;color&quot;:&quot;&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:&quot;&quot;,&quot;width&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;,&quot;radius&quot;:&quot;&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{&quot;styles&quot;:{&quot;label&quot;:{&quot;fontSize&quot;:&quot;14px&quot;},&quot;value&quot;:{&quot;fontSize&quot;:&quot;14px&quot;}}},&quot;text&quot;:{&quot;align&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;&quot;,&quot;customClass&quot;:&quot;tight-columns-container &quot;,&quot;class&quot;:&quot;tight-columns-container &quot;,&quot;style&quot;:&quot;      \n         &quot;},&quot;label&quot;:&quot;Default&quot;,&quot;name&quot;:&quot;Default&quot;,&quot;conditionString&quot;:&quot;&quot;,&quot;draggable&quot;:false}]},{&quot;name&quot;:&quot;Text&quot;,&quot;element&quot;:&quot;outputField&quot;,&quot;size&quot;:{&quot;isResponsive&quot;:true,&quot;default&quot;:&quot;1&quot;,&quot;large&quot;:&quot;1&quot;,&quot;medium&quot;:&quot;1&quot;,&quot;small&quot;:&quot;1&quot;},&quot;stateIndex&quot;:0,&quot;class&quot;:&quot;slds-col &quot;,&quot;property&quot;:{&quot;record&quot;:&quot;{record}&quot;,&quot;mergeField&quot;:&quot;%3Cdiv%3E%3Cspan%20style=%22font-size:%2010pt;%20font-family:%20&apos;Salesforce%20Sans&apos;,%20Arial,%20sans-serif;%22%3EAmbiti%3C/span%3E%3C/div%3E%0A%3Cdiv%3E%3Cspan%20style=%22font-size:%2010pt;%20font-family:%20&apos;Salesforce%20Sans&apos;,%20Arial,%20sans-serif;%22%3E%7BareaOfneedsformula%7D%3C/span%3E%3C/div%3E&quot;,&quot;card&quot;:&quot;{card}&quot;},&quot;type&quot;:&quot;text&quot;,&quot;styleObject&quot;:{&quot;size&quot;:{&quot;isResponsive&quot;:true,&quot;default&quot;:&quot;1&quot;,&quot;large&quot;:&quot;1&quot;,&quot;medium&quot;:&quot;1&quot;,&quot;small&quot;:&quot;1&quot;},&quot;sizeClass&quot;:&quot;slds-large-size_1-of-12 slds-medium-size_1-of-12 slds-small-size_1-of-12 slds-size_1-of-12 &quot;,&quot;padding&quot;:[],&quot;margin&quot;:[],&quot;background&quot;:{&quot;color&quot;:&quot;&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:&quot;&quot;,&quot;width&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;,&quot;radius&quot;:&quot;&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{},&quot;text&quot;:{&quot;align&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;&quot;,&quot;customClass&quot;:&quot;tight-columns-container &quot;,&quot;class&quot;:&quot;tight-columns-container &quot;,&quot;style&quot;:&quot;      \n         &quot;},&quot;elementLabel&quot;:&quot;Block-1-Text-7&quot;,&quot;key&quot;:&quot;element_element_block_1_0_outputField_7_0&quot;,&quot;parentElementKey&quot;:&quot;element_block_1_0&quot;,&quot;styleObjects&quot;:[{&quot;key&quot;:0,&quot;conditions&quot;:&quot;default&quot;,&quot;styleObject&quot;:{&quot;size&quot;:{&quot;isResponsive&quot;:true,&quot;default&quot;:&quot;1&quot;,&quot;large&quot;:&quot;1&quot;,&quot;medium&quot;:&quot;1&quot;,&quot;small&quot;:&quot;1&quot;},&quot;sizeClass&quot;:&quot;slds-large-size_1-of-12 slds-medium-size_1-of-12 slds-small-size_1-of-12 slds-size_1-of-12 &quot;,&quot;padding&quot;:[],&quot;margin&quot;:[],&quot;background&quot;:{&quot;color&quot;:&quot;&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:&quot;&quot;,&quot;width&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;,&quot;radius&quot;:&quot;&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{},&quot;text&quot;:{&quot;align&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;&quot;,&quot;customClass&quot;:&quot;tight-columns-container &quot;,&quot;class&quot;:&quot;tight-columns-container &quot;,&quot;style&quot;:&quot;      \n         &quot;},&quot;label&quot;:&quot;Default&quot;,&quot;name&quot;:&quot;Default&quot;,&quot;conditionString&quot;:&quot;&quot;,&quot;draggable&quot;:false}]},{&quot;name&quot;:&quot;Field&quot;,&quot;element&quot;:&quot;outputField&quot;,&quot;size&quot;:{&quot;isResponsive&quot;:true,&quot;default&quot;:&quot;1&quot;,&quot;large&quot;:&quot;1&quot;,&quot;medium&quot;:&quot;1&quot;,&quot;small&quot;:&quot;1&quot;},&quot;stateIndex&quot;:0,&quot;class&quot;:&quot;slds-col &quot;,&quot;property&quot;:{&quot;placeholder&quot;:&quot;-&quot;,&quot;record&quot;:&quot;{record}&quot;,&quot;type&quot;:&quot;date&quot;,&quot;card&quot;:&quot;{card}&quot;,&quot;label&quot;:&quot;Data chiusura&quot;,&quot;format&quot;:&quot;&quot;,&quot;fieldName&quot;:&quot;closeDate&quot;,&quot;styles&quot;:{&quot;label&quot;:{&quot;fontSize&quot;:&quot;14px&quot;},&quot;value&quot;:{&quot;fontSize&quot;:&quot;14px&quot;}}},&quot;type&quot;:&quot;element&quot;,&quot;styleObject&quot;:{&quot;size&quot;:{&quot;isResponsive&quot;:true,&quot;default&quot;:&quot;1&quot;,&quot;large&quot;:&quot;1&quot;,&quot;medium&quot;:&quot;1&quot;,&quot;small&quot;:&quot;1&quot;},&quot;sizeClass&quot;:&quot;slds-large-size_1-of-12 slds-medium-size_1-of-12 slds-small-size_1-of-12 slds-size_1-of-12 &quot;,&quot;padding&quot;:[],&quot;margin&quot;:[],&quot;background&quot;:{&quot;color&quot;:&quot;&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:&quot;&quot;,&quot;width&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;,&quot;radius&quot;:&quot;&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{&quot;styles&quot;:{&quot;label&quot;:{&quot;fontSize&quot;:&quot;14px&quot;},&quot;value&quot;:{&quot;fontSize&quot;:&quot;14px&quot;}}},&quot;text&quot;:{&quot;align&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;&quot;,&quot;customClass&quot;:&quot;tight-columns-container &quot;,&quot;class&quot;:&quot;tight-columns-container &quot;,&quot;style&quot;:&quot;      \n         &quot;},&quot;elementLabel&quot;:&quot;Block-1-Field-9&quot;,&quot;styleObjects&quot;:[{&quot;key&quot;:0,&quot;conditions&quot;:&quot;default&quot;,&quot;styleObject&quot;:{&quot;size&quot;:{&quot;isResponsive&quot;:true,&quot;default&quot;:&quot;1&quot;,&quot;large&quot;:&quot;1&quot;,&quot;medium&quot;:&quot;1&quot;,&quot;small&quot;:&quot;1&quot;},&quot;sizeClass&quot;:&quot;slds-large-size_1-of-12 slds-medium-size_1-of-12 slds-small-size_1-of-12 slds-size_1-of-12 &quot;,&quot;padding&quot;:[],&quot;margin&quot;:[],&quot;background&quot;:{&quot;color&quot;:&quot;&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:&quot;&quot;,&quot;width&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;,&quot;radius&quot;:&quot;&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{&quot;styles&quot;:{&quot;label&quot;:{&quot;fontSize&quot;:&quot;14px&quot;},&quot;value&quot;:{&quot;fontSize&quot;:&quot;14px&quot;}}},&quot;text&quot;:{&quot;align&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;&quot;,&quot;customClass&quot;:&quot;tight-columns-container &quot;,&quot;class&quot;:&quot;tight-columns-container &quot;,&quot;style&quot;:&quot;      \n         &quot;},&quot;label&quot;:&quot;Default&quot;,&quot;name&quot;:&quot;Default&quot;,&quot;conditionString&quot;:&quot;&quot;,&quot;draggable&quot;:false}],&quot;key&quot;:&quot;element_element_block_1_0_outputField_8_0&quot;,&quot;parentElementKey&quot;:&quot;element_block_1_0&quot;},{&quot;key&quot;:&quot;element_element_block_1_0_outputField_9_0&quot;,&quot;name&quot;:&quot;Field&quot;,&quot;element&quot;:&quot;outputField&quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;1&quot;},&quot;stateIndex&quot;:0,&quot;class&quot;:&quot;slds-col &quot;,&quot;property&quot;:{&quot;placeholder&quot;:&quot;&quot;,&quot;record&quot;:&quot;{record}&quot;,&quot;type&quot;:&quot;text&quot;,&quot;card&quot;:&quot;{card}&quot;,&quot;label&quot;:&quot;Esito&quot;,&quot;fieldName&quot;:&quot;closeSubStatus&quot;,&quot;styles&quot;:{&quot;label&quot;:{&quot;fontSize&quot;:&quot;14px&quot;},&quot;value&quot;:{&quot;fontSize&quot;:&quot;14px&quot;}}},&quot;type&quot;:&quot;element&quot;,&quot;styleObject&quot;:{&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;1&quot;},&quot;sizeClass&quot;:&quot;slds-size_1-of-12 &quot;,&quot;padding&quot;:[],&quot;margin&quot;:[],&quot;background&quot;:{&quot;color&quot;:&quot;&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:&quot;&quot;,&quot;width&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;,&quot;radius&quot;:&quot;&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{&quot;styles&quot;:{&quot;label&quot;:{&quot;fontSize&quot;:&quot;14px&quot;},&quot;value&quot;:{&quot;fontSize&quot;:&quot;14px&quot;}}},&quot;text&quot;:{&quot;align&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;&quot;,&quot;customClass&quot;:&quot;tight-columns-container &quot;,&quot;class&quot;:&quot;tight-columns-container &quot;,&quot;style&quot;:&quot;      \n         &quot;},&quot;parentElementKey&quot;:&quot;element_block_1_0&quot;,&quot;elementLabel&quot;:&quot;Block-1-Field-10&quot;,&quot;styleObjects&quot;:[{&quot;key&quot;:0,&quot;conditions&quot;:&quot;default&quot;,&quot;styleObject&quot;:{&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;1&quot;},&quot;sizeClass&quot;:&quot;slds-size_1-of-12 &quot;,&quot;padding&quot;:[],&quot;margin&quot;:[],&quot;background&quot;:{&quot;color&quot;:&quot;&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:&quot;&quot;,&quot;width&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;,&quot;radius&quot;:&quot;&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{&quot;styles&quot;:{&quot;label&quot;:{&quot;fontSize&quot;:&quot;14px&quot;},&quot;value&quot;:{&quot;fontSize&quot;:&quot;14px&quot;}}},&quot;text&quot;:{&quot;align&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;&quot;,&quot;customClass&quot;:&quot;tight-columns-container &quot;,&quot;class&quot;:&quot;tight-columns-container &quot;,&quot;style&quot;:&quot;      \n         &quot;},&quot;label&quot;:&quot;Default&quot;,&quot;name&quot;:&quot;Default&quot;,&quot;conditionString&quot;:&quot;&quot;,&quot;draggable&quot;:false}]}],&quot;elementLabel&quot;:&quot;Block-1&quot;,&quot;styleObjects&quot;:[{&quot;key&quot;:0,&quot;conditions&quot;:&quot;default&quot;,&quot;styleObject&quot;:{&quot;padding&quot;:[{&quot;type&quot;:&quot;around&quot;,&quot;size&quot;:&quot;x-small&quot;,&quot;label&quot;:&quot;around:x-small&quot;}],&quot;class&quot;:&quot;slds-p-around_x-small &quot;,&quot;sizeClass&quot;:&quot;slds-large-size_12-of-12 slds-medium-size_11-of-12 slds-small-size_12-of-12 slds-size_12-of-12 &quot;,&quot;margin&quot;:[],&quot;background&quot;:{&quot;color&quot;:&quot;&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;size&quot;:{&quot;isResponsive&quot;:true,&quot;default&quot;:&quot;12&quot;,&quot;large&quot;:&quot;12&quot;,&quot;medium&quot;:&quot;11&quot;,&quot;small&quot;:&quot;12&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:&quot;&quot;,&quot;width&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;,&quot;radius&quot;:&quot;&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{},&quot;text&quot;:{&quot;align&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;&quot;,&quot;style&quot;:&quot;      \n         &quot;,&quot;customClass&quot;:&quot;&quot;},&quot;label&quot;:&quot;Default&quot;,&quot;name&quot;:&quot;Default&quot;,&quot;conditionString&quot;:&quot;&quot;,&quot;draggable&quot;:false}]},{&quot;name&quot;:&quot;Custom LWC&quot;,&quot;element&quot;:&quot;customLwc&quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;12&quot;},&quot;stateIndex&quot;:0,&quot;class&quot;:&quot;slds-col &quot;,&quot;property&quot;:{&quot;customlwcname&quot;:&quot;uniforceFlexcardRefreshListener&quot;},&quot;type&quot;:&quot;element&quot;,&quot;styleObject&quot;:{&quot;sizeClass&quot;:&quot;slds-size_12-of-12&quot;},&quot;elementLabel&quot;:&quot;Custom LWC-2&quot;}]}},&quot;childCards&quot;:[],&quot;actions&quot;:[],&quot;omniscripts&quot;:[],&quot;documents&quot;:[]}],&quot;dataSource&quot;:{&quot;type&quot;:&quot;IntegrationProcedures&quot;,&quot;value&quot;:{&quot;dsDelay&quot;:&quot;&quot;,&quot;ipMethod&quot;:&quot;LeadManagement_GetAccountOppData&quot;,&quot;vlocityAsync&quot;:false,&quot;inputMap&quot;:{&quot;recordId&quot;:&quot;{recordId}&quot;},&quot;jsonMap&quot;:&quot;{\&quot;recordId\&quot;:\&quot;{recordId}\&quot;,\&quot;accountId\&quot;:\&quot;{accountId}\&quot;}&quot;,&quot;resultVar&quot;:&quot;&quot;},&quot;orderBy&quot;:{&quot;name&quot;:&quot;&quot;,&quot;isReverse&quot;:&quot;&quot;},&quot;contextVariables&quot;:[{&quot;name&quot;:&quot;recordId&quot;,&quot;val&quot;:&quot;0069O00000UL5TFQA1&quot;,&quot;id&quot;:2},{&quot;name&quot;:&quot;accountId&quot;,&quot;val&quot;:&quot;0019X00001CrPH2QAN&quot;,&quot;id&quot;:14}]},&quot;title&quot;:&quot;NewOppCmpLyt&quot;,&quot;enableLwc&quot;:true,&quot;isFlex&quot;:true,&quot;theme&quot;:&quot;slds&quot;,&quot;selectableMode&quot;:&quot;Multi&quot;,&quot;lwc&quot;:{&quot;DeveloperName&quot;:&quot;cfST_NoteTrattativa_3_Unipolsai&quot;,&quot;Id&quot;:&quot;0Rb9O000003FFkzSAG&quot;,&quot;MasterLabel&quot;:&quot;cfST_NoteTrattativa_3_Unipolsai&quot;,&quot;NamespacePrefix&quot;:&quot;c&quot;,&quot;ManageableState&quot;:&quot;unmanaged&quot;},&quot;xmlObject&quot;:{&quot;apiVersion&quot;:56,&quot;targetConfigs&quot;:&quot;PHRhcmdldENvbmZpZyB0YXJnZXRzPSJsaWdodG5pbmdfX1JlY29yZFBhZ2UiPjxwcm9wZXJ0eSBuYW1lPSJkZWJ1ZyIgdHlwZT0iQm9vbGVhbiI+PC9wcm9wZXJ0eT48cHJvcGVydHkgbmFtZT0icmVjb3JkSWQiIHR5cGU9IlN0cmluZyI+PC9wcm9wZXJ0eT48L3RhcmdldENvbmZpZz48dGFyZ2V0Q29uZmlnIHRhcmdldHM9ImxpZ2h0bmluZ19fQXBwUGFnZSI+PHByb3BlcnR5IG5hbWU9ImRlYnVnIiB0eXBlPSJCb29sZWFuIj48L3Byb3BlcnR5Pjxwcm9wZXJ0eSBuYW1lPSJyZWNvcmRJZCIgdHlwZT0iU3RyaW5nIj48L3Byb3BlcnR5PjwvdGFyZ2V0Q29uZmlnPg==&quot;,&quot;isExplicitImport&quot;:false,&quot;isExposed&quot;:true,&quot;id&quot;:&quot;0Rb9Q000002RmYrSAK&quot;,&quot;masterLabel&quot;:&quot;NewOppCmpLyt&quot;,&quot;targets&quot;:{&quot;target&quot;:[&quot;lightning__RecordPage&quot;,&quot;lightning__AppPage&quot;,&quot;lightning__HomePage&quot;]},&quot;description&quot;:&quot;&quot;,&quot;runtimeNamespace&quot;:&quot;&quot;},&quot;xmlJson&quot;:[{&quot;@attributes&quot;:{&quot;targets&quot;:&quot;lightning__RecordPage&quot;},&quot;property&quot;:[{&quot;@attributes&quot;:{&quot;name&quot;:&quot;debug&quot;,&quot;type&quot;:&quot;Boolean&quot;}},{&quot;@attributes&quot;:{&quot;name&quot;:&quot;recordId&quot;,&quot;type&quot;:&quot;String&quot;}}]},{&quot;@attributes&quot;:{&quot;targets&quot;:&quot;lightning__AppPage&quot;},&quot;property&quot;:[{&quot;@attributes&quot;:{&quot;name&quot;:&quot;debug&quot;,&quot;type&quot;:&quot;Boolean&quot;}},{&quot;@attributes&quot;:{&quot;name&quot;:&quot;recordId&quot;,&quot;type&quot;:&quot;String&quot;}}]}],&quot;globalCSS&quot;:false,&quot;events&quot;:[{&quot;eventname&quot;:&quot;refreshView&quot;,&quot;channelname&quot;:&quot;NewOppCmpLyt&quot;,&quot;element&quot;:&quot;action&quot;,&quot;eventtype&quot;:&quot;event&quot;,&quot;recordIndex&quot;:&quot;0&quot;,&quot;actionList&quot;:[{&quot;key&quot;:&quot;1748957495464-hapzrpka2&quot;,&quot;label&quot;:&quot;Action&quot;,&quot;draggable&quot;:false,&quot;isOpen&quot;:true,&quot;card&quot;:&quot;{card}&quot;,&quot;stateAction&quot;:{&quot;id&quot;:&quot;flex-action-1748958908811&quot;,&quot;type&quot;:&quot;cardAction&quot;,&quot;displayName&quot;:&quot;Action&quot;,&quot;vlocityIcon&quot;:&quot;standard-default&quot;,&quot;targetType&quot;:&quot;Web Page&quot;,&quot;openUrlIn&quot;:&quot;Current Window&quot;,&quot;Web Page&quot;:{&quot;targetName&quot;:&quot;/apex&quot;},&quot;eventName&quot;:&quot;reload&quot;},&quot;actionIndex&quot;:0}],&quot;showSpinner&quot;:&quot;false&quot;,&quot;key&quot;:&quot;event-0&quot;,&quot;displayLabel&quot;:&quot;refreshView&quot;,&quot;eventLabel&quot;:&quot;custom event&quot;}],&quot;sessionVars&quot;:[]}</propertySetConfig>
    <sampleDataSourceResponse>{&quot;countProductChildOpp&quot;:0,&quot;isMultiParent&quot;:true,&quot;checkCp&quot;:{&quot;allPermission&quot;:true,&quot;anyPermission&quot;:true,&quot;customerPermissionCheck&quot;:{&quot;RicezioneAssegnazioneLead&quot;:true,&quot;OperativitaAttivitaNonAssegnate&quot;:true},&quot;errorCode&quot;:&quot;INVOKE-200&quot;,&quot;error&quot;:&quot;OK&quot;},&quot;assignedUserId&quot;:&quot;0059X00000PXCK9QAP&quot;,&quot;name&quot;:&quot;UNI0000003527&quot;,&quot;secondAssignment&quot;:&quot;-&quot;,&quot;createdDate&quot;:&quot;2025-07-16T08:22:18.000Z&quot;,&quot;multiSplit&quot;:false,&quot;accountId&quot;:&quot;0019X00001CrPH2QAN&quot;,&quot;oppAmount&quot;:142.69,&quot;hasMultipleAreasFormula&quot;:false,&quot;multiCloseType&quot;:&quot;Tutte&quot;,&quot;adminProfileId&quot;:&quot;00e7Q000004Nir1QAC&quot;,&quot;multiAutoAssignment&quot;:false,&quot;areaOfneedsformula&quot;:&quot;&lt;img src=\&quot;/resource/areas_of_need/areas_of_need/on/casa.png\&quot; alt=\&quot;Casa\&quot; style=\&quot;height:20px; width:20px;\&quot; border=\&quot;0\&quot;/&gt;&quot;,&quot;contactChannel&quot;:&quot;Agenzia&quot;,&quot;multiDevName&quot;:&quot;Omnicanale&quot;,&quot;overallareasofneed&quot;:&quot;Casa&quot;,&quot;nameandchannel&quot;:&quot;UNI0000003527 - Preventivatore digitale Unica&quot;,&quot;multiAllowedCompanies&quot;:&quot;unipolsai&quot;,&quot;multiAutoClosing&quot;:true,&quot;temperature&quot;:&quot;&lt;img src=\&quot;/resource/temperature/temp2hot.png\&quot; alt=\&quot;calda\&quot; style=\&quot;height:30px; width:60px;\&quot; border=\&quot;0\&quot;/&gt; Calda&quot;,&quot;multiMatchProds&quot;:&quot;\&quot;UNICA\&quot;;\&quot;ESSIG-AUTO\&quot;;\&quot;ESSIG-RE\&quot;&quot;,&quot;engpoint&quot;:&quot;Digitale Unica&quot;,&quot;multiAutoOpen&quot;:true,&quot;stageName&quot;:&quot;In gestione&quot;,&quot;assignedGroupName&quot;:&quot;-&quot;,&quot;producType&quot;:&quot;-&quot;,&quot;multiManualClosing&quot;:true,&quot;assignedTo&quot;:&quot;0059X00000PXCK9QAP&quot;,&quot;closeSubStatus&quot;:&quot;-&quot;,&quot;multiMerge&quot;:false,&quot;channel&quot;:&quot;Preventivatore digitale Unica&quot;,&quot;isSameAgency&quot;:false,&quot;accountName&quot;:&quot;ARTURO BISSOLATI&quot;}</sampleDataSourceResponse>
    <stylingConfiguration>{&quot;customStyles&quot;:&quot;.tight-buttons-container {\r\n    display: flex;\r\n    flex: none;\r\n    width: auto;\r\n    margin: 0px  2px;\r\n}\r\n\r\n.tight-buttons-container button,\r\n.tight-buttons-container button span {\r\n    flex: 1 1 auto; \r\n    max-width: 100%;\r\n    padding: 5px 10px; \r\n    text-overflow: ellipsis;\r\n    overflow: hidden;\r\n    text-wrap: nowrap;\r\n    font-size: 14px;\r\n}\r\n\r\n.tight-columns-container {\r\n    display: flex;\r\n    flex: none;\r\n    width: auto;\r\n    margin: 0px  24px;\r\n}\r\n&quot;}</stylingConfiguration>
    <versionNumber>48</versionNumber>
</OmniUiCard>
