<?xml version="1.0" encoding="UTF-8"?>
<OmniUiCard xmlns="http://soap.sforce.com/2006/04/metadata">
    <authorName>Unipolsai</authorName>
    <clonedFromOmniUiCardKey>UniProdottiAssicurativi/Unipolsai/12.0</clonedFromOmniUiCardKey>
    <dataSourceConfig>{&quot;dataSource&quot;:{&quot;type&quot;:&quot;IntegrationProcedures&quot;,&quot;value&quot;:{&quot;dsDelay&quot;:&quot;&quot;,&quot;ipMethod&quot;:&quot;UniDS_ProdottiAssicurativi&quot;,&quot;vlocityAsync&quot;:false,&quot;inputMap&quot;:{&quot;AccountId&quot;:&quot;{recordId}&quot;,&quot;UserId&quot;:&quot;{userId}&quot;,&quot;showOtherAgency&quot;:&quot;true&quot;},&quot;jsonMap&quot;:&quot;{\&quot;recordId\&quot;:\&quot;{recordId}\&quot;,\&quot;userId\&quot;:\&quot;{userId}\&quot;,\&quot;showOtherAgency\&quot;:\&quot;{showOtherAgency}\&quot;}&quot;,&quot;resultVar&quot;:&quot;&quot;},&quot;orderBy&quot;:{&quot;name&quot;:&quot;&quot;,&quot;isReverse&quot;:&quot;&quot;},&quot;contextVariables&quot;:[{&quot;name&quot;:&quot;recordId&quot;,&quot;val&quot;:&quot;0019X00000tIvmXQAS&quot;,&quot;id&quot;:1},{&quot;name&quot;:&quot;userId&quot;,&quot;val&quot;:&quot;0059X00000Jr08cQAB&quot;,&quot;id&quot;:3},{&quot;name&quot;:&quot;showOtherAgency&quot;,&quot;val&quot;:&quot;true&quot;,&quot;id&quot;:53}]}}</dataSourceConfig>
    <description>Card used for showing Prodotti Presso altra agenzia</description>
    <isActive>false</isActive>
    <isManagedUsingStdDesigner>false</isManagedUsingStdDesigner>
    <name>UniProdottiAssicurativiAltraAgenzia</name>
    <omniUiCardType>Parent</omniUiCardType>
    <propertySetConfig>{&quot;states&quot;:[{&quot;fields&quot;:[],&quot;conditions&quot;:{&quot;id&quot;:&quot;state-condition-object&quot;,&quot;isParent&quot;:true,&quot;group&quot;:[{&quot;id&quot;:&quot;state-new-condition-28&quot;,&quot;field&quot;:&quot;Session.showOtherAgency&quot;,&quot;operator&quot;:&quot;==&quot;,&quot;value&quot;:&quot;true&quot;,&quot;type&quot;:&quot;custom&quot;,&quot;hasMergeField&quot;:false}]},&quot;definedActions&quot;:{&quot;actions&quot;:[]},&quot;name&quot;:&quot;OtherAgency&quot;,&quot;isSmartAction&quot;:false,&quot;smartAction&quot;:{},&quot;styleObject&quot;:{&quot;padding&quot;:[{&quot;type&quot;:&quot;around&quot;,&quot;size&quot;:&quot;x-small&quot;}],&quot;margin&quot;:[{&quot;type&quot;:&quot;around&quot;,&quot;size&quot;:&quot;none&quot;}],&quot;container&quot;:{&quot;class&quot;:&quot;slds-card&quot;},&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;12&quot;},&quot;sizeClass&quot;:&quot;slds-size_12-of-12&quot;,&quot;class&quot;:&quot;slds-card slds-p-around_x-small slds-m-bottom_x-small&quot;},&quot;components&quot;:{&quot;layer-0&quot;:{&quot;children&quot;:[{&quot;name&quot;:&quot;Block&quot;,&quot;element&quot;:&quot;block&quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;12&quot;},&quot;stateIndex&quot;:0,&quot;class&quot;:&quot;slds-col &quot;,&quot;property&quot;:{&quot;label&quot;:&quot;Block&quot;,&quot;collapsible&quot;:false,&quot;record&quot;:&quot;{record}&quot;,&quot;collapsedByDefault&quot;:false,&quot;card&quot;:&quot;{card}&quot;},&quot;type&quot;:&quot;block&quot;,&quot;styleObject&quot;:{&quot;padding&quot;:[{&quot;type&quot;:&quot;around&quot;,&quot;size&quot;:&quot;x-small&quot;,&quot;label&quot;:&quot;around:x-small&quot;}],&quot;class&quot;:&quot;slds-border_top slds-border_right slds-border_left slds-p-around_x-small &quot;,&quot;sizeClass&quot;:&quot;slds-size_12-of-12 &quot;,&quot;margin&quot;:[],&quot;background&quot;:{&quot;color&quot;:&quot;#f3f3f3&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;12&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:[&quot;border_top&quot;,&quot;border_right&quot;,&quot;border_left&quot;],&quot;width&quot;:&quot;1&quot;,&quot;color&quot;:&quot;#E5E5E5&quot;,&quot;radius&quot;:&quot;5px 5px 0 0&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{},&quot;text&quot;:{&quot;align&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;&quot;,&quot;style&quot;:&quot;background-color:#f3f3f3;     border-top: #E5E5E5 1px solid;border-right: #E5E5E5 1px solid;border-left: #E5E5E5 1px solid; \n    border-radius:5px 5px 0 0;     &quot;},&quot;children&quot;:[{&quot;name&quot;:&quot;Icon&quot;,&quot;element&quot;:&quot;flexIcon&quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;1&quot;},&quot;stateIndex&quot;:1,&quot;class&quot;:&quot;slds-col &quot;,&quot;property&quot;:{&quot;record&quot;:&quot;{record}&quot;,&quot;card&quot;:&quot;{card}&quot;,&quot;iconType&quot;:&quot;Salesforce SVG&quot;,&quot;iconName&quot;:&quot;standard:opportunity&quot;,&quot;size&quot;:&quot;medium&quot;,&quot;extraclass&quot;:&quot;slds-icon_container slds-icon-standard-opportunity&quot;,&quot;variant&quot;:&quot;inverse&quot;,&quot;imgsrc&quot;:&quot;&quot;},&quot;type&quot;:&quot;element&quot;,&quot;styleObject&quot;:{&quot;sizeClass&quot;:&quot;slds-size_1-of-12 &quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;1&quot;},&quot;padding&quot;:[],&quot;margin&quot;:[],&quot;background&quot;:{&quot;color&quot;:&quot;&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:&quot;&quot;,&quot;width&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;,&quot;radius&quot;:&quot;&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{},&quot;text&quot;:{&quot;align&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;&quot;,&quot;customClass&quot;:&quot;CustomIcon&quot;,&quot;class&quot;:&quot;CustomIcon&quot;,&quot;style&quot;:&quot;      \n         &quot;},&quot;elementLabel&quot;:&quot;Block-0-Icon-1&quot;,&quot;styleObjects&quot;:[{&quot;key&quot;:0,&quot;conditions&quot;:&quot;default&quot;,&quot;styleObject&quot;:{&quot;sizeClass&quot;:&quot;slds-size_1-of-12 &quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;1&quot;},&quot;padding&quot;:[],&quot;margin&quot;:[],&quot;background&quot;:{&quot;color&quot;:&quot;&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:&quot;&quot;,&quot;width&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;,&quot;radius&quot;:&quot;&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{},&quot;text&quot;:{&quot;align&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;&quot;,&quot;customClass&quot;:&quot;CustomIcon&quot;,&quot;class&quot;:&quot;CustomIcon&quot;,&quot;style&quot;:&quot;      \n         &quot;},&quot;label&quot;:&quot;Default&quot;,&quot;name&quot;:&quot;Default&quot;,&quot;conditionString&quot;:&quot;&quot;,&quot;draggable&quot;:false}],&quot;key&quot;:&quot;element_element_block_0_1_flexIcon_0_1&quot;,&quot;parentElementKey&quot;:&quot;element_block_0_1&quot;},{&quot;name&quot;:&quot;Text&quot;,&quot;element&quot;:&quot;outputField&quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;5&quot;},&quot;stateIndex&quot;:1,&quot;class&quot;:&quot;slds-col &quot;,&quot;property&quot;:{&quot;record&quot;:&quot;{record}&quot;,&quot;mergeField&quot;:&quot;%3Cdiv%20class=%22slds-text-align_left%22%3E%0A%3Cdiv%20class=%22slds-text-align_left%22%3E%0A%3Cdiv%20class=%22slds-text-align_left%22%3E%3Cspan%20style=%22font-size:%2014pt;%22%3E%3Cstrong%20class=%22slds-text-heading_medium%22%3EProdotti%20Assicurativi%20(%7BotherAgencyCount%7D)%3C/strong%3E%3C/span%3E%3C/div%3E%0A%3C/div%3E%0A%3C/div%3E&quot;,&quot;card&quot;:&quot;{card}&quot;},&quot;type&quot;:&quot;text&quot;,&quot;styleObject&quot;:{&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;5&quot;},&quot;sizeClass&quot;:&quot;slds-size_5-of-12 &quot;,&quot;padding&quot;:[],&quot;margin&quot;:[],&quot;background&quot;:{&quot;color&quot;:&quot;&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:&quot;&quot;,&quot;width&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;,&quot;radius&quot;:&quot;&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{},&quot;text&quot;:{&quot;align&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;&quot;,&quot;class&quot;:&quot;&quot;,&quot;style&quot;:&quot;      \n         &quot;},&quot;elementLabel&quot;:&quot;Block-0-Text-2&quot;,&quot;styleObjects&quot;:[{&quot;key&quot;:0,&quot;conditions&quot;:&quot;default&quot;,&quot;styleObject&quot;:{&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;5&quot;},&quot;sizeClass&quot;:&quot;slds-size_5-of-12 &quot;,&quot;padding&quot;:[],&quot;margin&quot;:[],&quot;background&quot;:{&quot;color&quot;:&quot;&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:&quot;&quot;,&quot;width&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;,&quot;radius&quot;:&quot;&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{},&quot;text&quot;:{&quot;align&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;&quot;,&quot;class&quot;:&quot;&quot;,&quot;style&quot;:&quot;      \n         &quot;},&quot;label&quot;:&quot;Default&quot;,&quot;name&quot;:&quot;Default&quot;,&quot;conditionString&quot;:&quot;&quot;,&quot;draggable&quot;:false}],&quot;key&quot;:&quot;element_element_block_0_1_outputField_1_1&quot;,&quot;parentElementKey&quot;:&quot;element_block_0_1&quot;}],&quot;elementLabel&quot;:&quot;Block-1&quot;,&quot;styleObjects&quot;:[{&quot;key&quot;:0,&quot;conditions&quot;:&quot;default&quot;,&quot;styleObject&quot;:{&quot;padding&quot;:[{&quot;type&quot;:&quot;around&quot;,&quot;size&quot;:&quot;x-small&quot;,&quot;label&quot;:&quot;around:x-small&quot;}],&quot;class&quot;:&quot;slds-border_top slds-border_right slds-border_left slds-p-around_x-small &quot;,&quot;sizeClass&quot;:&quot;slds-size_12-of-12 &quot;,&quot;margin&quot;:[],&quot;background&quot;:{&quot;color&quot;:&quot;#f3f3f3&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;12&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:[&quot;border_top&quot;,&quot;border_right&quot;,&quot;border_left&quot;],&quot;width&quot;:&quot;1&quot;,&quot;color&quot;:&quot;#E5E5E5&quot;,&quot;radius&quot;:&quot;5px 5px 0 0&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{},&quot;text&quot;:{&quot;align&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;&quot;,&quot;style&quot;:&quot;background-color:#f3f3f3;     border-top: #E5E5E5 1px solid;border-right: #E5E5E5 1px solid;border-left: #E5E5E5 1px solid; \n    border-radius:5px 5px 0 0;     &quot;},&quot;label&quot;:&quot;Default&quot;,&quot;name&quot;:&quot;Default&quot;,&quot;conditionString&quot;:&quot;&quot;,&quot;draggable&quot;:false}]},{&quot;name&quot;:&quot;Block&quot;,&quot;element&quot;:&quot;block&quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;12&quot;},&quot;stateIndex&quot;:0,&quot;class&quot;:&quot;slds-col &quot;,&quot;property&quot;:{&quot;label&quot;:&quot;Block&quot;,&quot;collapsible&quot;:false,&quot;record&quot;:&quot;{record}&quot;,&quot;collapsedByDefault&quot;:false,&quot;card&quot;:&quot;{card}&quot;},&quot;type&quot;:&quot;block&quot;,&quot;styleObject&quot;:{&quot;padding&quot;:[{&quot;type&quot;:&quot;around&quot;,&quot;size&quot;:&quot;x-small&quot;,&quot;label&quot;:&quot;around:x-small&quot;}],&quot;class&quot;:&quot;slds-border_right slds-border_left slds-p-around_x-small &quot;,&quot;sizeClass&quot;:&quot;slds-size_12-of-12 &quot;,&quot;margin&quot;:[],&quot;background&quot;:{&quot;color&quot;:&quot;&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;12&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:[&quot;border_right&quot;,&quot;border_left&quot;],&quot;width&quot;:&quot;1&quot;,&quot;color&quot;:&quot;#E5E5E5&quot;,&quot;radius&quot;:&quot;&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{},&quot;text&quot;:{&quot;align&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;&quot;,&quot;style&quot;:&quot;     border-right: #E5E5E5 1px solid;border-left: #E5E5E5 1px solid; \n         &quot;},&quot;children&quot;:[{&quot;key&quot;:&quot;element_element_block_1_0_outputField_0_0&quot;,&quot;name&quot;:&quot;Text&quot;,&quot;element&quot;:&quot;outputField&quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;4&quot;},&quot;stateIndex&quot;:0,&quot;class&quot;:&quot;slds-col &quot;,&quot;property&quot;:{&quot;record&quot;:&quot;{record}&quot;,&quot;mergeField&quot;:&quot;%3Cdiv%3E&amp;nbsp;%20&amp;nbsp;%E2%9C%94%20Unica%20presente%20tra%20i%20prodotti%3C/div%3E&quot;,&quot;card&quot;:&quot;{card}&quot;,&quot;data-conditions&quot;:{&quot;id&quot;:&quot;state-condition-object&quot;,&quot;isParent&quot;:true,&quot;group&quot;:[{&quot;id&quot;:&quot;state-new-condition-0&quot;,&quot;field&quot;:&quot;response.unicaAgency&quot;,&quot;operator&quot;:&quot;&gt;&quot;,&quot;value&quot;:&quot;0&quot;,&quot;type&quot;:&quot;custom&quot;,&quot;hasMergeField&quot;:false}]},&quot;data-preloadConditionalElement&quot;:true},&quot;type&quot;:&quot;text&quot;,&quot;styleObject&quot;:{&quot;sizeClass&quot;:&quot;slds-size_4-of-12 &quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;4&quot;}},&quot;parentElementKey&quot;:&quot;element_block_1_0&quot;,&quot;elementLabel&quot;:&quot;Block-1-Text-0&quot;}],&quot;elementLabel&quot;:&quot;Block-2&quot;,&quot;styleObjects&quot;:[{&quot;key&quot;:0,&quot;conditions&quot;:&quot;default&quot;,&quot;styleObject&quot;:{&quot;padding&quot;:[{&quot;type&quot;:&quot;around&quot;,&quot;size&quot;:&quot;x-small&quot;,&quot;label&quot;:&quot;around:x-small&quot;}],&quot;class&quot;:&quot;slds-border_right slds-border_left slds-p-around_x-small &quot;,&quot;sizeClass&quot;:&quot;slds-size_12-of-12 &quot;,&quot;margin&quot;:[],&quot;background&quot;:{&quot;color&quot;:&quot;&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;12&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:[&quot;border_right&quot;,&quot;border_left&quot;],&quot;width&quot;:&quot;1&quot;,&quot;color&quot;:&quot;#E5E5E5&quot;,&quot;radius&quot;:&quot;&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{},&quot;text&quot;:{&quot;align&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;&quot;,&quot;style&quot;:&quot;     border-right: #E5E5E5 1px solid;border-left: #E5E5E5 1px solid; \n         &quot;},&quot;label&quot;:&quot;Default&quot;,&quot;name&quot;:&quot;Default&quot;,&quot;conditionString&quot;:&quot;&quot;,&quot;draggable&quot;:false}]},{&quot;name&quot;:&quot;Block&quot;,&quot;element&quot;:&quot;block&quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;12&quot;},&quot;stateIndex&quot;:0,&quot;class&quot;:&quot;slds-col &quot;,&quot;property&quot;:{&quot;label&quot;:&quot;Block&quot;,&quot;collapsible&quot;:false,&quot;record&quot;:&quot;{record}&quot;,&quot;collapsedByDefault&quot;:false,&quot;card&quot;:&quot;{card}&quot;},&quot;type&quot;:&quot;block&quot;,&quot;styleObject&quot;:{&quot;padding&quot;:[{&quot;type&quot;:&quot;around&quot;,&quot;size&quot;:&quot;x-small&quot;,&quot;label&quot;:&quot;around:x-small&quot;}],&quot;class&quot;:&quot;slds-border_right slds-border_left slds-p-around_x-small &quot;,&quot;sizeClass&quot;:&quot;slds-size_12-of-12 &quot;,&quot;margin&quot;:[],&quot;background&quot;:{&quot;color&quot;:&quot;&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;12&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:[&quot;border_right&quot;,&quot;border_left&quot;],&quot;width&quot;:&quot;1&quot;,&quot;color&quot;:&quot;#E5E5E5&quot;,&quot;radius&quot;:&quot;&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{},&quot;text&quot;:{&quot;align&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;&quot;,&quot;style&quot;:&quot;     border-right: #E5E5E5 1px solid;border-left: #E5E5E5 1px solid; \n         &quot;},&quot;children&quot;:[{&quot;key&quot;:&quot;element_element_block_2_0_block_0_0&quot;,&quot;name&quot;:&quot;Block&quot;,&quot;element&quot;:&quot;block&quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;2&quot;},&quot;stateIndex&quot;:0,&quot;class&quot;:&quot;slds-col &quot;,&quot;property&quot;:{&quot;label&quot;:&quot;Block&quot;,&quot;collapsible&quot;:false,&quot;record&quot;:&quot;{record}&quot;,&quot;collapsedByDefault&quot;:false,&quot;card&quot;:&quot;{card}&quot;,&quot;data-conditions&quot;:{&quot;id&quot;:&quot;state-condition-object&quot;,&quot;isParent&quot;:true,&quot;group&quot;:[{&quot;id&quot;:&quot;state-new-condition-2&quot;,&quot;field&quot;:&quot;prodottiAssicurativi.other_agency.motor.count&quot;,&quot;operator&quot;:&quot;!=&quot;,&quot;value&quot;:&quot;0&quot;,&quot;type&quot;:&quot;custom&quot;,&quot;hasMergeField&quot;:false}]}},&quot;type&quot;:&quot;block&quot;,&quot;styleObject&quot;:{&quot;padding&quot;:[{&quot;type&quot;:&quot;around&quot;,&quot;size&quot;:&quot;x-small&quot;,&quot;label&quot;:&quot;around:x-small&quot;}],&quot;class&quot;:&quot;slds-border_top slds-border_right slds-border_bottom slds-border_left slds-p-around_x-small &quot;,&quot;sizeClass&quot;:&quot;slds-size_2-of-12 &quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;2&quot;},&quot;margin&quot;:[],&quot;background&quot;:{&quot;color&quot;:&quot;&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:[&quot;border_top&quot;,&quot;border_right&quot;,&quot;border_bottom&quot;,&quot;border_left&quot;],&quot;width&quot;:&quot;1px&quot;,&quot;color&quot;:&quot;#C9C9C9&quot;,&quot;radius&quot;:&quot;4px&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{},&quot;text&quot;:{&quot;align&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;margin-right:12px;&quot;,&quot;style&quot;:&quot;     border-top: #C9C9C9 1pxpx solid;border-right: #C9C9C9 1pxpx solid;border-bottom: #C9C9C9 1pxpx solid;border-left: #C9C9C9 1pxpx solid; \n    border-radius:4px; height:110px; min-height:110px; max-height:220px;  margin-right:12px;&quot;,&quot;height&quot;:&quot;110px&quot;,&quot;minHeight&quot;:&quot;110px&quot;,&quot;maxHeight&quot;:&quot;220px&quot;},&quot;children&quot;:[{&quot;key&quot;:&quot;element_element_element_block_2_1_block_0_1_flexImg_0_1&quot;,&quot;name&quot;:&quot;Image&quot;,&quot;element&quot;:&quot;flexImg&quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;12&quot;},&quot;stateIndex&quot;:1,&quot;class&quot;:&quot;slds-col &quot;,&quot;property&quot;:{&quot;record&quot;:&quot;{record}&quot;,&quot;card&quot;:&quot;{card}&quot;,&quot;size&quot;:&quot;&quot;,&quot;extraclass&quot;:&quot;slds-align_absolute-center&quot;,&quot;stateImg&quot;:{&quot;imgsrc&quot;:&quot;/resource/images_quote_domains/domain_img/Ruota_Unipol.jpg&quot;,&quot;alternativeText&quot;:&quot;Image description&quot;,&quot;document&quot;:{&quot;Id&quot;:&quot;0689X000006z4KzQAI&quot;,&quot;ContentDocumentId&quot;:&quot;0699X000006o2ftQAA&quot;,&quot;Title&quot;:&quot;Ruota_Unipol&quot;,&quot;VersionNumber&quot;:&quot;1&quot;,&quot;FileType&quot;:&quot;JPG&quot;,&quot;FileExtension&quot;:&quot;jpg&quot;,&quot;IsAssetEnabled&quot;:false,&quot;PublishStatus&quot;:&quot;P&quot;,&quot;title&quot;:&quot;Ruota_Unipol&quot;,&quot;attachmentType&quot;:&quot;ContentVersion&quot;}},&quot;imgWidth&quot;:&quot;50px&quot;,&quot;action&quot;:{&quot;label&quot;:&quot;Action&quot;,&quot;iconName&quot;:&quot;standard-default&quot;,&quot;eventType&quot;:&quot;onclick&quot;,&quot;actionList&quot;:[{&quot;key&quot;:&quot;1744099770909-z0fc7vwio&quot;,&quot;label&quot;:&quot;Action&quot;,&quot;draggable&quot;:false,&quot;isOpen&quot;:true,&quot;card&quot;:&quot;{card}&quot;,&quot;stateAction&quot;:{&quot;id&quot;:&quot;flex-action-1750238512520&quot;,&quot;type&quot;:&quot;Custom&quot;,&quot;displayName&quot;:&quot;Action&quot;,&quot;vlocityIcon&quot;:&quot;standard-default&quot;,&quot;targetType&quot;:&quot;Navigation Item&quot;,&quot;openUrlIn&quot;:&quot;New Tab/Window&quot;,&quot;Navigation Item&quot;:{&quot;targetName&quot;:&quot;Prodotti_Presso_Altra_Agenzia&quot;},&quot;hasExtraParams&quot;:true,&quot;targetParams&quot;:{&quot;c__selectedTab&quot;:&quot;motor&quot;,&quot;c__recordId&quot;:&quot;{recordId}&quot;,&quot;c__agency&quot;:&quot;other&quot;},&quot;actionConditions&quot;:{&quot;id&quot;:&quot;state-condition-object&quot;,&quot;isParent&quot;:true,&quot;group&quot;:[]}},&quot;actionIndex&quot;:0}],&quot;showSpinner&quot;:&quot;false&quot;}},&quot;type&quot;:&quot;element&quot;,&quot;styleObject&quot;:{&quot;sizeClass&quot;:&quot;slds-size_12-of-12&quot;},&quot;parentElementKey&quot;:&quot;element_element_block_2_1_block_0_1&quot;,&quot;elementLabel&quot;:&quot;Block-3-Block-0-Image-0&quot;},{&quot;key&quot;:&quot;element_element_element_block_2_1_block_0_1_outputField_1_1&quot;,&quot;name&quot;:&quot;Text&quot;,&quot;element&quot;:&quot;outputField&quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;12&quot;},&quot;stateIndex&quot;:1,&quot;class&quot;:&quot;slds-col &quot;,&quot;property&quot;:{&quot;record&quot;:&quot;{record}&quot;,&quot;mergeField&quot;:&quot;%3Cdiv%20class=%22slds-text-align_center%22%3E%0A%3Cdiv%20class=%22slds-text-align_center%22%3E%0A%3Cdiv%20class=%22slds-text-align_center%22%3EMotor%20(%7BprodottiAssicurativi.other_agency.motor.count%7D%3Cspan%20data-teams=%22true%22%3E)%3C/span%3E%3C/div%3E%0A%3Cdiv%20class=%22slds-text-align_center%22%3E%3Cspan%20data-teams=%22true%22%3E%7BprodottiAssicurativi.other_agency.motor.premium_amount%7D%3C/span%3E%3C/div%3E%0A%3C/div%3E%0A%3C/div%3E&quot;,&quot;card&quot;:&quot;{card}&quot;},&quot;type&quot;:&quot;text&quot;,&quot;styleObject&quot;:{&quot;sizeClass&quot;:&quot;slds-size_12-of-12&quot;},&quot;parentElementKey&quot;:&quot;element_element_block_2_1_block_0_1&quot;,&quot;elementLabel&quot;:&quot;Block-3-Block-0-Text-1&quot;}],&quot;parentElementKey&quot;:&quot;element_block_2_0&quot;,&quot;elementLabel&quot;:&quot;Block-3-Block-0&quot;,&quot;styleObjects&quot;:[{&quot;key&quot;:0,&quot;conditions&quot;:&quot;default&quot;,&quot;styleObject&quot;:{&quot;padding&quot;:[{&quot;type&quot;:&quot;around&quot;,&quot;size&quot;:&quot;x-small&quot;,&quot;label&quot;:&quot;around:x-small&quot;}],&quot;class&quot;:&quot;slds-border_top slds-border_right slds-border_bottom slds-border_left slds-p-around_x-small &quot;,&quot;sizeClass&quot;:&quot;slds-size_2-of-12 &quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;2&quot;},&quot;margin&quot;:[],&quot;background&quot;:{&quot;color&quot;:&quot;&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:[&quot;border_top&quot;,&quot;border_right&quot;,&quot;border_bottom&quot;,&quot;border_left&quot;],&quot;width&quot;:&quot;1px&quot;,&quot;color&quot;:&quot;#C9C9C9&quot;,&quot;radius&quot;:&quot;4px&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{},&quot;text&quot;:{&quot;align&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;margin-right:12px;&quot;,&quot;style&quot;:&quot;     border-top: #C9C9C9 1pxpx solid;border-right: #C9C9C9 1pxpx solid;border-bottom: #C9C9C9 1pxpx solid;border-left: #C9C9C9 1pxpx solid; \n    border-radius:4px; height:110px; min-height:110px; max-height:220px;  margin-right:12px;&quot;,&quot;height&quot;:&quot;110px&quot;,&quot;minHeight&quot;:&quot;110px&quot;,&quot;maxHeight&quot;:&quot;220px&quot;},&quot;label&quot;:&quot;Default&quot;,&quot;name&quot;:&quot;Default&quot;,&quot;conditionString&quot;:&quot;&quot;,&quot;draggable&quot;:false}]},{&quot;name&quot;:&quot;Block&quot;,&quot;element&quot;:&quot;block&quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;3&quot;},&quot;stateIndex&quot;:0,&quot;class&quot;:&quot;slds-col &quot;,&quot;property&quot;:{&quot;label&quot;:&quot;Block&quot;,&quot;collapsible&quot;:false,&quot;record&quot;:&quot;{record}&quot;,&quot;collapsedByDefault&quot;:false,&quot;card&quot;:&quot;{card}&quot;,&quot;data-conditions&quot;:{&quot;id&quot;:&quot;state-condition-object&quot;,&quot;isParent&quot;:true,&quot;group&quot;:[{&quot;id&quot;:&quot;state-new-condition-0&quot;,&quot;field&quot;:&quot;prodottiAssicurativi.other_agency.casa_famiglia.count&quot;,&quot;operator&quot;:&quot;!=&quot;,&quot;value&quot;:&quot;0&quot;,&quot;type&quot;:&quot;custom&quot;,&quot;hasMergeField&quot;:false}]}},&quot;type&quot;:&quot;block&quot;,&quot;styleObject&quot;:{&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;3&quot;},&quot;padding&quot;:[{&quot;type&quot;:&quot;around&quot;,&quot;size&quot;:&quot;x-small&quot;,&quot;label&quot;:&quot;around:x-small&quot;}],&quot;class&quot;:&quot;slds-border_top slds-border_right slds-border_bottom slds-border_left slds-p-around_x-small &quot;,&quot;sizeClass&quot;:&quot;slds-size_3-of-12 &quot;,&quot;margin&quot;:[],&quot;background&quot;:{&quot;color&quot;:&quot;&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:[&quot;border_top&quot;,&quot;border_right&quot;,&quot;border_bottom&quot;,&quot;border_left&quot;],&quot;width&quot;:&quot;1&quot;,&quot;color&quot;:&quot;#C9C9C9&quot;,&quot;radius&quot;:&quot;4px&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{},&quot;text&quot;:{&quot;align&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;margin-right:12px;&quot;,&quot;height&quot;:&quot;110px&quot;,&quot;style&quot;:&quot;     border-top: #C9C9C9 1px solid;border-right: #C9C9C9 1px solid;border-bottom: #C9C9C9 1px solid;border-left: #C9C9C9 1px solid; \n    border-radius:4px; height:110px; min-height:110px; max-height:220px;  margin-right:12px;&quot;,&quot;minHeight&quot;:&quot;110px&quot;,&quot;maxHeight&quot;:&quot;220px&quot;},&quot;children&quot;:[{&quot;key&quot;:&quot;element_element_element_block_2_1_block_1_1_flexImg_0_1&quot;,&quot;name&quot;:&quot;Image&quot;,&quot;element&quot;:&quot;flexImg&quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;12&quot;},&quot;stateIndex&quot;:1,&quot;class&quot;:&quot;slds-col &quot;,&quot;property&quot;:{&quot;record&quot;:&quot;{record}&quot;,&quot;card&quot;:&quot;{card}&quot;,&quot;size&quot;:&quot;&quot;,&quot;extraclass&quot;:&quot;slds-align_absolute-center&quot;,&quot;stateImg&quot;:{&quot;imgsrc&quot;:&quot;/resource/images_quote_domains/domain_img/Casa_Unipol.jpg&quot;,&quot;alternativeText&quot;:&quot;Image description&quot;,&quot;document&quot;:{&quot;label&quot;:&quot;Casa_Unipol (Version:1)&quot;,&quot;value&quot;:&quot;/sfc/servlet.shepherd/version/download/0689X000006z4HlQAI&quot;,&quot;title&quot;:&quot;Casa_Unipol&quot;,&quot;Id&quot;:&quot;0689X000006z4HlQAI&quot;,&quot;attachmentType&quot;:&quot;ContentVersion&quot;}},&quot;imgHeight&quot;:&quot;50px&quot;,&quot;action&quot;:{&quot;label&quot;:&quot;Action&quot;,&quot;iconName&quot;:&quot;standard-default&quot;,&quot;eventType&quot;:&quot;onclick&quot;,&quot;actionList&quot;:[{&quot;key&quot;:&quot;1744099857102-ri9f26vaa&quot;,&quot;label&quot;:&quot;Action&quot;,&quot;draggable&quot;:false,&quot;isOpen&quot;:true,&quot;card&quot;:&quot;{card}&quot;,&quot;stateAction&quot;:{&quot;id&quot;:&quot;flex-action-1750238521186&quot;,&quot;type&quot;:&quot;Custom&quot;,&quot;displayName&quot;:&quot;Action&quot;,&quot;vlocityIcon&quot;:&quot;standard-default&quot;,&quot;targetType&quot;:&quot;Navigation Item&quot;,&quot;openUrlIn&quot;:&quot;New Tab/Window&quot;,&quot;Navigation Item&quot;:{&quot;targetName&quot;:&quot;Prodotti_Presso_Altra_Agenzia&quot;},&quot;hasExtraParams&quot;:true,&quot;targetParams&quot;:{&quot;c__selectedTab&quot;:&quot;casa&quot;,&quot;c__recordId&quot;:&quot;{recordId}&quot;,&quot;c__agency&quot;:&quot;other&quot;},&quot;actionConditions&quot;:{&quot;id&quot;:&quot;state-condition-object&quot;,&quot;isParent&quot;:true,&quot;group&quot;:[]}},&quot;actionIndex&quot;:0}],&quot;showSpinner&quot;:&quot;false&quot;}},&quot;type&quot;:&quot;element&quot;,&quot;styleObject&quot;:{&quot;sizeClass&quot;:&quot;slds-size_12-of-12&quot;},&quot;parentElementKey&quot;:&quot;element_element_block_2_1_block_1_1&quot;,&quot;elementLabel&quot;:&quot;Block-3-Block-2-Image-0&quot;},{&quot;key&quot;:&quot;element_element_element_block_2_1_block_1_1_outputField_1_1&quot;,&quot;name&quot;:&quot;Text&quot;,&quot;element&quot;:&quot;outputField&quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;12&quot;},&quot;stateIndex&quot;:1,&quot;class&quot;:&quot;slds-col &quot;,&quot;property&quot;:{&quot;record&quot;:&quot;{record}&quot;,&quot;mergeField&quot;:&quot;%3Cdiv%20class=%22slds-text-align_center%22%3E%0A%3Cdiv%20class=%22slds-text-align_center%22%3ECasa%20&amp;amp;%20Famiglia&amp;nbsp;%20(%7BprodottiAssicurativi.other_agency.casa_famiglia.count%7D%3Cspan%20data-teams=%22true%22%3E)%3C/span%3E%0A%3Cdiv%20class=%22slds-text-align_center%22%3E%7BprodottiAssicurativi.other_agency.casa_famiglia.premium_amount%7D%3C/div%3E%0A%3C/div%3E%0A%3C/div%3E&quot;,&quot;card&quot;:&quot;{card}&quot;},&quot;type&quot;:&quot;text&quot;,&quot;styleObject&quot;:{&quot;sizeClass&quot;:&quot;slds-size_12-of-12 &quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;12&quot;}},&quot;parentElementKey&quot;:&quot;element_element_block_2_1_block_1_1&quot;,&quot;elementLabel&quot;:&quot;Block-3-Block-2-Text-1&quot;}],&quot;elementLabel&quot;:&quot;Block-3-Block-2&quot;,&quot;styleObjects&quot;:[{&quot;key&quot;:0,&quot;conditions&quot;:&quot;default&quot;,&quot;styleObject&quot;:{&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;3&quot;},&quot;padding&quot;:[{&quot;type&quot;:&quot;around&quot;,&quot;size&quot;:&quot;x-small&quot;,&quot;label&quot;:&quot;around:x-small&quot;}],&quot;class&quot;:&quot;slds-border_top slds-border_right slds-border_bottom slds-border_left slds-p-around_x-small &quot;,&quot;sizeClass&quot;:&quot;slds-size_3-of-12 &quot;,&quot;margin&quot;:[],&quot;background&quot;:{&quot;color&quot;:&quot;&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:[&quot;border_top&quot;,&quot;border_right&quot;,&quot;border_bottom&quot;,&quot;border_left&quot;],&quot;width&quot;:&quot;1&quot;,&quot;color&quot;:&quot;#C9C9C9&quot;,&quot;radius&quot;:&quot;4px&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{},&quot;text&quot;:{&quot;align&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;margin-right:12px;&quot;,&quot;height&quot;:&quot;110px&quot;,&quot;style&quot;:&quot;     border-top: #C9C9C9 1px solid;border-right: #C9C9C9 1px solid;border-bottom: #C9C9C9 1px solid;border-left: #C9C9C9 1px solid; \n    border-radius:4px; height:110px; min-height:110px; max-height:220px;  margin-right:12px;&quot;,&quot;minHeight&quot;:&quot;110px&quot;,&quot;maxHeight&quot;:&quot;220px&quot;},&quot;label&quot;:&quot;Default&quot;,&quot;name&quot;:&quot;Default&quot;,&quot;conditionString&quot;:&quot;&quot;,&quot;draggable&quot;:false}],&quot;key&quot;:&quot;element_element_block_2_0_block_1_0&quot;,&quot;parentElementKey&quot;:&quot;element_block_2_0&quot;},{&quot;name&quot;:&quot;Block&quot;,&quot;element&quot;:&quot;block&quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;2&quot;},&quot;stateIndex&quot;:0,&quot;class&quot;:&quot;slds-col &quot;,&quot;property&quot;:{&quot;label&quot;:&quot;Block&quot;,&quot;collapsible&quot;:false,&quot;record&quot;:&quot;{record}&quot;,&quot;collapsedByDefault&quot;:false,&quot;card&quot;:&quot;{card}&quot;,&quot;data-conditions&quot;:{&quot;id&quot;:&quot;state-condition-object&quot;,&quot;isParent&quot;:true,&quot;group&quot;:[{&quot;id&quot;:&quot;state-new-condition-18&quot;,&quot;field&quot;:&quot;prodottiAssicurativi.other_agency.persona.count&quot;,&quot;operator&quot;:&quot;!=&quot;,&quot;value&quot;:&quot;0&quot;,&quot;type&quot;:&quot;custom&quot;,&quot;hasMergeField&quot;:false}]}},&quot;type&quot;:&quot;block&quot;,&quot;styleObject&quot;:{&quot;padding&quot;:[{&quot;type&quot;:&quot;around&quot;,&quot;size&quot;:&quot;x-small&quot;,&quot;label&quot;:&quot;around:x-small&quot;}],&quot;class&quot;:&quot;slds-border_top slds-border_right slds-border_bottom slds-border_left slds-p-around_x-small &quot;,&quot;sizeClass&quot;:&quot;slds-size_2-of-12 &quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;2&quot;},&quot;margin&quot;:[],&quot;background&quot;:{&quot;color&quot;:&quot;&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:[&quot;border_top&quot;,&quot;border_right&quot;,&quot;border_bottom&quot;,&quot;border_left&quot;],&quot;width&quot;:&quot;1&quot;,&quot;color&quot;:&quot;#C9C9C9&quot;,&quot;radius&quot;:&quot;4px&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{},&quot;text&quot;:{&quot;align&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;margin-right:12px;&quot;,&quot;height&quot;:&quot;110px&quot;,&quot;style&quot;:&quot;     border-top: #C9C9C9 1px solid;border-right: #C9C9C9 1px solid;border-bottom: #C9C9C9 1px solid;border-left: #C9C9C9 1px solid; \n    border-radius:4px; height:110px; min-height:110px; max-height:220px;  margin-right:12px;&quot;,&quot;minHeight&quot;:&quot;110px&quot;,&quot;maxHeight&quot;:&quot;220px&quot;},&quot;children&quot;:[{&quot;name&quot;:&quot;Image&quot;,&quot;element&quot;:&quot;flexImg&quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;12&quot;},&quot;stateIndex&quot;:1,&quot;class&quot;:&quot;slds-col &quot;,&quot;property&quot;:{&quot;record&quot;:&quot;{record}&quot;,&quot;card&quot;:&quot;{card}&quot;,&quot;size&quot;:&quot;&quot;,&quot;extraclass&quot;:&quot;slds-align_absolute-center&quot;,&quot;stateImg&quot;:{&quot;imgsrc&quot;:&quot;/resource/images_quote_domains/domain_img/Famiglia_Unipol.png&quot;,&quot;alternativeText&quot;:&quot;Image description&quot;,&quot;document&quot;:{&quot;label&quot;:&quot;Famiglia_Unipol (Version:1)&quot;,&quot;value&quot;:&quot;/sfc/servlet.shepherd/version/download/0689X000006z4JNQAY&quot;,&quot;title&quot;:&quot;Famiglia_Unipol&quot;,&quot;Id&quot;:&quot;0689X000006z4JNQAY&quot;,&quot;attachmentType&quot;:&quot;ContentVersion&quot;}},&quot;imgHeight&quot;:&quot;50px&quot;,&quot;action&quot;:{&quot;label&quot;:&quot;Action&quot;,&quot;iconName&quot;:&quot;standard-default&quot;,&quot;eventType&quot;:&quot;onclick&quot;,&quot;actionList&quot;:[{&quot;key&quot;:&quot;1744100007119-r1dbkqme7&quot;,&quot;label&quot;:&quot;Action&quot;,&quot;draggable&quot;:false,&quot;isOpen&quot;:true,&quot;card&quot;:&quot;{card}&quot;,&quot;stateAction&quot;:{&quot;id&quot;:&quot;flex-action-1750238530069&quot;,&quot;type&quot;:&quot;Custom&quot;,&quot;displayName&quot;:&quot;Action&quot;,&quot;vlocityIcon&quot;:&quot;standard-default&quot;,&quot;targetType&quot;:&quot;Navigation Item&quot;,&quot;openUrlIn&quot;:&quot;New Tab/Window&quot;,&quot;Navigation Item&quot;:{&quot;targetName&quot;:&quot;Prodotti_Presso_Altra_Agenzia&quot;},&quot;hasExtraParams&quot;:true,&quot;targetParams&quot;:{&quot;c__selectedTab&quot;:&quot;persona&quot;,&quot;c__recordId&quot;:&quot;{recordId}&quot;,&quot;c__agency&quot;:&quot;other&quot;},&quot;actionConditions&quot;:{&quot;id&quot;:&quot;state-condition-object&quot;,&quot;isParent&quot;:true,&quot;group&quot;:[]}},&quot;actionIndex&quot;:0}],&quot;showSpinner&quot;:&quot;false&quot;}},&quot;type&quot;:&quot;element&quot;,&quot;styleObject&quot;:{&quot;sizeClass&quot;:&quot;slds-size_12-of-12&quot;},&quot;elementLabel&quot;:&quot;Block-3-Block-3-Image-0&quot;,&quot;key&quot;:&quot;element_element_element_block_2_1_block_2_1_flexImg_0_1&quot;,&quot;parentElementKey&quot;:&quot;element_element_block_2_1_block_2_1&quot;},{&quot;key&quot;:&quot;element_element_element_block_2_1_block_2_1_outputField_1_1&quot;,&quot;name&quot;:&quot;Text&quot;,&quot;element&quot;:&quot;outputField&quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;12&quot;},&quot;stateIndex&quot;:1,&quot;class&quot;:&quot;slds-col &quot;,&quot;property&quot;:{&quot;record&quot;:&quot;{record}&quot;,&quot;mergeField&quot;:&quot;%3Cdiv%20class=%22slds-text-align_center%22%3E%0A%3Cdiv%20class=%22slds-text-align_center%22%3EPersona(%7BprodottiAssicurativi.other_agency.persona.count%7D)%3C/div%3E%0A%3Cdiv%20class=%22slds-text-align_center%22%3E%7BprodottiAssicurativi.other_agency.persona.premium_amount%7D%3C/div%3E%0A%3C/div%3E&quot;,&quot;card&quot;:&quot;{card}&quot;},&quot;type&quot;:&quot;text&quot;,&quot;styleObject&quot;:{&quot;sizeClass&quot;:&quot;slds-size_12-of-12&quot;},&quot;parentElementKey&quot;:&quot;element_element_block_2_1_block_2_1&quot;,&quot;elementLabel&quot;:&quot;Block-3-Block-3-Text-1&quot;}],&quot;elementLabel&quot;:&quot;Block-3-Block-3&quot;,&quot;styleObjects&quot;:[{&quot;key&quot;:0,&quot;conditions&quot;:&quot;default&quot;,&quot;styleObject&quot;:{&quot;padding&quot;:[{&quot;type&quot;:&quot;around&quot;,&quot;size&quot;:&quot;x-small&quot;,&quot;label&quot;:&quot;around:x-small&quot;}],&quot;class&quot;:&quot;slds-border_top slds-border_right slds-border_bottom slds-border_left slds-p-around_x-small &quot;,&quot;sizeClass&quot;:&quot;slds-size_2-of-12 &quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;2&quot;},&quot;margin&quot;:[],&quot;background&quot;:{&quot;color&quot;:&quot;&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:[&quot;border_top&quot;,&quot;border_right&quot;,&quot;border_bottom&quot;,&quot;border_left&quot;],&quot;width&quot;:&quot;1&quot;,&quot;color&quot;:&quot;#C9C9C9&quot;,&quot;radius&quot;:&quot;4px&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{},&quot;text&quot;:{&quot;align&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;margin-right:12px;&quot;,&quot;height&quot;:&quot;110px&quot;,&quot;style&quot;:&quot;     border-top: #C9C9C9 1px solid;border-right: #C9C9C9 1px solid;border-bottom: #C9C9C9 1px solid;border-left: #C9C9C9 1px solid; \n    border-radius:4px; height:110px; min-height:110px; max-height:220px;  margin-right:12px;&quot;,&quot;minHeight&quot;:&quot;110px&quot;,&quot;maxHeight&quot;:&quot;220px&quot;},&quot;label&quot;:&quot;Default&quot;,&quot;name&quot;:&quot;Default&quot;,&quot;conditionString&quot;:&quot;&quot;,&quot;draggable&quot;:false}],&quot;key&quot;:&quot;element_element_block_2_0_block_2_0&quot;,&quot;parentElementKey&quot;:&quot;element_block_2_0&quot;},{&quot;name&quot;:&quot;Block&quot;,&quot;element&quot;:&quot;block&quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;2&quot;},&quot;stateIndex&quot;:0,&quot;class&quot;:&quot;slds-col &quot;,&quot;property&quot;:{&quot;label&quot;:&quot;Block&quot;,&quot;collapsible&quot;:false,&quot;record&quot;:&quot;{record}&quot;,&quot;collapsedByDefault&quot;:false,&quot;card&quot;:&quot;{card}&quot;,&quot;data-conditions&quot;:{&quot;id&quot;:&quot;state-condition-object&quot;,&quot;isParent&quot;:true,&quot;group&quot;:[{&quot;id&quot;:&quot;state-new-condition-39&quot;,&quot;field&quot;:&quot;prodottiAssicurativi.other_agency.vita.count&quot;,&quot;operator&quot;:&quot;!=&quot;,&quot;value&quot;:&quot;0&quot;,&quot;type&quot;:&quot;custom&quot;,&quot;hasMergeField&quot;:false}]}},&quot;type&quot;:&quot;block&quot;,&quot;styleObject&quot;:{&quot;padding&quot;:[{&quot;type&quot;:&quot;around&quot;,&quot;size&quot;:&quot;x-small&quot;,&quot;label&quot;:&quot;around:x-small&quot;}],&quot;class&quot;:&quot;slds-border_top slds-border_right slds-border_bottom slds-border_left slds-p-around_x-small &quot;,&quot;sizeClass&quot;:&quot;slds-size_2-of-12 &quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;2&quot;},&quot;margin&quot;:[],&quot;background&quot;:{&quot;color&quot;:&quot;&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:[&quot;border_top&quot;,&quot;border_right&quot;,&quot;border_bottom&quot;,&quot;border_left&quot;],&quot;width&quot;:&quot;1&quot;,&quot;color&quot;:&quot;#C9C9C9&quot;,&quot;radius&quot;:&quot;4px&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{},&quot;text&quot;:{&quot;align&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;margin-right:12px;&quot;,&quot;height&quot;:&quot;110px&quot;,&quot;style&quot;:&quot;     border-top: #C9C9C9 1px solid;border-right: #C9C9C9 1px solid;border-bottom: #C9C9C9 1px solid;border-left: #C9C9C9 1px solid; \n    border-radius:4px; height:110px; min-height:110px; max-height:220px;  margin-right:12px;&quot;,&quot;minHeight&quot;:&quot;110px&quot;,&quot;maxHeight&quot;:&quot;220px&quot;},&quot;children&quot;:[{&quot;key&quot;:&quot;element_element_element_block_2_0_block_3_0_flexImg_0_0&quot;,&quot;name&quot;:&quot;Image&quot;,&quot;element&quot;:&quot;flexImg&quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;12&quot;},&quot;stateIndex&quot;:0,&quot;class&quot;:&quot;slds-col &quot;,&quot;property&quot;:{&quot;record&quot;:&quot;{record}&quot;,&quot;card&quot;:&quot;{card}&quot;,&quot;size&quot;:&quot;&quot;,&quot;extraclass&quot;:&quot;slds-align_absolute-center&quot;,&quot;stateImg&quot;:{&quot;imgsrc&quot;:&quot;/resource/images_quote_domains/domain_img/Ruota_Unipol.jpg&quot;,&quot;alternativeText&quot;:&quot;Image description&quot;,&quot;document&quot;:{&quot;label&quot;:&quot;Ruota_Unipol (Version:1)&quot;,&quot;value&quot;:&quot;/sfc/servlet.shepherd/version/download/0689X000006z4KzQAI&quot;,&quot;title&quot;:&quot;Ruota_Unipol&quot;,&quot;Id&quot;:&quot;0689X000006z4KzQAI&quot;,&quot;attachmentType&quot;:&quot;ContentVersion&quot;}},&quot;imgHeight&quot;:&quot;50px&quot;,&quot;action&quot;:{&quot;label&quot;:&quot;Action&quot;,&quot;iconName&quot;:&quot;standard-default&quot;,&quot;eventType&quot;:&quot;onclick&quot;,&quot;actionList&quot;:[{&quot;key&quot;:&quot;1744100089066-hrzklcbxs&quot;,&quot;label&quot;:&quot;Action&quot;,&quot;draggable&quot;:false,&quot;isOpen&quot;:true,&quot;card&quot;:&quot;{card}&quot;,&quot;stateAction&quot;:{&quot;id&quot;:&quot;flex-action-1750238544819&quot;,&quot;type&quot;:&quot;Custom&quot;,&quot;displayName&quot;:&quot;Action&quot;,&quot;vlocityIcon&quot;:&quot;standard-default&quot;,&quot;targetType&quot;:&quot;Navigation Item&quot;,&quot;openUrlIn&quot;:&quot;New Tab/Window&quot;,&quot;Navigation Item&quot;:{&quot;targetName&quot;:&quot;Prodotti_Presso_Altra_Agenzia&quot;},&quot;hasExtraParams&quot;:true,&quot;targetParams&quot;:{&quot;c__selectedTab&quot;:&quot;vita&quot;,&quot;c__recordId&quot;:&quot;{recordId}&quot;,&quot;c__agency&quot;:&quot;other&quot;},&quot;actionConditions&quot;:{&quot;id&quot;:&quot;state-condition-object&quot;,&quot;isParent&quot;:true,&quot;group&quot;:[]}},&quot;actionIndex&quot;:0}],&quot;showSpinner&quot;:&quot;false&quot;}},&quot;type&quot;:&quot;element&quot;,&quot;styleObject&quot;:{&quot;sizeClass&quot;:&quot;slds-size_12-of-12&quot;},&quot;parentElementKey&quot;:&quot;element_element_block_2_0_block_3_0&quot;,&quot;elementLabel&quot;:&quot;Block-3-Block-6-Image-0&quot;},{&quot;key&quot;:&quot;element_element_element_block_2_0_block_3_0_outputField_2_0&quot;,&quot;name&quot;:&quot;Text&quot;,&quot;element&quot;:&quot;outputField&quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;12&quot;},&quot;stateIndex&quot;:0,&quot;class&quot;:&quot;slds-col &quot;,&quot;property&quot;:{&quot;record&quot;:&quot;{record}&quot;,&quot;mergeField&quot;:&quot;%3Cdiv%20class=%22slds-text-align_center%22%3E%0A%3Cdiv%20class=%22slds-text-align_center%22%3EVita%20(%7BprodottiAssicurativi.other_agency.vita.count%7D)%3C/div%3E%0A%3Cdiv%20class=%22slds-text-align_center%22%3E%7BprodottiAssicurativi.other_agency.vita.premium_amount%7D%3C/div%3E%0A%3C/div%3E&quot;,&quot;card&quot;:&quot;{card}&quot;},&quot;type&quot;:&quot;text&quot;,&quot;styleObject&quot;:{&quot;sizeClass&quot;:&quot;slds-size_12-of-12&quot;},&quot;parentElementKey&quot;:&quot;element_element_block_2_0_block_3_0&quot;,&quot;elementLabel&quot;:&quot;Block-3-Block-6-Text-1&quot;}],&quot;elementLabel&quot;:&quot;Block-3-Block-6&quot;,&quot;styleObjects&quot;:[{&quot;key&quot;:0,&quot;conditions&quot;:&quot;default&quot;,&quot;styleObject&quot;:{&quot;padding&quot;:[{&quot;type&quot;:&quot;around&quot;,&quot;size&quot;:&quot;x-small&quot;,&quot;label&quot;:&quot;around:x-small&quot;}],&quot;class&quot;:&quot;slds-border_top slds-border_right slds-border_bottom slds-border_left slds-p-around_x-small &quot;,&quot;sizeClass&quot;:&quot;slds-size_2-of-12 &quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;2&quot;},&quot;margin&quot;:[],&quot;background&quot;:{&quot;color&quot;:&quot;&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:[&quot;border_top&quot;,&quot;border_right&quot;,&quot;border_bottom&quot;,&quot;border_left&quot;],&quot;width&quot;:&quot;1&quot;,&quot;color&quot;:&quot;#C9C9C9&quot;,&quot;radius&quot;:&quot;4px&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{},&quot;text&quot;:{&quot;align&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;margin-right:12px;&quot;,&quot;height&quot;:&quot;110px&quot;,&quot;style&quot;:&quot;     border-top: #C9C9C9 1px solid;border-right: #C9C9C9 1px solid;border-bottom: #C9C9C9 1px solid;border-left: #C9C9C9 1px solid; \n    border-radius:4px; height:110px; min-height:110px; max-height:220px;  margin-right:12px;&quot;,&quot;minHeight&quot;:&quot;110px&quot;,&quot;maxHeight&quot;:&quot;220px&quot;},&quot;label&quot;:&quot;Default&quot;,&quot;name&quot;:&quot;Default&quot;,&quot;conditionString&quot;:&quot;&quot;,&quot;draggable&quot;:false}],&quot;key&quot;:&quot;element_element_block_2_0_block_3_0&quot;,&quot;parentElementKey&quot;:&quot;element_block_2_0&quot;},{&quot;key&quot;:&quot;element_element_block_2_0_block_4_0&quot;,&quot;name&quot;:&quot;Block&quot;,&quot;element&quot;:&quot;block&quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;2&quot;},&quot;stateIndex&quot;:0,&quot;class&quot;:&quot;slds-col &quot;,&quot;property&quot;:{&quot;label&quot;:&quot;Block&quot;,&quot;collapsible&quot;:false,&quot;record&quot;:&quot;{record}&quot;,&quot;collapsedByDefault&quot;:false,&quot;card&quot;:&quot;{card}&quot;,&quot;data-conditions&quot;:{&quot;id&quot;:&quot;state-condition-object&quot;,&quot;isParent&quot;:true,&quot;group&quot;:[{&quot;id&quot;:&quot;state-new-condition-9&quot;,&quot;field&quot;:&quot;prodottiAssicurativi.other_agency.salute.count&quot;,&quot;operator&quot;:&quot;!=&quot;,&quot;value&quot;:&quot;0&quot;,&quot;type&quot;:&quot;custom&quot;,&quot;hasMergeField&quot;:false}]}},&quot;type&quot;:&quot;block&quot;,&quot;styleObject&quot;:{&quot;padding&quot;:[{&quot;type&quot;:&quot;around&quot;,&quot;size&quot;:&quot;x-small&quot;,&quot;label&quot;:&quot;around:x-small&quot;}],&quot;class&quot;:&quot;slds-border_top slds-border_right slds-border_bottom slds-border_left slds-p-around_x-small &quot;,&quot;sizeClass&quot;:&quot;slds-size_2-of-12 &quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;2&quot;},&quot;margin&quot;:[],&quot;background&quot;:{&quot;color&quot;:&quot;&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:[&quot;border_top&quot;,&quot;border_right&quot;,&quot;border_bottom&quot;,&quot;border_left&quot;],&quot;width&quot;:&quot;1&quot;,&quot;color&quot;:&quot;#C9C9C9&quot;,&quot;radius&quot;:&quot;4px&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{},&quot;text&quot;:{&quot;align&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;margin-right:12px;&quot;,&quot;height&quot;:&quot;110px&quot;,&quot;style&quot;:&quot;     border-top: #C9C9C9 1px solid;border-right: #C9C9C9 1px solid;border-bottom: #C9C9C9 1px solid;border-left: #C9C9C9 1px solid; \n    border-radius:4px; height:110px; min-height:110px; max-height:220px;  margin-right:12px;&quot;,&quot;minHeight&quot;:&quot;110px&quot;,&quot;maxHeight&quot;:&quot;220px&quot;},&quot;children&quot;:[{&quot;name&quot;:&quot;Image&quot;,&quot;element&quot;:&quot;flexImg&quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;12&quot;},&quot;stateIndex&quot;:0,&quot;class&quot;:&quot;slds-col &quot;,&quot;property&quot;:{&quot;record&quot;:&quot;{record}&quot;,&quot;card&quot;:&quot;{card}&quot;,&quot;size&quot;:&quot;&quot;,&quot;extraclass&quot;:&quot;slds-align_absolute-center&quot;,&quot;stateImg&quot;:{&quot;imgsrc&quot;:&quot;/resource/images_quote_domains/domain_img/Salute_Unipol.png&quot;,&quot;alternativeText&quot;:&quot;Image description&quot;,&quot;document&quot;:{&quot;label&quot;:&quot;Ruota_Unipol (Version:1)&quot;,&quot;value&quot;:&quot;/sfc/servlet.shepherd/version/download/0689X000006z4KzQAI&quot;,&quot;title&quot;:&quot;Ruota_Unipol&quot;,&quot;Id&quot;:&quot;0689X000006z4KzQAI&quot;,&quot;attachmentType&quot;:&quot;ContentVersion&quot;}},&quot;imgHeight&quot;:&quot;50px&quot;,&quot;action&quot;:{&quot;label&quot;:&quot;Action&quot;,&quot;iconName&quot;:&quot;standard-default&quot;,&quot;eventType&quot;:&quot;onclick&quot;,&quot;actionList&quot;:[{&quot;key&quot;:&quot;1744100089066-hrzklcbxs&quot;,&quot;label&quot;:&quot;Action&quot;,&quot;draggable&quot;:false,&quot;isOpen&quot;:true,&quot;card&quot;:&quot;{card}&quot;,&quot;stateAction&quot;:{&quot;id&quot;:&quot;flex-action-1752740090681&quot;,&quot;type&quot;:&quot;Custom&quot;,&quot;displayName&quot;:&quot;Action&quot;,&quot;vlocityIcon&quot;:&quot;standard-default&quot;,&quot;targetType&quot;:&quot;Navigation Item&quot;,&quot;openUrlIn&quot;:&quot;New Tab/Window&quot;,&quot;Navigation Item&quot;:{&quot;targetName&quot;:&quot;Prodotti_Presso_Altra_Agenzia&quot;},&quot;hasExtraParams&quot;:true,&quot;targetParams&quot;:{&quot;c__selectedTab&quot;:&quot;salute&quot;,&quot;c__recordId&quot;:&quot;{recordId}&quot;,&quot;c__agency&quot;:&quot;other&quot;},&quot;actionConditions&quot;:{&quot;id&quot;:&quot;state-condition-object&quot;,&quot;isParent&quot;:true,&quot;group&quot;:[]}},&quot;actionIndex&quot;:0}],&quot;showSpinner&quot;:&quot;false&quot;}},&quot;type&quot;:&quot;element&quot;,&quot;styleObject&quot;:{&quot;sizeClass&quot;:&quot;slds-size_12-of-12&quot;},&quot;elementLabel&quot;:&quot;Block-3-Block-6-Image-0-clone-0&quot;,&quot;key&quot;:&quot;element_element_element_block_2_0_block_4_0_flexImg_0_0&quot;,&quot;parentElementKey&quot;:&quot;element_element_block_2_0_block_4_0&quot;},{&quot;key&quot;:&quot;element_element_element_block_2_0_block_4_0_outputField_1_0&quot;,&quot;name&quot;:&quot;Text&quot;,&quot;element&quot;:&quot;outputField&quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;12&quot;},&quot;stateIndex&quot;:0,&quot;class&quot;:&quot;slds-col &quot;,&quot;property&quot;:{&quot;record&quot;:&quot;{record}&quot;,&quot;mergeField&quot;:&quot;%3Cdiv%20class=%22slds-text-align_center%22%3E%0A%3Cdiv%20class=%22slds-text-align_center%22%3ESalute%20(%7BprodottiAssicurativi.other_agency.salute.count%7D)%3C/div%3E%0A%3Cdiv%20class=%22slds-text-align_center%22%3E%7BprodottiAssicurativi.other_agency.salute.premium_amount%7D%3C/div%3E%0A%3C/div%3E&quot;,&quot;card&quot;:&quot;{card}&quot;},&quot;type&quot;:&quot;text&quot;,&quot;styleObject&quot;:{&quot;sizeClass&quot;:&quot;slds-size_12-of-12&quot;},&quot;parentElementKey&quot;:&quot;element_element_block_2_0_block_4_0&quot;,&quot;elementLabel&quot;:&quot;Block-3-Block-1-Text-1&quot;}],&quot;parentElementKey&quot;:&quot;element_block_2_0&quot;,&quot;elementLabel&quot;:&quot;Block-3-Block-1&quot;,&quot;styleObjects&quot;:[{&quot;key&quot;:0,&quot;conditions&quot;:&quot;default&quot;,&quot;styleObject&quot;:{&quot;padding&quot;:[{&quot;type&quot;:&quot;around&quot;,&quot;size&quot;:&quot;x-small&quot;,&quot;label&quot;:&quot;around:x-small&quot;}],&quot;class&quot;:&quot;slds-border_top slds-border_right slds-border_bottom slds-border_left slds-p-around_x-small &quot;,&quot;sizeClass&quot;:&quot;slds-size_2-of-12 &quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;2&quot;},&quot;margin&quot;:[],&quot;background&quot;:{&quot;color&quot;:&quot;&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:[&quot;border_top&quot;,&quot;border_right&quot;,&quot;border_bottom&quot;,&quot;border_left&quot;],&quot;width&quot;:&quot;1&quot;,&quot;color&quot;:&quot;#C9C9C9&quot;,&quot;radius&quot;:&quot;4px&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{},&quot;text&quot;:{&quot;align&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;margin-right:12px;&quot;,&quot;height&quot;:&quot;110px&quot;,&quot;style&quot;:&quot;     border-top: #C9C9C9 1px solid;border-right: #C9C9C9 1px solid;border-bottom: #C9C9C9 1px solid;border-left: #C9C9C9 1px solid; \n    border-radius:4px; height:110px; min-height:110px; max-height:220px;  margin-right:12px;&quot;,&quot;minHeight&quot;:&quot;110px&quot;,&quot;maxHeight&quot;:&quot;220px&quot;},&quot;label&quot;:&quot;Default&quot;,&quot;name&quot;:&quot;Default&quot;,&quot;conditionString&quot;:&quot;&quot;,&quot;draggable&quot;:false}]}],&quot;elementLabel&quot;:&quot;Block-3&quot;,&quot;styleObjects&quot;:[{&quot;key&quot;:0,&quot;conditions&quot;:&quot;default&quot;,&quot;styleObject&quot;:{&quot;padding&quot;:[{&quot;type&quot;:&quot;around&quot;,&quot;size&quot;:&quot;x-small&quot;,&quot;label&quot;:&quot;around:x-small&quot;}],&quot;class&quot;:&quot;slds-border_right slds-border_left slds-p-around_x-small &quot;,&quot;sizeClass&quot;:&quot;slds-size_12-of-12 &quot;,&quot;margin&quot;:[],&quot;background&quot;:{&quot;color&quot;:&quot;&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;12&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:[&quot;border_right&quot;,&quot;border_left&quot;],&quot;width&quot;:&quot;1&quot;,&quot;color&quot;:&quot;#E5E5E5&quot;,&quot;radius&quot;:&quot;&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{},&quot;text&quot;:{&quot;align&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;&quot;,&quot;style&quot;:&quot;     border-right: #E5E5E5 1px solid;border-left: #E5E5E5 1px solid; \n         &quot;},&quot;label&quot;:&quot;Default&quot;,&quot;name&quot;:&quot;Default&quot;,&quot;conditionString&quot;:&quot;&quot;,&quot;draggable&quot;:false}]},{&quot;name&quot;:&quot;Block&quot;,&quot;element&quot;:&quot;block&quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;12&quot;},&quot;stateIndex&quot;:0,&quot;class&quot;:&quot;slds-col &quot;,&quot;property&quot;:{&quot;label&quot;:&quot;Block&quot;,&quot;collapsible&quot;:false,&quot;record&quot;:&quot;{record}&quot;,&quot;collapsedByDefault&quot;:false,&quot;card&quot;:&quot;{card}&quot;},&quot;type&quot;:&quot;block&quot;,&quot;styleObject&quot;:{&quot;padding&quot;:[{&quot;type&quot;:&quot;around&quot;,&quot;size&quot;:&quot;x-small&quot;,&quot;label&quot;:&quot;around:x-small&quot;}],&quot;class&quot;:&quot;slds-border_right slds-border_left slds-p-around_x-small &quot;,&quot;sizeClass&quot;:&quot;slds-size_12-of-12 &quot;,&quot;margin&quot;:[],&quot;background&quot;:{&quot;color&quot;:&quot;&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;12&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:[&quot;border_right&quot;,&quot;border_left&quot;],&quot;width&quot;:&quot;1&quot;,&quot;color&quot;:&quot;#E5E5E5&quot;,&quot;radius&quot;:&quot;&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{},&quot;text&quot;:{&quot;align&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;&quot;,&quot;style&quot;:&quot;     border-right: #E5E5E5 1px solid;border-left: #E5E5E5 1px solid; \n         &quot;},&quot;children&quot;:[{&quot;name&quot;:&quot;Block&quot;,&quot;element&quot;:&quot;block&quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;12&quot;},&quot;stateIndex&quot;:1,&quot;class&quot;:&quot;slds-col &quot;,&quot;property&quot;:{&quot;label&quot;:&quot;Block&quot;,&quot;collapsible&quot;:false,&quot;record&quot;:&quot;{record}&quot;,&quot;collapsedByDefault&quot;:false,&quot;card&quot;:&quot;{card}&quot;,&quot;data-conditions&quot;:{&quot;id&quot;:&quot;state-condition-object&quot;,&quot;isParent&quot;:true,&quot;group&quot;:[]}},&quot;type&quot;:&quot;block&quot;,&quot;styleObject&quot;:{&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;12&quot;},&quot;padding&quot;:[{&quot;type&quot;:&quot;around&quot;,&quot;size&quot;:&quot;xx-small&quot;,&quot;label&quot;:&quot;around:xx-small&quot;}],&quot;class&quot;:&quot;slds-border_top slds-border_right slds-border_left slds-border_bottom slds-p-around_xx-small slds-m-right_small &quot;,&quot;sizeClass&quot;:&quot;slds-size_12-of-12 &quot;,&quot;margin&quot;:[{&quot;type&quot;:&quot;right&quot;,&quot;size&quot;:&quot;small&quot;,&quot;label&quot;:&quot;right:small&quot;}],&quot;background&quot;:{&quot;color&quot;:&quot;&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:[&quot;border_top&quot;,&quot;border_right&quot;,&quot;border_left&quot;,&quot;border_bottom&quot;],&quot;width&quot;:&quot;1&quot;,&quot;color&quot;:&quot;#C9C9C9&quot;,&quot;radius&quot;:&quot;4px 4px 4px 4px&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{},&quot;text&quot;:{&quot;align&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;margin-left: 5px\n&quot;,&quot;style&quot;:&quot;     border-top: #C9C9C9 1px solid;border-right: #C9C9C9 1px solid;border-left: #C9C9C9 1px solid;border-bottom: #C9C9C9 1px solid; \n    border-radius:4px 4px 4px 4px;     margin-left: 5px\n&quot;},&quot;children&quot;:[{&quot;name&quot;:&quot;Image&quot;,&quot;element&quot;:&quot;flexImg&quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;1&quot;},&quot;stateIndex&quot;:1,&quot;class&quot;:&quot;slds-col &quot;,&quot;property&quot;:{&quot;record&quot;:&quot;{record}&quot;,&quot;card&quot;:&quot;{card}&quot;,&quot;size&quot;:&quot;&quot;,&quot;extraclass&quot;:&quot;slds-align_absolute-center&quot;,&quot;stateImg&quot;:{&quot;imgsrc&quot;:&quot;/resource/images_quote_domains/domain_img/Offerte_Viaggi_Unipol.png&quot;,&quot;alternativeText&quot;:&quot;&quot;,&quot;document&quot;:{&quot;label&quot;:&quot;Offerte_Viaggi_Unipol (Version:1)&quot;,&quot;value&quot;:&quot;/sfc/servlet.shepherd/version/download/0689X00000704HJQAY&quot;,&quot;title&quot;:&quot;Offerte_Viaggi_Unipol&quot;,&quot;Id&quot;:&quot;0689X00000704HJQAY&quot;,&quot;attachmentType&quot;:&quot;ContentVersion&quot;}},&quot;imgHeight&quot;:&quot;24px&quot;,&quot;imgWidth&quot;:&quot;24px&quot;},&quot;type&quot;:&quot;element&quot;,&quot;styleObject&quot;:{&quot;sizeClass&quot;:&quot;slds-size_1-of-12 &quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;1&quot;},&quot;padding&quot;:[],&quot;margin&quot;:[],&quot;background&quot;:{&quot;color&quot;:&quot;&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:&quot;&quot;,&quot;width&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;,&quot;radius&quot;:&quot;&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{},&quot;text&quot;:{&quot;align&quot;:&quot;left&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;&quot;,&quot;class&quot;:&quot;slds-text-align_left &quot;,&quot;style&quot;:&quot;      \n         &quot;},&quot;elementLabel&quot;:&quot;Block-3-Image-1&quot;,&quot;styleObjects&quot;:[{&quot;key&quot;:0,&quot;conditions&quot;:&quot;default&quot;,&quot;styleObject&quot;:{&quot;sizeClass&quot;:&quot;slds-size_1-of-12 &quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;1&quot;},&quot;padding&quot;:[],&quot;margin&quot;:[],&quot;background&quot;:{&quot;color&quot;:&quot;&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:&quot;&quot;,&quot;width&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;,&quot;radius&quot;:&quot;&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{},&quot;text&quot;:{&quot;align&quot;:&quot;left&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;&quot;,&quot;class&quot;:&quot;slds-text-align_left &quot;,&quot;style&quot;:&quot;      \n         &quot;},&quot;label&quot;:&quot;Default&quot;,&quot;name&quot;:&quot;Default&quot;,&quot;conditionString&quot;:&quot;&quot;,&quot;draggable&quot;:false}],&quot;key&quot;:&quot;element_element_element_block_3_1_block_0_1_flexImg_0_1&quot;,&quot;parentElementKey&quot;:&quot;element_element_block_3_1_block_0_1&quot;},{&quot;name&quot;:&quot;Text&quot;,&quot;element&quot;:&quot;outputField&quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;2&quot;},&quot;stateIndex&quot;:1,&quot;class&quot;:&quot;slds-col &quot;,&quot;property&quot;:{&quot;record&quot;:&quot;{record}&quot;,&quot;mergeField&quot;:&quot;%3Cdiv%3EOfferte%20Suggerite:&amp;nbsp;%3C/div%3E&quot;,&quot;card&quot;:&quot;{card}&quot;},&quot;type&quot;:&quot;text&quot;,&quot;styleObject&quot;:{&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;2&quot;},&quot;sizeClass&quot;:&quot;slds-size_2-of-12 &quot;},&quot;elementLabel&quot;:&quot;Block-3-Text-2&quot;,&quot;key&quot;:&quot;element_element_element_block_3_1_block_0_1_outputField_1_1&quot;,&quot;parentElementKey&quot;:&quot;element_element_block_3_1_block_0_1&quot;},{&quot;name&quot;:&quot;Text&quot;,&quot;element&quot;:&quot;outputField&quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;5&quot;},&quot;stateIndex&quot;:1,&quot;class&quot;:&quot;slds-col &quot;,&quot;property&quot;:{&quot;record&quot;:&quot;{record}&quot;,&quot;mergeField&quot;:&quot;%3Cdiv%3E%3Cspan%20style=%22text-decoration:%20underline;%20color:%20#3598db;%22%3E%7Bgetnbo%7D%3C/span%3E%3C/div%3E&quot;,&quot;card&quot;:&quot;{card}&quot;},&quot;type&quot;:&quot;text&quot;,&quot;styleObject&quot;:{&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;5&quot;},&quot;sizeClass&quot;:&quot;slds-size_5-of-12 &quot;,&quot;padding&quot;:[],&quot;margin&quot;:[],&quot;background&quot;:{&quot;color&quot;:&quot;&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:&quot;&quot;,&quot;width&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;,&quot;radius&quot;:&quot;&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{},&quot;text&quot;:{&quot;align&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;&quot;,&quot;class&quot;:&quot;&quot;,&quot;style&quot;:&quot;      \n         &quot;},&quot;elementLabel&quot;:&quot;Block-3-Text-3&quot;,&quot;styleObjects&quot;:[{&quot;key&quot;:0,&quot;conditions&quot;:&quot;default&quot;,&quot;styleObject&quot;:{&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;5&quot;},&quot;sizeClass&quot;:&quot;slds-size_5-of-12 &quot;,&quot;padding&quot;:[],&quot;margin&quot;:[],&quot;background&quot;:{&quot;color&quot;:&quot;&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:&quot;&quot;,&quot;width&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;,&quot;radius&quot;:&quot;&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{},&quot;text&quot;:{&quot;align&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;&quot;,&quot;class&quot;:&quot;&quot;,&quot;style&quot;:&quot;      \n         &quot;},&quot;label&quot;:&quot;Default&quot;,&quot;name&quot;:&quot;Default&quot;,&quot;conditionString&quot;:&quot;&quot;,&quot;draggable&quot;:false}],&quot;key&quot;:&quot;element_element_element_block_3_1_block_0_1_outputField_2_1&quot;,&quot;parentElementKey&quot;:&quot;element_element_block_3_1_block_0_1&quot;}],&quot;elementLabel&quot;:&quot;Block-4-Block-1&quot;,&quot;styleObjects&quot;:[{&quot;key&quot;:0,&quot;conditions&quot;:&quot;default&quot;,&quot;styleObject&quot;:{&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;12&quot;},&quot;padding&quot;:[{&quot;type&quot;:&quot;around&quot;,&quot;size&quot;:&quot;xx-small&quot;,&quot;label&quot;:&quot;around:xx-small&quot;}],&quot;class&quot;:&quot;slds-border_top slds-border_right slds-border_left slds-border_bottom slds-p-around_xx-small slds-m-right_small &quot;,&quot;sizeClass&quot;:&quot;slds-size_12-of-12 &quot;,&quot;margin&quot;:[{&quot;type&quot;:&quot;right&quot;,&quot;size&quot;:&quot;small&quot;,&quot;label&quot;:&quot;right:small&quot;}],&quot;background&quot;:{&quot;color&quot;:&quot;&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:[&quot;border_top&quot;,&quot;border_right&quot;,&quot;border_left&quot;,&quot;border_bottom&quot;],&quot;width&quot;:&quot;1&quot;,&quot;color&quot;:&quot;#C9C9C9&quot;,&quot;radius&quot;:&quot;4px 4px 4px 4px&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{},&quot;text&quot;:{&quot;align&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;margin-left: 5px\n&quot;,&quot;style&quot;:&quot;     border-top: #C9C9C9 1px solid;border-right: #C9C9C9 1px solid;border-left: #C9C9C9 1px solid;border-bottom: #C9C9C9 1px solid; \n    border-radius:4px 4px 4px 4px;     margin-left: 5px\n&quot;},&quot;label&quot;:&quot;Default&quot;,&quot;name&quot;:&quot;Default&quot;,&quot;conditionString&quot;:&quot;&quot;,&quot;draggable&quot;:false}],&quot;key&quot;:&quot;element_element_block_3_1_block_0_1&quot;,&quot;parentElementKey&quot;:&quot;element_block_3_1&quot;}],&quot;elementLabel&quot;:&quot;Block-4&quot;,&quot;styleObjects&quot;:[{&quot;key&quot;:0,&quot;conditions&quot;:&quot;default&quot;,&quot;styleObject&quot;:{&quot;padding&quot;:[{&quot;type&quot;:&quot;around&quot;,&quot;size&quot;:&quot;x-small&quot;,&quot;label&quot;:&quot;around:x-small&quot;}],&quot;class&quot;:&quot;slds-border_right slds-border_left slds-p-around_x-small &quot;,&quot;sizeClass&quot;:&quot;slds-size_12-of-12 &quot;,&quot;margin&quot;:[],&quot;background&quot;:{&quot;color&quot;:&quot;&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;12&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:[&quot;border_right&quot;,&quot;border_left&quot;],&quot;width&quot;:&quot;1&quot;,&quot;color&quot;:&quot;#E5E5E5&quot;,&quot;radius&quot;:&quot;&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{},&quot;text&quot;:{&quot;align&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;&quot;,&quot;style&quot;:&quot;     border-right: #E5E5E5 1px solid;border-left: #E5E5E5 1px solid; \n         &quot;},&quot;label&quot;:&quot;Default&quot;,&quot;name&quot;:&quot;Default&quot;,&quot;conditionString&quot;:&quot;&quot;,&quot;draggable&quot;:false}]},{&quot;name&quot;:&quot;Block&quot;,&quot;element&quot;:&quot;block&quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;12&quot;},&quot;stateIndex&quot;:0,&quot;class&quot;:&quot;slds-col &quot;,&quot;property&quot;:{&quot;label&quot;:&quot;Block&quot;,&quot;collapsible&quot;:false,&quot;record&quot;:&quot;{record}&quot;,&quot;collapsedByDefault&quot;:false,&quot;card&quot;:&quot;{card}&quot;,&quot;data-conditions&quot;:{&quot;id&quot;:&quot;state-condition-object&quot;,&quot;isParent&quot;:true,&quot;group&quot;:[]}},&quot;type&quot;:&quot;block&quot;,&quot;styleObject&quot;:{&quot;padding&quot;:[{&quot;type&quot;:&quot;around&quot;,&quot;size&quot;:&quot;x-small&quot;,&quot;label&quot;:&quot;around:x-small&quot;}],&quot;class&quot;:&quot;slds-border_right slds-border_bottom slds-border_left slds-p-around_x-small &quot;,&quot;sizeClass&quot;:&quot;slds-size_12-of-12 &quot;,&quot;margin&quot;:[],&quot;background&quot;:{&quot;color&quot;:&quot;&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;12&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:[&quot;border_right&quot;,&quot;border_bottom&quot;,&quot;border_left&quot;],&quot;width&quot;:&quot;1&quot;,&quot;color&quot;:&quot;#E5E5E5&quot;,&quot;radius&quot;:&quot;0 0 4px 4px&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{},&quot;text&quot;:{&quot;align&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;&quot;,&quot;style&quot;:&quot;     border-right: #E5E5E5 1px solid;border-bottom: #E5E5E5 1px solid;border-left: #E5E5E5 1px solid; \n    border-radius:0 0 4px 4px;     &quot;},&quot;children&quot;:[{&quot;key&quot;:&quot;element_element_block_4_1_action_0_1&quot;,&quot;name&quot;:&quot;Action&quot;,&quot;element&quot;:&quot;action&quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;12&quot;},&quot;stateIndex&quot;:1,&quot;class&quot;:&quot;slds-col &quot;,&quot;property&quot;:{&quot;label&quot;:&quot;Visualizza tutto&quot;,&quot;iconName&quot;:&quot;standard-default&quot;,&quot;record&quot;:&quot;{record}&quot;,&quot;card&quot;:&quot;{card}&quot;,&quot;stateObj&quot;:&quot;{record}&quot;,&quot;actionList&quot;:[{&quot;stateAction&quot;:{&quot;id&quot;:&quot;flex-action-1744364843239&quot;,&quot;type&quot;:&quot;Custom&quot;,&quot;targetType&quot;:&quot;Navigation Item&quot;,&quot;openUrlIn&quot;:&quot;New Tab/Window&quot;,&quot;Navigation Item&quot;:{&quot;targetName&quot;:&quot;Prodotti_Presso_Altra_Agenzia&quot;},&quot;hasExtraParams&quot;:true,&quot;targetParams&quot;:{&quot;c__selectedTab&quot;:&quot;tutti&quot;,&quot;c__recordId&quot;:&quot;{recordId}&quot;,&quot;c__agency&quot;:&quot;other&quot;}},&quot;key&quot;:&quot;1732991821274-c2zmf73bq&quot;,&quot;label&quot;:&quot;Action&quot;,&quot;draggable&quot;:false,&quot;isOpen&quot;:true,&quot;actionIndex&quot;:0}],&quot;showSpinner&quot;:&quot;false&quot;,&quot;hideActionIcon&quot;:true,&quot;displayAsButton&quot;:true,&quot;flyoutDetails&quot;:{}},&quot;type&quot;:&quot;element&quot;,&quot;styleObject&quot;:{&quot;sizeClass&quot;:&quot;slds-size_12-of-12 &quot;,&quot;padding&quot;:[],&quot;margin&quot;:[],&quot;background&quot;:{&quot;color&quot;:&quot;&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;12&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:&quot;&quot;,&quot;width&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;,&quot;radius&quot;:&quot;&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{},&quot;text&quot;:{&quot;align&quot;:&quot;center&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;color: #0070EF; \ntext-align: center;\nfont-size: 13px; \nfont-style: normal; \nfont-weight: 600; \nline-height: normal;\ntext-decoration: underline; \ntext-decoration-color: #0070EF; \ntext-decoration-style: solid; \ntext-decoration-skip-ink: none; \ntext-decoration-thickness: auto; \ntext-underline-offset: auto; \ntext-underline-position: from-font;&quot;,&quot;class&quot;:&quot;slds-text-align_center &quot;,&quot;style&quot;:&quot;      \n         color: #0070EF; \ntext-align: center;\nfont-size: 13px; \nfont-style: normal; \nfont-weight: 600; \nline-height: normal;\ntext-decoration: underline; \ntext-decoration-color: #0070EF; \ntext-decoration-style: solid; \ntext-decoration-skip-ink: none; \ntext-decoration-thickness: auto; \ntext-underline-offset: auto; \ntext-underline-position: from-font;&quot;},&quot;parentElementKey&quot;:&quot;element_block_4_1&quot;,&quot;elementLabel&quot;:&quot;Block-4-Action-0&quot;,&quot;styleObjects&quot;:[{&quot;key&quot;:0,&quot;conditions&quot;:&quot;default&quot;,&quot;styleObject&quot;:{&quot;sizeClass&quot;:&quot;slds-size_12-of-12 &quot;,&quot;padding&quot;:[],&quot;margin&quot;:[],&quot;background&quot;:{&quot;color&quot;:&quot;&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;12&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:&quot;&quot;,&quot;width&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;,&quot;radius&quot;:&quot;&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{},&quot;text&quot;:{&quot;align&quot;:&quot;center&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;color: #0070EF; \ntext-align: center;\nfont-size: 13px; \nfont-style: normal; \nfont-weight: 600; \nline-height: normal;\ntext-decoration: underline; \ntext-decoration-color: #0070EF; \ntext-decoration-style: solid; \ntext-decoration-skip-ink: none; \ntext-decoration-thickness: auto; \ntext-underline-offset: auto; \ntext-underline-position: from-font;&quot;,&quot;class&quot;:&quot;slds-text-align_center &quot;,&quot;style&quot;:&quot;      \n         color: #0070EF; \ntext-align: center;\nfont-size: 13px; \nfont-style: normal; \nfont-weight: 600; \nline-height: normal;\ntext-decoration: underline; \ntext-decoration-color: #0070EF; \ntext-decoration-style: solid; \ntext-decoration-skip-ink: none; \ntext-decoration-thickness: auto; \ntext-underline-offset: auto; \ntext-underline-position: from-font;&quot;},&quot;label&quot;:&quot;Default&quot;,&quot;name&quot;:&quot;Default&quot;,&quot;conditionString&quot;:&quot;&quot;,&quot;draggable&quot;:false}]}],&quot;elementLabel&quot;:&quot;Block-5&quot;,&quot;styleObjects&quot;:[{&quot;key&quot;:0,&quot;conditions&quot;:&quot;default&quot;,&quot;styleObject&quot;:{&quot;padding&quot;:[{&quot;type&quot;:&quot;around&quot;,&quot;size&quot;:&quot;x-small&quot;,&quot;label&quot;:&quot;around:x-small&quot;}],&quot;class&quot;:&quot;slds-border_right slds-border_bottom slds-border_left slds-p-around_x-small &quot;,&quot;sizeClass&quot;:&quot;slds-size_12-of-12 &quot;,&quot;margin&quot;:[],&quot;background&quot;:{&quot;color&quot;:&quot;&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;12&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:[&quot;border_right&quot;,&quot;border_bottom&quot;,&quot;border_left&quot;],&quot;width&quot;:&quot;1&quot;,&quot;color&quot;:&quot;#E5E5E5&quot;,&quot;radius&quot;:&quot;0 0 4px 4px&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{},&quot;text&quot;:{&quot;align&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;&quot;,&quot;style&quot;:&quot;     border-right: #E5E5E5 1px solid;border-bottom: #E5E5E5 1px solid;border-left: #E5E5E5 1px solid; \n    border-radius:0 0 4px 4px;     &quot;},&quot;label&quot;:&quot;Default&quot;,&quot;name&quot;:&quot;Default&quot;,&quot;conditionString&quot;:&quot;&quot;,&quot;draggable&quot;:false}]}]}},&quot;childCards&quot;:[],&quot;actions&quot;:[],&quot;omniscripts&quot;:[],&quot;documents&quot;:[{&quot;Id&quot;:&quot;0689X000006z4KzQAI&quot;,&quot;developerName&quot;:&quot;Ruota_Unipol&quot;,&quot;type&quot;:&quot;ContentVersion&quot;},{&quot;Id&quot;:&quot;0689X000006z4HlQAI&quot;,&quot;developerName&quot;:&quot;Casa_Unipol&quot;,&quot;type&quot;:&quot;ContentVersion&quot;},{&quot;Id&quot;:&quot;0689X000006z4JNQAY&quot;,&quot;developerName&quot;:&quot;Famiglia_Unipol&quot;,&quot;type&quot;:&quot;ContentVersion&quot;},{&quot;Id&quot;:&quot;0689X000006z4KzQAI&quot;,&quot;developerName&quot;:&quot;Ruota_Unipol&quot;,&quot;type&quot;:&quot;ContentVersion&quot;},{&quot;Id&quot;:&quot;0689X000006z4KzQAI&quot;,&quot;developerName&quot;:&quot;Ruota_Unipol&quot;,&quot;type&quot;:&quot;ContentVersion&quot;},{&quot;Id&quot;:&quot;0689X00000704HJQAY&quot;,&quot;developerName&quot;:&quot;Offerte_Viaggi_Unipol&quot;,&quot;type&quot;:&quot;ContentVersion&quot;}]}],&quot;dataSource&quot;:{&quot;type&quot;:&quot;IntegrationProcedures&quot;,&quot;value&quot;:{&quot;dsDelay&quot;:&quot;&quot;,&quot;ipMethod&quot;:&quot;UniDS_ProdottiAssicurativi&quot;,&quot;vlocityAsync&quot;:false,&quot;inputMap&quot;:{&quot;AccountId&quot;:&quot;{recordId}&quot;,&quot;UserId&quot;:&quot;{userId}&quot;,&quot;showOtherAgency&quot;:&quot;true&quot;},&quot;jsonMap&quot;:&quot;{\&quot;recordId\&quot;:\&quot;{recordId}\&quot;,\&quot;userId\&quot;:\&quot;{userId}\&quot;,\&quot;showOtherAgency\&quot;:\&quot;{showOtherAgency}\&quot;}&quot;,&quot;resultVar&quot;:&quot;&quot;},&quot;orderBy&quot;:{&quot;name&quot;:&quot;&quot;,&quot;isReverse&quot;:&quot;&quot;},&quot;contextVariables&quot;:[{&quot;name&quot;:&quot;recordId&quot;,&quot;val&quot;:&quot;0019X00000tIvmXQAS&quot;,&quot;id&quot;:1},{&quot;name&quot;:&quot;userId&quot;,&quot;val&quot;:&quot;0059X00000Jr08cQAB&quot;,&quot;id&quot;:3},{&quot;name&quot;:&quot;showOtherAgency&quot;,&quot;val&quot;:&quot;true&quot;,&quot;id&quot;:53}]},&quot;title&quot;:&quot;UniProdottiAssicurativiAltraAgenzia&quot;,&quot;enableLwc&quot;:true,&quot;isFlex&quot;:true,&quot;theme&quot;:&quot;slds&quot;,&quot;selectableMode&quot;:&quot;Multi&quot;,&quot;xmlObject&quot;:{&quot;targetConfigs&quot;:&quot;CiAgICA8dGFyZ2V0Q29uZmlnIHhtbG5zPSJodHRwOi8vc29hcC5zZm9yY2UuY29tLzIwMDYvMDQvbWV0YWRhdGEiIHRhcmdldHM9ImxpZ2h0bmluZ0NvbW11bml0eV9fRGVmYXVsdCI+CiAgICAgIDxwcm9wZXJ0eSBuYW1lPSJyZWNvcmRJZCIgdHlwZT0iU3RyaW5nIi8+CiAgICA8L3RhcmdldENvbmZpZz4KICA=&quot;,&quot;targets&quot;:{&quot;target&quot;:[&quot;lightning__AppPage&quot;,&quot;lightning__HomePage&quot;,&quot;lightning__RecordPage&quot;,&quot;lightningCommunity__Page&quot;,&quot;lightningCommunity__Default&quot;]},&quot;isExplicitImport&quot;:false,&quot;masterLabel&quot;:&quot;UniProdottiAssicurativiAltraAgenzia&quot;,&quot;apiVersion&quot;:56,&quot;description&quot;:&quot;&quot;,&quot;runtimeNamespace&quot;:&quot;&quot;,&quot;isExposed&quot;:true},&quot;lwc&quot;:{&quot;DeveloperName&quot;:&quot;cfSS_Prodotti_Assicurativi_14_Unipolsai&quot;,&quot;Id&quot;:&quot;0Rb9O000003Kz2PSAS&quot;,&quot;MasterLabel&quot;:&quot;cfSS_Prodotti_Assicurativi_14_Unipolsai&quot;,&quot;NamespacePrefix&quot;:&quot;c&quot;,&quot;ManageableState&quot;:&quot;unmanaged&quot;},&quot;xmlJson&quot;:[{&quot;@attributes&quot;:{&quot;xmlns&quot;:&quot;http://soap.sforce.com/2006/04/metadata&quot;,&quot;targets&quot;:&quot;lightningCommunity__Default&quot;},&quot;property&quot;:[{&quot;@attributes&quot;:{&quot;name&quot;:&quot;recordId&quot;,&quot;type&quot;:&quot;String&quot;}}]}],&quot;isRepeatable&quot;:true,&quot;dynamicCanvasWidth&quot;:{&quot;type&quot;:&quot;desktop&quot;},&quot;globalCSS&quot;:true,&quot;sessionVars&quot;:[{&quot;name&quot;:&quot;showOtherAgency&quot;,&quot;val&quot;:&quot;&quot;,&quot;id&quot;:3}],&quot;events&quot;:[{&quot;eventname&quot;:&quot;ProdottiAgenciaEvent&quot;,&quot;channelname&quot;:&quot;ProdottiAgencia&quot;,&quot;element&quot;:&quot;action&quot;,&quot;eventtype&quot;:&quot;pubsub&quot;,&quot;recordIndex&quot;:&quot;0&quot;,&quot;actionList&quot;:[{&quot;key&quot;:&quot;1751966978775-m26g9c77a&quot;,&quot;label&quot;:&quot;Action&quot;,&quot;draggable&quot;:false,&quot;isOpen&quot;:true,&quot;card&quot;:&quot;{card}&quot;,&quot;stateAction&quot;:{&quot;id&quot;:&quot;flex-action-1752125692591&quot;,&quot;type&quot;:&quot;cardAction&quot;,&quot;displayName&quot;:&quot;Action&quot;,&quot;vlocityIcon&quot;:&quot;standard-default&quot;,&quot;targetType&quot;:&quot;Web Page&quot;,&quot;openUrlIn&quot;:&quot;Current Window&quot;,&quot;Web Page&quot;:{&quot;targetName&quot;:&quot;/apex&quot;},&quot;eventName&quot;:&quot;setValues&quot;,&quot;fieldValues&quot;:[{&quot;fieldName&quot;:&quot;Session.showOtherAgency&quot;,&quot;fieldValue&quot;:&quot;{action.showOtherAgency}&quot;},{&quot;fieldName&quot;:&quot;otherAgencyCount&quot;,&quot;fieldValue&quot;:&quot;{action.otherAgencyCount}&quot;},{&quot;fieldName&quot;:&quot;sameAgencyCount&quot;,&quot;fieldValue&quot;:&quot;{action.sameAgencyCount}&quot;}]},&quot;actionIndex&quot;:0}],&quot;showSpinner&quot;:&quot;false&quot;,&quot;key&quot;:&quot;event-0&quot;,&quot;displayLabel&quot;:&quot;ProdottiAgencia:ProdottiAgenciaEvent&quot;,&quot;eventLabel&quot;:&quot;pubsub&quot;}]}</propertySetConfig>
    <sampleDataSourceResponse>{&quot;checkCp&quot;:{&quot;error&quot;:&quot;OK&quot;,&quot;allPermission&quot;:false,&quot;errorCode&quot;:&quot;INVOKE-200&quot;,&quot;anyPermission&quot;:true,&quot;customerPermissionCheck&quot;:{&quot;X182_202030000&quot;:true,&quot;X182_202200200&quot;:true,&quot;X172_201030300&quot;:true,&quot;X172_201030700&quot;:true,&quot;X172_201030500&quot;:true,&quot;X172_201030900&quot;:true,&quot;X172_202150200&quot;:true,&quot;X172_202020102&quot;:true,&quot;X172_202020103&quot;:true,&quot;X172_202150000&quot;:true,&quot;X172_202020100&quot;:true,&quot;X172_202020101&quot;:true,&quot;X182_202010000&quot;:true,&quot;X182_206110100&quot;:true,&quot;X182_202210100&quot;:true,&quot;X182_201020000&quot;:true,&quot;X174_202000000&quot;:true,&quot;X172_202150600&quot;:true,&quot;X172_202020502&quot;:true,&quot;X172_202020503&quot;:true,&quot;X172_202150400&quot;:true,&quot;X172_202020500&quot;:true,&quot;X172_202020104&quot;:true,&quot;X172_202020501&quot;:true,&quot;X172_202020105&quot;:true,&quot;X172_202020504&quot;:true,&quot;X182_201010000&quot;:true,&quot;X182_202200100&quot;:true,&quot;X182_202120000&quot;:true,&quot;X172_201030400&quot;:true,&quot;X172_201030200&quot;:true,&quot;X172_201030800&quot;:true,&quot;X172_201030600&quot;:true,&quot;X172_202020000&quot;:true,&quot;X172_202150101&quot;:true,&quot;X172_202150100&quot;:true,&quot;X172_201031000&quot;:true,&quot;X174_201000000&quot;:true,&quot;X169_506000000&quot;:false,&quot;X182_202140000&quot;:true,&quot;X182_202210200&quot;:true,&quot;X182_206110200&quot;:true,&quot;X172_202150501&quot;:true,&quot;X172_202150303&quot;:true,&quot;X172_202150105&quot;:true,&quot;X172_202150500&quot;:true,&quot;X172_202150302&quot;:true,&quot;X172_202150104&quot;:true,&quot;X172_202150301&quot;:true,&quot;X172_202150103&quot;:true,&quot;X172_202150300&quot;:true,&quot;X172_202150102&quot;:true,&quot;X172_202020600&quot;:true,&quot;X172_202150504&quot;:true,&quot;X172_202150503&quot;:true,&quot;X172_202150305&quot;:true,&quot;X172_202150107&quot;:true,&quot;X172_202150502&quot;:true,&quot;X172_202150304&quot;:true,&quot;X172_202150106&quot;:true}},&quot;prodottiAssicurativi&quot;:{&quot;has_unica_policies&quot;:true,&quot;isAbbinato&quot;:false,&quot;other_agency&quot;:{&quot;salute&quot;:{&quot;count&quot;:0},&quot;casa_famiglia&quot;:{&quot;count&quot;:45},&quot;vita&quot;:{&quot;count&quot;:0},&quot;total_count&quot;:78,&quot;persona&quot;:{&quot;count&quot;:1},&quot;motor&quot;:{&quot;count&quot;:32}}}}</sampleDataSourceResponse>
    <stylingConfiguration>{&quot;customStyles&quot;:&quot;.CustomIcon{\r\n    width: 32px;\r\n    height: 32px;\r\n    margin-right: 8px;\r\n}&quot;}</stylingConfiguration>
    <versionNumber>2</versionNumber>
</OmniUiCard>
