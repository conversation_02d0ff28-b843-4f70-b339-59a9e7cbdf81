<?xml version="1.0" encoding="UTF-8"?>
<StandardValueSet xmlns="http://soap.sforce.com/2006/04/metadata">
    <sorted>false</sorted>
    <standardValue>
        <fullName>Nuova richiesta</fullName>
        <default>false</default>
        <label>Nuova richiesta</label>
        <closed>false</closed>
    </standardValue>
    <standardValue>
        <fullName>In gestione</fullName>
        <default>false</default>
        <label>In gestione</label>
        <closed>false</closed>
    </standardValue>
    <standardValue>
        <fullName>In attesa risposta cliente</fullName>
        <default>false</default>
        <label>In attesa risposta cliente</label>
        <closed>false</closed>
    </standardValue>
    <standardValue>
        <fullName>Mancata risposta cliente</fullName>
        <default>false</default>
        <label>Mancata risposta cliente</label>
        <closed>false</closed>
    </standardValue>
    <standardValue>
        <fullName>Ricevuta risposta cliente</fullName>
        <default>false</default>
        <label>Ricevuta risposta cliente</label>
        <closed>false</closed>
    </standardValue>
    <standardValue>
        <fullName>In attesa terze parti</fullName>
        <default>false</default>
        <label>In attesa terze parti</label>
        <closed>false</closed>
    </standardValue>
        <standardValue>
        <fullName>Trasferito</fullName>
        <default>false</default>
        <label>Trasferito</label>
        <closed>false</closed>
    </standardValue>
    <standardValue>
        <fullName>Chiuso - Risolto</fullName>
        <default>false</default>
        <label>Chiuso - Risolto</label>
        <closed>true</closed>
    </standardValue>
    <standardValue>
        <fullName>Chiuso - Annullato</fullName>
        <default>false</default>
        <label>Chiuso - Annullato</label>
        <closed>true</closed>
    </standardValue>
    <standardValue>
        <fullName>New</fullName>
        <default>true</default>
        <label>New</label>
        <closed>false</closed>
    </standardValue>
    <standardValue>
        <fullName>Expired</fullName>
        <default>false</default>
        <label>Expired</label>
        <closed>false</closed>
    </standardValue>
    <standardValue>
        <fullName>Closed</fullName>
        <default>false</default>
        <label>Closed</label>
        <closed>true</closed>
    </standardValue>
    <standardValue>
        <fullName>Merged</fullName>
        <default>false</default>
        <label>Merged</label>
        <closed>true</closed>
    </standardValue>
    <standardValue>
        <fullName>Aperto</fullName>
        <default>false</default>
        <label>Aperto</label>
        <closed>false</closed>
    </standardValue>
    <standardValue>
        <fullName>Assegnato</fullName>
        <default>false</default>
        <label>Assegnato</label>
        <closed>false</closed>
    </standardValue>
    <standardValue>
        <fullName>Chiuso</fullName>
        <default>false</default>
        <label>Chiuso</label>
        <closed>true</closed>
    </standardValue>
</StandardValueSet>
