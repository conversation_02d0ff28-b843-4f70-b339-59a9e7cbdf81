/**
 * @File Name         : urcs_CalculateTimeCaseSLA_Test.cls
 * @Description       : 
 * <AUTHOR> ACN DEV TEAM
 * @Group             : 
 * @Last Modified On  : 06-12-2025
 * @Last Modified By  : ACN DEV TEAM
**/
@isTest
public class urcs_CalculateTimeCaseSLA_Test {
    
    @TestSetup
    static void makeData(){
        Account account = new Account();
        account.Name = 'Test Society 1';
        account.ExternalId__c = 'Test2' ;
        account.RecordTypeId = Schema.SObjectType.Account.getRecordTypeInfosByDeveloperName().get('Society').getRecordTypeId();
        insert account;

        Entitlement ent = new Entitlement();
        ent.Name ='urcs_CaseEntitlement';
        ent.AccountId = account.id;
        ent.startDate = Date.Today();
        insert ent;

        Case myCase = new Case();
        myCase.AccountId = account.Id;
        myCase.Status = 'Nuova Richiesta';
        myCase.Categoria__c = 'Art. 94';
        myCase.sottoCategoria__c = 'Richiesta informazioni e procedure art. 94';
        myCase.EntitlementId = ent.id;
        myCase.RecordTypeId = Schema.SObjectType.Case.getRecordTypeInfosByDeveloperName().get('ur_CaseCRM').getRecordTypeId();
        insert myCase;
    }

    @isTest
    public static void calculateMilestoneTriggerTime_Test(){
        Test.startTest();            
        List<MilestoneType> mtLst = [SELECT Id, Name FROM MilestoneType WHERE Name IN('Goal','Deadline')];      
        List<Case> myCases = [SELECT Id  FROM Case LIMIT 1];      
        urcs_CalculateTimeCaseSLA calculator = new urcs_CalculateTimeCaseSLA();
        Integer actualTriggerTime = calculator.calculateMilestoneTriggerTime(myCases[0].Id, mtLst[0].Id);
        Integer actualTriggerTime2 = calculator.calculateMilestoneTriggerTime(myCases[0].Id, mtLst[1].Id);
        //System.assert(actualTriggerTime > 0, 'Tempo milestone negativo');
        //System.assert(actualTriggerTime2 > 0, 'Tempo milestone negativo');
        Test.stopTest();                
    }
}