/**
 * @File Name         : urcs_DC_GetOptional_Test.cls
 * @Description       : 
 * <AUTHOR> VE
 * @Group             : 
 * @Last Modified On  : 11-07-2025
 * @Last Modified By  : VE
**/
@isTest
public class urcs_DC_GetOptional_Test {
    
    @TestSetup
    static void makeData(){
        Asset testAsset = new Asset();
        testAsset.Name = 'Test Vehicle Asset';
        testAsset.idAutoLeo__c = '12345';
        insert testAsset;
    }
    
    @isTest
    static void testCallMethod() {
        Asset testAsset = [SELECT Id FROM Asset WHERE Name = 'Test Vehicle Asset' LIMIT 1];
        
        Map<String, Object> input = new Map<String, Object>{
            'recordId' => testAsset.Id,
            'Set Values' => new Map<String, Object>{
                'methodExecute' => 'init'
            }
        };
        
        Map<String, Object> output = new Map<String, Object>();
        Map<String, Object> options = new Map<String, Object>();
        
        Map<String, Object> args = new Map<String, Object>{
            'input' => input,
            'output' => output,
            'options' => options
        };
        
        urcs_DC_GetOptional controller = new urcs_DC_GetOptional();
        
        Test.startTest();
        Object result = controller.call('testAction', args);
        Test.stopTest();
    }
    
    @isTest
    static void testInvokeMethod() {
        Asset testAsset = [SELECT Id FROM Asset WHERE Name = 'Test Vehicle Asset' LIMIT 1];
        
        Map<String, Object> input = new Map<String, Object>{
            'recordId' => testAsset.Id,
            'Set Values' => new Map<String, Object>{
                'methodExecute' => 'init'
            }
        };
        
        Map<String, Object> output = new Map<String, Object>();
        Map<String, Object> options = new Map<String, Object>();
        
        Test.startTest();
        Map<String, Object> result = urcs_DC_GetOptional.invokeMethod(input, output, options);
        Test.stopTest();
    }
    
    @isTest
    static void testGetOptional() {
        Asset testAsset = [SELECT Id FROM Asset WHERE Name = 'Test Vehicle Asset' LIMIT 1];
        
        Map<String, Object> input = new Map<String, Object>{
            'recordId' => testAsset.Id
        };
        
        Map<String, Object> output = new Map<String, Object>();
        Map<String, Object> options = new Map<String, Object>();
        
        Test.startTest();
        try{
            Map<String, Object> result = urcs_DC_GetOptional.getOptional(input, output, options);
        }catch(Exception ex){}
        Test.stopTest();
    }
    
    @isTest
    static void testGetOptionalWithNullRecordId() {
        Map<String, Object> input = new Map<String, Object>{
            'recordId' => null
        };
        
        Map<String, Object> output = new Map<String, Object>();
        Map<String, Object> options = new Map<String, Object>();
        
        Test.startTest();
        try{
            Map<String, Object> result = urcs_DC_GetOptional.getOptional(input, output, options);
        }catch(Exception ex){}
        Test.stopTest();
    }
    
    @isTest
    static void testInvokeMethodException() {
        Map<String, Object> input = new Map<String, Object>{
            'recordId' => 'invalid',
            'Set Values' => new Map<String, Object>{
                'methodExecute' => 'init'
            }
        };
        
        Map<String, Object> output = new Map<String, Object>();
        Map<String, Object> options = new Map<String, Object>();
        
        Test.startTest();
        try{
            Map<String, Object> result = urcs_DC_GetOptional.invokeMethod(input, output, options);
        }catch(Exception ex){}
        Test.stopTest();
    }
}