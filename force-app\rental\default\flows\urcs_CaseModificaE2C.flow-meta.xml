<?xml version="1.0" encoding="UTF-8"?>
<Flow xmlns="http://soap.sforce.com/2006/04/metadata">
    <actionCalls>
        <name>Send_Email_Creazione</name>
        <label>Send Email Creazione</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <actionName>emailSimple</actionName>
        <actionType>emailSimple</actionType>
        <faultConnector>
            <targetReference>Errore_generico</targetReference>
        </faultConnector>
        <flowTransactionModel>CurrentTransaction</flowTransactionModel>
        <inputParameters>
            <name>senderType</name>
            <value>
                <stringValue>OrgWideEmailAddress</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>senderAddress</name>
            <value>
                <elementReference>Get_OWA_No_Reply.Address</elementReference>
            </value>
        </inputParameters>
        <inputParameters>
            <name>useEmailTemplate</name>
            <value>
                <stringValue>True</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>emailTemplateId</name>
            <value>
                <stringValue>{!Get_Email_Template_Creazione.Id}</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>recipientId</name>
            <value>
                <elementReference>caseRecord.ContactId</elementReference>
            </value>
        </inputParameters>
        <inputParameters>
            <name>relatedRecordId</name>
            <value>
                <elementReference>recordId</elementReference>
            </value>
        </inputParameters>
        <inputParameters>
            <name>logEmailOnSend</name>
            <value>
                <booleanValue>true</booleanValue>
            </value>
        </inputParameters>
        <nameSegment>emailSimple</nameSegment>
        <offset>0</offset>
        <versionString>1.0.1</versionString>
    </actionCalls>
    <apiVersion>64.0</apiVersion>
    <areMetricsLoggedToDataCloud>false</areMetricsLoggedToDataCloud>
    <assignments>
        <name>Assign</name>
        <label>Assign detail info</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <assignmentItems>
            <assignToReference>caseRecord.LobCliente__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>GetAccountDetailsInfo.Lob__c</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>caseRecord.CanaleCliente__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>GetAccountDetailsInfo.Canale__c</elementReference>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Assign_case_fields</targetReference>
        </connector>
    </assignments>
    <assignments>
        <name>Assign_Case_AccountId</name>
        <label>Assign Case.AccountId</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <assignmentItems>
            <assignToReference>caseRecord.AccountId</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>Veicoli.firstSelectedRow.ServiceContract__r.ParentServiceContract.AccountId</elementReference>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>getAccountSocRental</targetReference>
        </connector>
    </assignments>
    <assignments>
        <name>Assign_case_fields</name>
        <label>Assign case fields</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <assignmentItems>
            <assignToReference>caseRecord.ContractAsset__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>Veicoli.firstSelectedRow.Id</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>caseRecord.DriverId__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>Veicoli.firstSelectedRow.ServiceContract__r.Account.PersonContact.Id</elementReference>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Screen_conferma</targetReference>
        </connector>
    </assignments>
    <assignments>
        <name>Assign_caserecord</name>
        <label>Assign caserecord</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <assignmentItems>
            <assignToReference>caseRecord</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>GetCaseInfo</elementReference>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Get_Asset_RT</targetReference>
        </connector>
    </assignments>
    <assignments>
        <name>Assign_email_sollecito</name>
        <label>Assign email sollecito</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <assignmentItems>
            <assignToReference>caseRecord.EmailSollecito__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>caseRecord.Contact.Email</elementReference>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>GetAccountDetailsInfo_is_null</targetReference>
        </connector>
    </assignments>
    <assignments>
        <name>Assign_FM_contact_info</name>
        <label>Assign FM contact info</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <assignmentItems>
            <assignToReference>caseRecord.ContactId</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>getFMInfo.Id</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>caseRecord.NomeChiamante__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>NominativoFM</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>caseRecord.EmailChiamante__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>EmailFM.value</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>caseRecord.TelChiamante__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>TelefonoFM.value</elementReference>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>assign_invio_email_automatico</targetReference>
        </connector>
    </assignments>
    <assignments>
        <name>assign_invio_email_automatico</name>
        <label>assign invio email automatico</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <assignmentItems>
            <assignToReference>caseRecord.InvioEmailAutomatico__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <booleanValue>true</booleanValue>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>caseRecord.Origin</assignToReference>
            <operator>Assign</operator>
            <value>
                <stringValue>Email Services</stringValue>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Screen_campi_case</targetReference>
        </connector>
    </assignments>
    <assignments>
        <name>Assign_ShowRequiredMsg</name>
        <label>Assign ShowRequiredMsg</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <assignmentItems>
            <assignToReference>ShowRequiredMsg</assignToReference>
            <operator>Assign</operator>
            <value>
                <booleanValue>true</booleanValue>
            </value>
        </assignmentItems>
        <connector>
            <isGoTo>true</isGoTo>
            <targetReference>Screen_campi_case</targetReference>
        </connector>
    </assignments>
    <assignments>
        <name>Assign_showrequiredmsg_false</name>
        <label>Assign showRequiredMsg false</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <assignmentItems>
            <assignToReference>ShowRequiredMsg</assignToReference>
            <operator>Assign</operator>
            <value>
                <booleanValue>false</booleanValue>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>CheckZonaCircRequired</targetReference>
        </connector>
    </assignments>
    <assignments>
        <name>Assign_ShowRequiredMsgAR</name>
        <label>Assign ShowRequiredMsgAR</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <assignmentItems>
            <assignToReference>ShowRequiredMsgAR</assignToReference>
            <operator>Assign</operator>
            <value>
                <booleanValue>true</booleanValue>
            </value>
        </assignmentItems>
        <connector>
            <isGoTo>true</isGoTo>
            <targetReference>ScreenDatiAR</targetReference>
        </connector>
    </assignments>
    <assignments>
        <name>Assign_ShowRequiredMsgAR_false</name>
        <label>Assign ShowRequiredMsgAR false</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <assignmentItems>
            <assignToReference>ShowRequiredMsgAR</assignToReference>
            <operator>Assign</operator>
            <value>
                <booleanValue>false</booleanValue>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>assign_invio_email_automatico</targetReference>
        </connector>
    </assignments>
    <assignments>
        <name>Assign_ShowRequiredMsgEC</name>
        <label>Assign ShowRequiredMsgEC</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <assignmentItems>
            <assignToReference>ShowRequiredMsgEC</assignToReference>
            <operator>Assign</operator>
            <value>
                <booleanValue>true</booleanValue>
            </value>
        </assignmentItems>
        <connector>
            <isGoTo>true</isGoTo>
            <targetReference>ScreenEntitaChiamante</targetReference>
        </connector>
    </assignments>
    <assignments>
        <name>Assign_Util_contact_info</name>
        <label>Assign Util contact info</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <assignmentItems>
            <assignToReference>caseRecord.ContactId</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>Veicoli.firstSelectedRow.ServiceContract__r.Account.PersonContactId</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>caseRecord.NomeChiamante__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>NominativoDriver</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>caseRecord.EmailChiamante__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>EmailDriver.value</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>caseRecord.TelChiamante__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>TelefonoDriver.value</elementReference>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>assign_invio_email_automatico</targetReference>
        </connector>
    </assignments>
    <customProperties>
        <name>ScreenProgressIndicator</name>
        <value>
            <stringValue>{&quot;location&quot;:&quot;top&quot;,&quot;type&quot;:&quot;simple&quot;}</stringValue>
        </value>
    </customProperties>
    <decisions>
        <name>Check_Altro_Rich_Info</name>
        <label>Check Altro Rich Info</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <defaultConnector>
            <targetReference>Assign_ShowRequiredMsgAR_false</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>Default Outcome</defaultConnectorLabel>
        <rules>
            <name>some_fields_null_AR</name>
            <conditionLogic>or</conditionLogic>
            <conditions>
                <leftValueReference>caseRecord.NomeChiamante__c</leftValueReference>
                <operator>IsBlank</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>caseRecord.TelChiamante__c</leftValueReference>
                <operator>IsBlank</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>caseRecord.EmailChiamante__c</leftValueReference>
                <operator>IsBlank</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Assign_ShowRequiredMsgAR</targetReference>
            </connector>
            <label>some fields null AR</label>
        </rules>
    </decisions>
    <decisions>
        <name>Check_assegnatario</name>
        <label>Check assegnatario</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <defaultConnector>
            <targetReference>Screen_ErrorMsgAss</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>Default Outcome</defaultConnectorLabel>
        <rules>
            <name>true</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>GetCaseInfo.UtAssegnatario__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <elementReference>currentUserId</elementReference>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Assign_caserecord</targetReference>
            </connector>
            <label>true</label>
        </rules>
    </decisions>
    <decisions>
        <name>Check_case_contactId</name>
        <label>Check Case.ContactId</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <defaultConnector>
            <targetReference>Assign_case_fields</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>Default Outcome</defaultConnectorLabel>
        <rules>
            <name>Case_ContactId_not_null</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>caseRecord.ContactId</leftValueReference>
                <operator>IsNull</operator>
                <rightValue>
                    <booleanValue>false</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Assign_email_sollecito</targetReference>
            </connector>
            <label>Case.ContactId not null</label>
        </rules>
    </decisions>
    <decisions>
        <name>check_contractAsset</name>
        <label>check contractAsset</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <defaultConnector>
            <targetReference>ScreenErrorMsg</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>Default Outcome</defaultConnectorLabel>
        <rules>
            <name>contractAsset_not_null</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>getContractAssetInfo</leftValueReference>
                <operator>IsNull</operator>
                <rightValue>
                    <booleanValue>false</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Screen_Veicolo_Contratto</targetReference>
            </connector>
            <label>contractAsset not null</label>
        </rules>
    </decisions>
    <decisions>
        <name>Check_Email_Template_Creazione</name>
        <label>Check Email Template Creazione</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <defaultConnectorLabel>Default Outcome</defaultConnectorLabel>
        <rules>
            <name>ET_Creazione_not_null</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>Get_Email_Template_Creazione</leftValueReference>
                <operator>IsNull</operator>
                <rightValue>
                    <booleanValue>false</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Send_Email_Creazione</targetReference>
            </connector>
            <label>ET Creazione not null</label>
        </rules>
    </decisions>
    <decisions>
        <name>Check_Ent_Chiamante</name>
        <label>Check Ent Chiamante</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <defaultConnector>
            <targetReference>CheckEntitchiamanteselezionata</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>selezionata</defaultConnectorLabel>
        <rules>
            <name>non_selezionata</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>caseRecord.EntChiamante__c</leftValueReference>
                <operator>IsNull</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Assign_ShowRequiredMsgEC</targetReference>
            </connector>
            <label>non selezionata</label>
        </rules>
    </decisions>
    <decisions>
        <name>Check_flag_result</name>
        <label>Check flag result</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <defaultConnector>
            <targetReference>Assign_Case_AccountId</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>Default Outcome</defaultConnectorLabel>
        <rules>
            <name>is_true</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>isZonaCircrRequired</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>caseRecord.Zona_di_circolazione__c</leftValueReference>
                <operator>IsNull</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <isGoTo>true</isGoTo>
                <targetReference>Screen_campi_case</targetReference>
            </connector>
            <label>is true</label>
        </rules>
    </decisions>
    <decisions>
        <name>Checkcampirequired</name>
        <label>Check campi required</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <defaultConnector>
            <targetReference>Assign_showrequiredmsg_false</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>Default Outcome</defaultConnectorLabel>
        <rules>
            <name>esistecamponull</name>
            <conditionLogic>or</conditionLogic>
            <conditions>
                <leftValueReference>caseRecord.Categoria__c</leftValueReference>
                <operator>IsNull</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>caseRecord.SottoCategoria__c</leftValueReference>
                <operator>IsNull</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Assign_ShowRequiredMsg</targetReference>
            </connector>
            <label>esiste campo null</label>
        </rules>
    </decisions>
    <decisions>
        <name>CheckEntitchiamanteselezionata</name>
        <label>Check Entità chiamante selezionata</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <defaultConnector>
            <targetReference>assign_invio_email_automatico</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>Default Outcome</defaultConnectorLabel>
        <rules>
            <name>Utilizzatore</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>caseRecord.EntChiamante__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Utilizzatore</stringValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>ScreenDatiUtilizzatore</targetReference>
            </connector>
            <label>Utilizzatore</label>
        </rules>
        <rules>
            <name>Fleet_Manager</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>caseRecord.EntChiamante__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Fleet Manager</stringValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>getFMRecordTypeInfo</targetReference>
            </connector>
            <label>Fleet Manager</label>
        </rules>
        <rules>
            <name>Altro_Richiedente</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>caseRecord.EntChiamante__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Altro Richiedente</stringValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>ScreenDatiAR</targetReference>
            </connector>
            <label>Altro Richiedente</label>
        </rules>
    </decisions>
    <decisions>
        <name>GetAccountDetailsInfo_is_null</name>
        <label>GetAccountDetailsInfo is null?</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <defaultConnector>
            <targetReference>Assign_case_fields</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>Default Outcome</defaultConnectorLabel>
        <rules>
            <name>NO</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>GetAccountDetailsInfo</leftValueReference>
                <operator>IsNull</operator>
                <rightValue>
                    <booleanValue>false</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Assign</targetReference>
            </connector>
            <label>NO</label>
        </rules>
    </decisions>
    <description>Flow per la modifica case da Email to case</description>
    <dynamicChoiceSets>
        <name>GroupCollection</name>
        <collectionReference>ufficiQueueList</collectionReference>
        <dataType>String</dataType>
        <displayField>Name</displayField>
        <object>Group</object>
        <valueField>Id</valueField>
    </dynamicChoiceSets>
    <dynamicChoiceSets>
        <name>VeicoliRecordChoiceSet</name>
        <dataType>String</dataType>
        <displayField>Name</displayField>
        <filterLogic>and</filterLogic>
        <filters>
            <field>RecordTypeId</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>Get_Asset_RT.Id</elementReference>
            </value>
        </filters>
        <object>Asset</object>
        <valueField>Id</valueField>
    </dynamicChoiceSets>
    <environments>Default</environments>
    <formulas>
        <name>currentUserId</name>
        <dataType>String</dataType>
        <expression>CASESAFEID({!$User.Id})</expression>
    </formulas>
    <interviewLabel>urcs_CaseModificaE2C {!$Flow.CurrentDateTime}</interviewLabel>
    <label>urcs_CaseModificaE2C</label>
    <processMetadataValues>
        <name>BuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>CanvasMode</name>
        <value>
            <stringValue>AUTO_LAYOUT_CANVAS</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>OriginBuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processType>Flow</processType>
    <recordLookups>
        <name>Get_Asset_RT</name>
        <label>Get Asset RT</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Screen_Veicolo</targetReference>
        </connector>
        <faultConnector>
            <isGoTo>true</isGoTo>
            <targetReference>Errore_generico</targetReference>
        </faultConnector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>SobjectType</field>
            <operator>EqualTo</operator>
            <value>
                <stringValue>Asset</stringValue>
            </value>
        </filters>
        <filters>
            <field>DeveloperName</field>
            <operator>EqualTo</operator>
            <value>
                <stringValue>ur_Veicolo</stringValue>
            </value>
        </filters>
        <getFirstRecordOnly>true</getFirstRecordOnly>
        <object>RecordType</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordLookups>
        <name>Get_Email_Template_Creazione</name>
        <label>Get Email Template Creazione</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Check_Email_Template_Creazione</targetReference>
        </connector>
        <faultConnector>
            <isGoTo>true</isGoTo>
            <targetReference>Errore_generico</targetReference>
        </faultConnector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>DeveloperName</field>
            <operator>StartsWith</operator>
            <value>
                <stringValue>urcs_CaseNotificaCreazione</stringValue>
            </value>
        </filters>
        <getFirstRecordOnly>true</getFirstRecordOnly>
        <object>EmailTemplate</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordLookups>
        <name>Get_OWA_No_Reply</name>
        <label>Get OWA No Reply</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Get_Email_Template_Creazione</targetReference>
        </connector>
        <faultConnector>
            <isGoTo>true</isGoTo>
            <targetReference>Errore_generico</targetReference>
        </faultConnector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>DisplayName</field>
            <operator>EqualTo</operator>
            <value>
                <stringValue>Unipol Rental CS No Reply</stringValue>
            </value>
        </filters>
        <filters>
            <field>IsVerified</field>
            <operator>EqualTo</operator>
            <value>
                <booleanValue>true</booleanValue>
            </value>
        </filters>
        <getFirstRecordOnly>true</getFirstRecordOnly>
        <object>OrgWideEmailAddress</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordLookups>
        <name>getAccountAccountRel</name>
        <label>getAccountAccountRel</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>GetAccountDetailsInfo</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>FinServ__Account__c</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>caseRecord.AccountId</elementReference>
            </value>
        </filters>
        <filters>
            <field>FinServ__RelatedAccount__c</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>getAccountSocRental.Id</elementReference>
            </value>
        </filters>
        <getFirstRecordOnly>true</getFirstRecordOnly>
        <object>FinServ__AccountAccountRelation__c</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordLookups>
        <name>GetAccountDetailsInfo</name>
        <label>GetAccountDetailsInfo</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Check_case_contactId</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Relation__c</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>getAccountAccountRel.Id</elementReference>
            </value>
        </filters>
        <getFirstRecordOnly>true</getFirstRecordOnly>
        <object>AccountDetails__c</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordLookups>
        <name>getAccountSocRental</name>
        <label>getAccountSocRental</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>getAccountAccountRel</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>ExternalId__c</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>$Setup.urcs_GeneralSettings__c.GroupSocRentalName__c</elementReference>
            </value>
        </filters>
        <getFirstRecordOnly>true</getFirstRecordOnly>
        <object>Account</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordLookups>
        <name>GetCaseInfo</name>
        <label>GetCaseInfo</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Check_assegnatario</targetReference>
        </connector>
        <faultConnector>
            <isGoTo>true</isGoTo>
            <targetReference>Errore_generico</targetReference>
        </faultConnector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Id</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>recordId</elementReference>
            </value>
        </filters>
        <getFirstRecordOnly>true</getFirstRecordOnly>
        <object>Case</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordLookups>
        <name>getContractAssetInfo</name>
        <label>getContractAssetInfo</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>check_contractAsset</targetReference>
        </connector>
        <faultConnector>
            <isGoTo>true</isGoTo>
            <targetReference>Errore_generico</targetReference>
        </faultConnector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Asset__c</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>ScreenVeicolo.recordId</elementReference>
            </value>
        </filters>
        <getFirstRecordOnly>false</getFirstRecordOnly>
        <limit>
            <numberValue>20000.0</numberValue>
        </limit>
        <object>ContractAsset__c</object>
        <queriedFields>Id</queriedFields>
        <queriedFields>Asset__c</queriedFields>
        <queriedFields>ServiceContract__c</queriedFields>
        <queriedFields>NomeAsset__c</queriedFields>
        <queriedFields>NomeContratto__c</queriedFields>
        <queriedFields>StatoContratto__c</queriedFields>
        <queriedFields>ClienteContratto__c</queriedFields>
        <queriedFields>tsDa__c</queriedFields>
        <queriedFields>tsA__c</queriedFields>
        <sortField>tsDa__c</sortField>
        <sortOrder>Desc</sortOrder>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordLookups>
        <name>getDriverPrinc</name>
        <label>getDriverPrinc</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>ScreenEntitaChiamante</targetReference>
        </connector>
        <faultConnector>
            <isGoTo>true</isGoTo>
            <targetReference>Errore_generico</targetReference>
        </faultConnector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Id</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>Veicoli.firstSelectedRow.ServiceContract__r.AccountId</elementReference>
            </value>
        </filters>
        <getFirstRecordOnly>true</getFirstRecordOnly>
        <object>Account</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordLookups>
        <name>getFMInfo</name>
        <label>getFMInfo</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>ScreenDatiFM</targetReference>
        </connector>
        <faultConnector>
            <isGoTo>true</isGoTo>
            <targetReference>Errore_generico</targetReference>
        </faultConnector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>RecordTypeId</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>getFMRecordTypeInfo.Id</elementReference>
            </value>
        </filters>
        <filters>
            <field>AccountId</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>Veicoli.firstSelectedRow.ServiceContract__r.ParentServiceContract.AccountId</elementReference>
            </value>
        </filters>
        <getFirstRecordOnly>true</getFirstRecordOnly>
        <object>Contact</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordLookups>
        <name>getFMRecordTypeInfo</name>
        <label>getFMRecordTypeInfo</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>getFMInfo</targetReference>
        </connector>
        <faultConnector>
            <isGoTo>true</isGoTo>
            <targetReference>Errore_generico</targetReference>
        </faultConnector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>SobjectType</field>
            <operator>EqualTo</operator>
            <value>
                <stringValue>Contact</stringValue>
            </value>
        </filters>
        <filters>
            <field>DeveloperName</field>
            <operator>EqualTo</operator>
            <value>
                <stringValue>ur_FleetManager</stringValue>
            </value>
        </filters>
        <getFirstRecordOnly>true</getFirstRecordOnly>
        <object>RecordType</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordUpdates>
        <name>Update_Case</name>
        <label>Update Case</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <connector>
            <targetReference>Get_OWA_No_Reply</targetReference>
        </connector>
        <faultConnector>
            <isGoTo>true</isGoTo>
            <targetReference>Errore_generico</targetReference>
        </faultConnector>
        <inputReference>caseRecord</inputReference>
    </recordUpdates>
    <runInMode>SystemModeWithSharing</runInMode>
    <screens>
        <name>Errore_generico</name>
        <label>Errore generico</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <allowBack>false</allowBack>
        <allowFinish>true</allowFinish>
        <allowPause>false</allowPause>
        <fields>
            <name>MsgErroreGenerico</name>
            <fieldText>&lt;p style=&quot;text-align: center;&quot;&gt;Si è verificato un errore: impossibile completare l&apos;operazione.&lt;/p&gt;&lt;p style=&quot;text-align: center;&quot;&gt;&lt;br&gt;&lt;/p&gt;&lt;p&gt;&lt;span style=&quot;background-color: rgb(255, 255, 255); color: rgb(255, 0, 0);&quot;&gt;{!$Flow.FaultMessage}&lt;/span&gt;&lt;/p&gt;&lt;p style=&quot;text-align: center;&quot;&gt;&lt;br&gt;&lt;/p&gt;&lt;p style=&quot;text-align: center;&quot;&gt;&lt;br&gt;&lt;/p&gt;&lt;p&gt;&lt;br&gt;&lt;/p&gt;</fieldText>
            <fieldType>DisplayText</fieldType>
        </fields>
        <showFooter>true</showFooter>
        <showHeader>true</showHeader>
    </screens>
    <screens>
        <name>Screen_campi_case</name>
        <label>Screen campi case</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <allowBack>true</allowBack>
        <allowFinish>true</allowFinish>
        <allowPause>false</allowPause>
        <connector>
            <targetReference>Checkcampirequired</targetReference>
        </connector>
        <fields>
            <name>RequiredMsg</name>
            <fieldText>&lt;p style=&quot;text-align: center;&quot;&gt;&lt;span style=&quot;color: rgb(255, 0, 0);&quot;&gt;I seguenti campi sono obbligatori: &lt;/span&gt;&lt;/p&gt;&lt;p style=&quot;text-align: center;&quot;&gt;&lt;span style=&quot;color: rgb(255, 0, 0);&quot;&gt;Categoria, Sottocategoria&lt;/span&gt;&lt;/p&gt;</fieldText>
            <fieldType>DisplayText</fieldType>
            <visibilityRule>
                <conditionLogic>and</conditionLogic>
                <conditions>
                    <leftValueReference>ShowRequiredMsg</leftValueReference>
                    <operator>EqualTo</operator>
                    <rightValue>
                        <booleanValue>true</booleanValue>
                    </rightValue>
                </conditions>
            </visibilityRule>
        </fields>
        <fields>
            <name>requiredMsgZonacirc</name>
            <fieldText>&lt;p style=&quot;text-align: center;&quot;&gt;&lt;span style=&quot;color: rgb(255, 0, 0);&quot;&gt;Il campo Zona di Circolazione è obbligatorio per questa combinazione di categoria e sottocategoria.&lt;/span&gt;&lt;/p&gt;</fieldText>
            <fieldType>DisplayText</fieldType>
            <visibilityRule>
                <conditionLogic>and</conditionLogic>
                <conditions>
                    <leftValueReference>isZonaCircrRequired</leftValueReference>
                    <operator>EqualTo</operator>
                    <rightValue>
                        <booleanValue>true</booleanValue>
                    </rightValue>
                </conditions>
            </visibilityRule>
        </fields>
        <fields>
            <name>Screen_campi_case_Section1</name>
            <fieldType>RegionContainer</fieldType>
            <fields>
                <name>Screen_campi_case_Section1_Column1</name>
                <fieldType>Region</fieldType>
                <fields>
                    <fieldType>ObjectProvided</fieldType>
                    <inputsOnNextNavToAssocScrn>UseStoredValues</inputsOnNextNavToAssocScrn>
                    <isRequired>false</isRequired>
                    <objectFieldReference>caseRecord.Origin</objectFieldReference>
                    <visibilityRule>
                        <conditionLogic>and</conditionLogic>
                        <conditions>
                            <leftValueReference>caseRecord.Origin</leftValueReference>
                            <operator>NotEqualTo</operator>
                            <rightValue>
                                <stringValue>Email Services</stringValue>
                            </rightValue>
                        </conditions>
                    </visibilityRule>
                </fields>
                <fields>
                    <fieldType>ObjectProvided</fieldType>
                    <inputsOnNextNavToAssocScrn>UseStoredValues</inputsOnNextNavToAssocScrn>
                    <isRequired>false</isRequired>
                    <objectFieldReference>caseRecord.TipoRichiesta__c</objectFieldReference>
                </fields>
                <fields>
                    <fieldType>ObjectProvided</fieldType>
                    <inputsOnNextNavToAssocScrn>UseStoredValues</inputsOnNextNavToAssocScrn>
                    <isRequired>false</isRequired>
                    <objectFieldReference>caseRecord.Categoria__c</objectFieldReference>
                </fields>
                <fields>
                    <fieldType>ObjectProvided</fieldType>
                    <inputsOnNextNavToAssocScrn>UseStoredValues</inputsOnNextNavToAssocScrn>
                    <isRequired>false</isRequired>
                    <objectFieldReference>caseRecord.SottoCategoria__c</objectFieldReference>
                </fields>
                <inputParameters>
                    <name>width</name>
                    <value>
                        <stringValue>6</stringValue>
                    </value>
                </inputParameters>
                <isRequired>false</isRequired>
            </fields>
            <fields>
                <name>Screen_campi_case_Section1_Column2</name>
                <fieldType>Region</fieldType>
                <fields>
                    <fieldType>ObjectProvided</fieldType>
                    <inputsOnNextNavToAssocScrn>UseStoredValues</inputsOnNextNavToAssocScrn>
                    <isRequired>false</isRequired>
                    <objectFieldReference>caseRecord.Zona_di_circolazione__c</objectFieldReference>
                </fields>
                <fields>
                    <fieldType>ObjectProvided</fieldType>
                    <inputsOnNextNavToAssocScrn>UseStoredValues</inputsOnNextNavToAssocScrn>
                    <isRequired>false</isRequired>
                    <objectFieldReference>caseRecord.Sollecitato__c</objectFieldReference>
                </fields>
                <inputParameters>
                    <name>width</name>
                    <value>
                        <stringValue>6</stringValue>
                    </value>
                </inputParameters>
                <isRequired>false</isRequired>
            </fields>
            <isRequired>false</isRequired>
            <regionContainerType>SectionWithoutHeader</regionContainerType>
        </fields>
        <showFooter>true</showFooter>
        <showHeader>true</showHeader>
        <stageReference>
            <elementReference>step4</elementReference>
        </stageReference>
    </screens>
    <screens>
        <name>Screen_conferma</name>
        <label>Screen conferma</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <allowBack>true</allowBack>
        <allowFinish>true</allowFinish>
        <allowPause>false</allowPause>
        <connector>
            <targetReference>Update_Case</targetReference>
        </connector>
        <fields>
            <name>MsgConferma</name>
            <fieldText>&lt;p style=&quot;text-align: center;&quot;&gt;Sei sicuro di voler confermare le modifiche?&lt;/p&gt;</fieldText>
            <fieldType>DisplayText</fieldType>
        </fields>
        <showFooter>true</showFooter>
        <showHeader>true</showHeader>
    </screens>
    <screens>
        <name>Screen_ErrorMsgAss</name>
        <label>Screen ErrorMsgAss</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <allowBack>false</allowBack>
        <allowFinish>true</allowFinish>
        <allowPause>false</allowPause>
        <fields>
            <name>ErrorMsgAss</name>
            <fieldText>&lt;p style=&quot;text-align: center;&quot;&gt;&lt;span style=&quot;background-color: rgb(255, 255, 255);&quot;&gt;Per effettuare l&apos;operazione è necessario prendere in carico il case.&lt;/span&gt;&lt;/p&gt;</fieldText>
            <fieldType>DisplayText</fieldType>
        </fields>
        <showFooter>true</showFooter>
        <showHeader>true</showHeader>
    </screens>
    <screens>
        <name>Screen_Veicolo</name>
        <label>Screen Veicolo</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <allowBack>false</allowBack>
        <allowFinish>true</allowFinish>
        <allowPause>false</allowPause>
        <connector>
            <targetReference>getContractAssetInfo</targetReference>
        </connector>
        <fields>
            <name>ScreenVeicolo</name>
            <extensionName>flowruntime:lookup</extensionName>
            <fieldType>ComponentInstance</fieldType>
            <inputParameters>
                <name>objectApiName</name>
                <value>
                    <stringValue>ContractAsset__c</stringValue>
                </value>
            </inputParameters>
            <inputParameters>
                <name>fieldApiName</name>
                <value>
                    <stringValue>Asset__c</stringValue>
                </value>
            </inputParameters>
            <inputParameters>
                <name>label</name>
                <value>
                    <stringValue>Veicolo</stringValue>
                </value>
            </inputParameters>
            <inputParameters>
                <name>required</name>
                <value>
                    <booleanValue>true</booleanValue>
                </value>
            </inputParameters>
            <inputParameters>
                <name>maxValues</name>
                <value>
                    <numberValue>1.0</numberValue>
                </value>
            </inputParameters>
            <inputsOnNextNavToAssocScrn>UseStoredValues</inputsOnNextNavToAssocScrn>
            <isRequired>true</isRequired>
            <storeOutputAutomatically>true</storeOutputAutomatically>
        </fields>
        <showFooter>true</showFooter>
        <showHeader>true</showHeader>
        <stageReference>
            <elementReference>Step_1</elementReference>
        </stageReference>
    </screens>
    <screens>
        <name>Screen_Veicolo_Contratto</name>
        <label>Screen Veicolo e Contratto</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <allowBack>true</allowBack>
        <allowFinish>true</allowFinish>
        <allowPause>false</allowPause>
        <connector>
            <targetReference>getDriverPrinc</targetReference>
        </connector>
        <fields>
            <name>Veicoli</name>
            <dataTypeMappings>
                <typeName>T</typeName>
                <typeValue>ContractAsset__c</typeValue>
            </dataTypeMappings>
            <extensionName>flowruntime:datatable</extensionName>
            <fieldType>ComponentInstance</fieldType>
            <inputParameters>
                <name>label</name>
                <value>
                    <stringValue>Veicoli e Contratti</stringValue>
                </value>
            </inputParameters>
            <inputParameters>
                <name>selectionMode</name>
                <value>
                    <stringValue>SINGLE_SELECT</stringValue>
                </value>
            </inputParameters>
            <inputParameters>
                <name>minRowSelection</name>
                <value>
                    <numberValue>1.0</numberValue>
                </value>
            </inputParameters>
            <inputParameters>
                <name>tableData</name>
                <value>
                    <elementReference>getContractAssetInfo</elementReference>
                </value>
            </inputParameters>
            <inputParameters>
                <name>isShowSearchBar</name>
                <value>
                    <booleanValue>true</booleanValue>
                </value>
            </inputParameters>
            <inputParameters>
                <name>shouldDisplayLabel</name>
                <value>
                    <booleanValue>true</booleanValue>
                </value>
            </inputParameters>
            <inputParameters>
                <name>maxRowSelection</name>
                <value>
                    <numberValue>1.0</numberValue>
                </value>
            </inputParameters>
            <inputParameters>
                <name>columns</name>
                <value>
                    <stringValue>[{&quot;apiName&quot;:&quot;NomeAsset__c&quot;,&quot;guid&quot;:&quot;column-b406&quot;,&quot;editable&quot;:false,&quot;hasCustomHeaderLabel&quot;:true,&quot;customHeaderLabel&quot;:&quot;Targa Veicolo&quot;,&quot;wrapText&quot;:true,&quot;order&quot;:0,&quot;label&quot;:&quot;Targa&quot;,&quot;type&quot;:&quot;customRichText&quot;},{&quot;apiName&quot;:&quot;NomeContratto__c&quot;,&quot;guid&quot;:&quot;column-3858&quot;,&quot;editable&quot;:false,&quot;hasCustomHeaderLabel&quot;:true,&quot;customHeaderLabel&quot;:&quot;Contratto&quot;,&quot;wrapText&quot;:true,&quot;order&quot;:1,&quot;label&quot;:&quot;Contratto&quot;,&quot;type&quot;:&quot;customRichText&quot;},{&quot;apiName&quot;:&quot;StatoContratto__c&quot;,&quot;guid&quot;:&quot;column-a373&quot;,&quot;editable&quot;:false,&quot;hasCustomHeaderLabel&quot;:false,&quot;customHeaderLabel&quot;:&quot;&quot;,&quot;wrapText&quot;:true,&quot;order&quot;:2,&quot;label&quot;:&quot;Stato Contratto&quot;,&quot;type&quot;:&quot;customRichText&quot;},{&quot;apiName&quot;:&quot;ClienteContratto__c&quot;,&quot;guid&quot;:&quot;column-a011&quot;,&quot;editable&quot;:false,&quot;hasCustomHeaderLabel&quot;:false,&quot;customHeaderLabel&quot;:&quot;&quot;,&quot;wrapText&quot;:true,&quot;order&quot;:3,&quot;label&quot;:&quot;Cliente Contratto&quot;,&quot;type&quot;:&quot;customRichText&quot;},{&quot;apiName&quot;:&quot;tsDa__c&quot;,&quot;guid&quot;:&quot;column-7d06&quot;,&quot;editable&quot;:false,&quot;hasCustomHeaderLabel&quot;:false,&quot;customHeaderLabel&quot;:&quot;&quot;,&quot;wrapText&quot;:true,&quot;order&quot;:4,&quot;label&quot;:&quot;Da&quot;,&quot;type&quot;:&quot;customDateTime&quot;},{&quot;apiName&quot;:&quot;tsA__c&quot;,&quot;guid&quot;:&quot;column-6015&quot;,&quot;editable&quot;:false,&quot;hasCustomHeaderLabel&quot;:false,&quot;customHeaderLabel&quot;:&quot;&quot;,&quot;wrapText&quot;:true,&quot;order&quot;:5,&quot;label&quot;:&quot;A&quot;,&quot;type&quot;:&quot;customDateTime&quot;}]</stringValue>
                </value>
            </inputParameters>
            <inputsOnNextNavToAssocScrn>UseStoredValues</inputsOnNextNavToAssocScrn>
            <isRequired>true</isRequired>
            <storeOutputAutomatically>true</storeOutputAutomatically>
        </fields>
        <showFooter>true</showFooter>
        <showHeader>true</showHeader>
        <stageReference>
            <elementReference>Step_3</elementReference>
        </stageReference>
    </screens>
    <screens>
        <name>ScreenDatiAR</name>
        <label>ScreenDatiAR</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <allowBack>true</allowBack>
        <allowFinish>true</allowFinish>
        <allowPause>false</allowPause>
        <connector>
            <targetReference>Check_Altro_Rich_Info</targetReference>
        </connector>
        <fields>
            <name>ShowRequiredMsgARText</name>
            <fieldText>&lt;p style=&quot;text-align: center;&quot;&gt;&lt;span style=&quot;background-color: rgb(255, 255, 255); color: rgb(255, 0, 0);&quot;&gt;Le informazioni dell&apos;altro richiedente sono obbligatorie.&lt;/span&gt;&lt;/p&gt;</fieldText>
            <fieldType>DisplayText</fieldType>
            <visibilityRule>
                <conditionLogic>and</conditionLogic>
                <conditions>
                    <leftValueReference>ShowRequiredMsgAR</leftValueReference>
                    <operator>EqualTo</operator>
                    <rightValue>
                        <booleanValue>true</booleanValue>
                    </rightValue>
                </conditions>
            </visibilityRule>
        </fields>
        <fields>
            <name>ScreenDatiAR_Section1</name>
            <fieldType>RegionContainer</fieldType>
            <fields>
                <name>ScreenDatiAR_Section1_Column1</name>
                <fieldType>Region</fieldType>
                <fields>
                    <name>Copy_1_of_EntChiamSelLabel</name>
                    <fieldText>&lt;p&gt;&lt;strong style=&quot;background-color: rgb(255, 255, 255);&quot;&gt;Entità chiamante selezionata&lt;/strong&gt;&lt;/p&gt;</fieldText>
                    <fieldType>DisplayText</fieldType>
                </fields>
                <fields>
                    <name>Copy_1_of_EntChiamSel</name>
                    <fieldText>&lt;p&gt;&lt;span style=&quot;background-color: rgb(255, 255, 255);&quot;&gt;{!caseRecord.EntChiamante__c}&lt;/span&gt;&lt;/p&gt;</fieldText>
                    <fieldType>DisplayText</fieldType>
                </fields>
                <inputParameters>
                    <name>width</name>
                    <value>
                        <stringValue>6</stringValue>
                    </value>
                </inputParameters>
                <isRequired>false</isRequired>
            </fields>
            <fields>
                <name>ScreenDatiAR_Section1_Column2</name>
                <fieldType>Region</fieldType>
                <fields>
                    <name>Copy_1_of_UtilLabel</name>
                    <fieldText>&lt;p&gt;&lt;strong style=&quot;background-color: rgb(255, 255, 255);&quot;&gt;per conto dell&apos;utilizzatore&lt;/strong&gt;&lt;/p&gt;</fieldText>
                    <fieldType>DisplayText</fieldType>
                </fields>
                <fields>
                    <name>Copy_1_of_UtilName</name>
                    <fieldText>&lt;p&gt;&lt;span style=&quot;background-color: rgb(255, 255, 255);&quot;&gt;{!getDriverPrinc.Name}&lt;/span&gt;&lt;/p&gt;</fieldText>
                    <fieldType>DisplayText</fieldType>
                </fields>
                <inputParameters>
                    <name>width</name>
                    <value>
                        <stringValue>6</stringValue>
                    </value>
                </inputParameters>
                <isRequired>false</isRequired>
            </fields>
            <isRequired>false</isRequired>
            <regionContainerType>SectionWithoutHeader</regionContainerType>
        </fields>
        <fields>
            <name>Copy_1_of_Dati_Utilizzatore</name>
            <fieldText>Dati Chiamante</fieldText>
            <fieldType>RegionContainer</fieldType>
            <fields>
                <name>Copy_1_of_Dati_Utilizzatore_Column1</name>
                <fieldType>Region</fieldType>
                <fields>
                    <fieldType>ObjectProvided</fieldType>
                    <inputsOnNextNavToAssocScrn>UseStoredValues</inputsOnNextNavToAssocScrn>
                    <isRequired>false</isRequired>
                    <objectFieldReference>caseRecord.NomeChiamante__c</objectFieldReference>
                </fields>
                <fields>
                    <fieldType>ObjectProvided</fieldType>
                    <inputsOnNextNavToAssocScrn>UseStoredValues</inputsOnNextNavToAssocScrn>
                    <isRequired>false</isRequired>
                    <objectFieldReference>caseRecord.EmailChiamante__c</objectFieldReference>
                </fields>
                <fields>
                    <fieldType>ObjectProvided</fieldType>
                    <inputsOnNextNavToAssocScrn>UseStoredValues</inputsOnNextNavToAssocScrn>
                    <isRequired>false</isRequired>
                    <objectFieldReference>caseRecord.TelChiamante__c</objectFieldReference>
                </fields>
                <inputParameters>
                    <name>width</name>
                    <value>
                        <stringValue>12</stringValue>
                    </value>
                </inputParameters>
                <isRequired>false</isRequired>
            </fields>
            <isRequired>false</isRequired>
            <regionContainerType>SectionWithHeader</regionContainerType>
        </fields>
        <showFooter>true</showFooter>
        <showHeader>true</showHeader>
        <stageReference>
            <elementReference>Step_3</elementReference>
        </stageReference>
    </screens>
    <screens>
        <name>ScreenDatiFM</name>
        <label>ScreenDatiFM</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <allowBack>true</allowBack>
        <allowFinish>true</allowFinish>
        <allowPause>false</allowPause>
        <connector>
            <targetReference>Assign_FM_contact_info</targetReference>
        </connector>
        <fields>
            <name>ScreenDatiFM_Section1</name>
            <fieldType>RegionContainer</fieldType>
            <fields>
                <name>ScreenDatiFM_Section1_Column1</name>
                <fieldType>Region</fieldType>
                <fields>
                    <name>Copy_2_of_EntChiamSelLabel</name>
                    <fieldText>&lt;p&gt;&lt;strong style=&quot;background-color: rgb(255, 255, 255);&quot;&gt;Entità chiamante selezionata&lt;/strong&gt;&lt;/p&gt;</fieldText>
                    <fieldType>DisplayText</fieldType>
                </fields>
                <fields>
                    <name>Copy_2_of_EntChiamSel</name>
                    <fieldText>&lt;p&gt;&lt;span style=&quot;background-color: rgb(255, 255, 255);&quot;&gt;{!caseRecord.EntChiamante__c}&lt;/span&gt;&lt;/p&gt;</fieldText>
                    <fieldType>DisplayText</fieldType>
                </fields>
                <inputParameters>
                    <name>width</name>
                    <value>
                        <stringValue>6</stringValue>
                    </value>
                </inputParameters>
                <isRequired>false</isRequired>
            </fields>
            <fields>
                <name>ScreenDatiFM_Section1_Column2</name>
                <fieldType>Region</fieldType>
                <fields>
                    <name>Copy_2_of_UtilLabel</name>
                    <fieldText>&lt;p&gt;&lt;strong style=&quot;background-color: rgb(255, 255, 255);&quot;&gt;per conto dell&apos;utilizzatore&lt;/strong&gt;&lt;/p&gt;</fieldText>
                    <fieldType>DisplayText</fieldType>
                </fields>
                <fields>
                    <name>Copy_2_of_UtilName</name>
                    <fieldText>&lt;p&gt;&lt;span style=&quot;background-color: rgb(255, 255, 255);&quot;&gt;{!getDriverPrinc.Name}&lt;/span&gt;&lt;/p&gt;</fieldText>
                    <fieldType>DisplayText</fieldType>
                </fields>
                <inputParameters>
                    <name>width</name>
                    <value>
                        <stringValue>6</stringValue>
                    </value>
                </inputParameters>
                <isRequired>false</isRequired>
            </fields>
            <isRequired>false</isRequired>
            <regionContainerType>SectionWithoutHeader</regionContainerType>
        </fields>
        <fields>
            <name>Copy_2_of_Dati_Utilizzatore</name>
            <fieldText>Dati Chiamante</fieldText>
            <fieldType>RegionContainer</fieldType>
            <fields>
                <name>Copy_2_of_Dati_Utilizzatore_Column1</name>
                <fieldType>Region</fieldType>
                <fields>
                    <name>NominativoFM</name>
                    <dataType>String</dataType>
                    <defaultValue>
                        <elementReference>getFMInfo.Name</elementReference>
                    </defaultValue>
                    <fieldText>Riferimento entità chiamante</fieldText>
                    <fieldType>InputField</fieldType>
                    <inputsOnNextNavToAssocScrn>UseStoredValues</inputsOnNextNavToAssocScrn>
                    <isReadOnly>
                        <booleanValue>true</booleanValue>
                    </isReadOnly>
                    <isRequired>false</isRequired>
                </fields>
                <fields>
                    <name>EmailFM</name>
                    <extensionName>flowruntime:email</extensionName>
                    <fieldType>ComponentInstance</fieldType>
                    <inputParameters>
                        <name>label</name>
                        <value>
                            <stringValue>Email Entità chiamante</stringValue>
                        </value>
                    </inputParameters>
                    <inputParameters>
                        <name>value</name>
                        <value>
                            <elementReference>getFMInfo.Email</elementReference>
                        </value>
                    </inputParameters>
                    <inputsOnNextNavToAssocScrn>UseStoredValues</inputsOnNextNavToAssocScrn>
                    <isRequired>true</isRequired>
                    <storeOutputAutomatically>true</storeOutputAutomatically>
                </fields>
                <fields>
                    <name>TelefonoFM</name>
                    <extensionName>flowruntime:phone</extensionName>
                    <fieldType>ComponentInstance</fieldType>
                    <inputParameters>
                        <name>label</name>
                        <value>
                            <stringValue>Telefono entità chiamante</stringValue>
                        </value>
                    </inputParameters>
                    <inputParameters>
                        <name>value</name>
                        <value>
                            <elementReference>getFMInfo.Phone</elementReference>
                        </value>
                    </inputParameters>
                    <inputsOnNextNavToAssocScrn>UseStoredValues</inputsOnNextNavToAssocScrn>
                    <isRequired>true</isRequired>
                    <storeOutputAutomatically>true</storeOutputAutomatically>
                </fields>
                <inputParameters>
                    <name>width</name>
                    <value>
                        <stringValue>12</stringValue>
                    </value>
                </inputParameters>
                <isRequired>false</isRequired>
            </fields>
            <isRequired>false</isRequired>
            <regionContainerType>SectionWithHeader</regionContainerType>
        </fields>
        <showFooter>true</showFooter>
        <showHeader>true</showHeader>
    </screens>
    <screens>
        <name>ScreenDatiUtilizzatore</name>
        <label>ScreenDatiUtilizzatore</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <allowBack>true</allowBack>
        <allowFinish>true</allowFinish>
        <allowPause>false</allowPause>
        <connector>
            <targetReference>Assign_Util_contact_info</targetReference>
        </connector>
        <fields>
            <name>ScreenDatiUtilizzatore_Section1</name>
            <fieldType>RegionContainer</fieldType>
            <fields>
                <name>ScreenDatiUtilizzatore_Section1_Column1</name>
                <fieldType>Region</fieldType>
                <fields>
                    <name>EntChiamSelLabel</name>
                    <fieldText>&lt;p&gt;&lt;strong style=&quot;background-color: rgb(255, 255, 255);&quot;&gt;Entità chiamante selezionata&lt;/strong&gt;&lt;/p&gt;</fieldText>
                    <fieldType>DisplayText</fieldType>
                </fields>
                <fields>
                    <name>EntChiamSel</name>
                    <fieldText>&lt;p&gt;&lt;span style=&quot;background-color: rgb(255, 255, 255);&quot;&gt;{!caseRecord.EntChiamante__c}&lt;/span&gt;&lt;/p&gt;</fieldText>
                    <fieldType>DisplayText</fieldType>
                </fields>
                <inputParameters>
                    <name>width</name>
                    <value>
                        <stringValue>6</stringValue>
                    </value>
                </inputParameters>
                <isRequired>false</isRequired>
            </fields>
            <fields>
                <name>ScreenDatiUtilizzatore_Section1_Column2</name>
                <fieldType>Region</fieldType>
                <fields>
                    <name>UtilLabel</name>
                    <fieldText>&lt;p&gt;&lt;strong style=&quot;background-color: rgb(255, 255, 255);&quot;&gt;per conto dell&apos;utilizzatore&lt;/strong&gt;&lt;/p&gt;</fieldText>
                    <fieldType>DisplayText</fieldType>
                </fields>
                <fields>
                    <name>UtilName</name>
                    <fieldText>&lt;p&gt;&lt;span style=&quot;background-color: rgb(255, 255, 255);&quot;&gt;{!getDriverPrinc.Name}&lt;/span&gt;&lt;/p&gt;</fieldText>
                    <fieldType>DisplayText</fieldType>
                </fields>
                <inputParameters>
                    <name>width</name>
                    <value>
                        <stringValue>6</stringValue>
                    </value>
                </inputParameters>
                <isRequired>false</isRequired>
            </fields>
            <isRequired>false</isRequired>
            <regionContainerType>SectionWithoutHeader</regionContainerType>
        </fields>
        <fields>
            <name>Dati_Utilizzatore</name>
            <fieldText>Dati Chiamante</fieldText>
            <fieldType>RegionContainer</fieldType>
            <fields>
                <name>Dati_Utilizzatore_Column1</name>
                <fieldType>Region</fieldType>
                <fields>
                    <name>NominativoDriver</name>
                    <dataType>String</dataType>
                    <defaultValue>
                        <elementReference>getDriverPrinc.Name</elementReference>
                    </defaultValue>
                    <fieldText>Riferimento entità chiamante</fieldText>
                    <fieldType>InputField</fieldType>
                    <inputsOnNextNavToAssocScrn>UseStoredValues</inputsOnNextNavToAssocScrn>
                    <isReadOnly>
                        <booleanValue>true</booleanValue>
                    </isReadOnly>
                    <isRequired>false</isRequired>
                </fields>
                <fields>
                    <name>EmailDriver</name>
                    <extensionName>flowruntime:email</extensionName>
                    <fieldType>ComponentInstance</fieldType>
                    <inputParameters>
                        <name>label</name>
                        <value>
                            <stringValue>Email Entità chiamante</stringValue>
                        </value>
                    </inputParameters>
                    <inputParameters>
                        <name>value</name>
                        <value>
                            <elementReference>Veicoli.firstSelectedRow.ServiceContract__r.Account.PersonContact.Email</elementReference>
                        </value>
                    </inputParameters>
                    <inputsOnNextNavToAssocScrn>UseStoredValues</inputsOnNextNavToAssocScrn>
                    <isRequired>true</isRequired>
                    <storeOutputAutomatically>true</storeOutputAutomatically>
                </fields>
                <fields>
                    <name>TelefonoDriver</name>
                    <extensionName>flowruntime:phone</extensionName>
                    <fieldType>ComponentInstance</fieldType>
                    <inputParameters>
                        <name>label</name>
                        <value>
                            <stringValue>Telefono entità chiamante</stringValue>
                        </value>
                    </inputParameters>
                    <inputParameters>
                        <name>value</name>
                        <value>
                            <elementReference>Veicoli.firstSelectedRow.ServiceContract__r.Account.PersonContact.Phone</elementReference>
                        </value>
                    </inputParameters>
                    <inputsOnNextNavToAssocScrn>UseStoredValues</inputsOnNextNavToAssocScrn>
                    <isRequired>true</isRequired>
                    <storeOutputAutomatically>true</storeOutputAutomatically>
                </fields>
                <inputParameters>
                    <name>width</name>
                    <value>
                        <stringValue>12</stringValue>
                    </value>
                </inputParameters>
                <isRequired>false</isRequired>
            </fields>
            <isRequired>false</isRequired>
            <regionContainerType>SectionWithHeader</regionContainerType>
        </fields>
        <showFooter>true</showFooter>
        <showHeader>true</showHeader>
        <stageReference>
            <elementReference>Step_3</elementReference>
        </stageReference>
    </screens>
    <screens>
        <name>ScreenEntitaChiamante</name>
        <label>ScreenEntitaChiamante</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <allowBack>true</allowBack>
        <allowFinish>true</allowFinish>
        <allowPause>false</allowPause>
        <connector>
            <targetReference>Check_Ent_Chiamante</targetReference>
        </connector>
        <fields>
            <name>ShowRequiredMsgECText</name>
            <fieldText>&lt;p style=&quot;text-align: center;&quot;&gt;&lt;span style=&quot;color: rgb(255, 0, 0);&quot;&gt;E&apos; obbligatorio selezionare l&apos;entità chiamante.&lt;/span&gt;&lt;/p&gt;</fieldText>
            <fieldType>DisplayText</fieldType>
            <visibilityRule>
                <conditionLogic>and</conditionLogic>
                <conditions>
                    <leftValueReference>ShowRequiredMsgEC</leftValueReference>
                    <operator>EqualTo</operator>
                    <rightValue>
                        <booleanValue>true</booleanValue>
                    </rightValue>
                </conditions>
            </visibilityRule>
        </fields>
        <fields>
            <name>ScreenEntitaChiamante_Section1</name>
            <fieldType>RegionContainer</fieldType>
            <fields>
                <name>ScreenEntitaChiamante_Section1_Column1</name>
                <fieldType>Region</fieldType>
                <fields>
                    <fieldType>ObjectProvided</fieldType>
                    <inputsOnNextNavToAssocScrn>UseStoredValues</inputsOnNextNavToAssocScrn>
                    <isRequired>false</isRequired>
                    <objectFieldReference>caseRecord.EntChiamante__c</objectFieldReference>
                </fields>
                <inputParameters>
                    <name>width</name>
                    <value>
                        <stringValue>6</stringValue>
                    </value>
                </inputParameters>
                <isRequired>false</isRequired>
            </fields>
            <fields>
                <name>ScreenEntitaChiamante_Section1_Column2</name>
                <fieldType>Region</fieldType>
                <fields>
                    <name>LabelDriver</name>
                    <fieldText>&lt;p&gt;&lt;strong&gt;per conto dell&apos;utilizzatore&lt;/strong&gt;&lt;/p&gt;</fieldText>
                    <fieldType>DisplayText</fieldType>
                </fields>
                <fields>
                    <name>driverName</name>
                    <fieldText>&lt;p&gt;{!getDriverPrinc.Name}&lt;/p&gt;</fieldText>
                    <fieldType>DisplayText</fieldType>
                </fields>
                <inputParameters>
                    <name>width</name>
                    <value>
                        <stringValue>6</stringValue>
                    </value>
                </inputParameters>
                <isRequired>false</isRequired>
            </fields>
            <isRequired>false</isRequired>
            <regionContainerType>SectionWithoutHeader</regionContainerType>
        </fields>
        <showFooter>true</showFooter>
        <showHeader>true</showHeader>
        <stageReference>
            <elementReference>step4</elementReference>
        </stageReference>
    </screens>
    <screens>
        <name>ScreenErrorMsg</name>
        <label>ScreenErrorMsg Contratti</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <allowBack>false</allowBack>
        <allowFinish>true</allowFinish>
        <allowPause>false</allowPause>
        <fields>
            <name>ErrorMsg</name>
            <fieldText>&lt;p style=&quot;text-align: center;&quot;&gt;Non sono disponibili contratti da selezionare.&lt;/p&gt;</fieldText>
            <fieldType>DisplayText</fieldType>
        </fields>
        <showFooter>true</showFooter>
        <showHeader>true</showHeader>
    </screens>
    <stages>
        <name>step4</name>
        <isActive>false</isActive>
        <label>step4</label>
        <stageOrder>4</stageOrder>
    </stages>
    <stages>
        <name>Step5</name>
        <isActive>true</isActive>
        <label>Step5</label>
        <stageOrder>5</stageOrder>
    </stages>
    <stages>
        <name>Step_1</name>
        <isActive>true</isActive>
        <label>Step_1</label>
        <stageOrder>1</stageOrder>
    </stages>
    <stages>
        <name>Step_2</name>
        <isActive>false</isActive>
        <label>Step_2</label>
        <stageOrder>2</stageOrder>
    </stages>
    <stages>
        <name>Step_3</name>
        <isActive>false</isActive>
        <label>Step_3</label>
        <stageOrder>3</stageOrder>
    </stages>
    <start>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <connector>
            <targetReference>GetCaseInfo</targetReference>
        </connector>
    </start>
    <status>Active</status>
    <subflows>
        <name>CheckZonaCircRequired</name>
        <label>CheckZonaCircRequired</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <connector>
            <targetReference>Check_flag_result</targetReference>
        </connector>
        <flowName>urcs_CaseCheckZonaCircRequired</flowName>
        <inputAssignments>
            <name>Categoria</name>
            <value>
                <elementReference>caseRecord.Categoria__c</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <name>Sottocategoria</name>
            <value>
                <elementReference>caseRecord.SottoCategoria__c</elementReference>
            </value>
        </inputAssignments>
        <outputAssignments>
            <assignToReference>isZonaCircrRequired</assignToReference>
            <name>isZonaCircrRequired</name>
        </outputAssignments>
    </subflows>
    <variables>
        <name>caseRecord</name>
        <dataType>SObject</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>true</isOutput>
        <objectType>Case</objectType>
    </variables>
    <variables>
        <name>DriverAccountIdList</name>
        <dataType>String</dataType>
        <isCollection>true</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
    </variables>
    <variables>
        <name>groupIdList</name>
        <dataType>String</dataType>
        <isCollection>true</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
    </variables>
    <variables>
        <name>isZonaCircrRequired</name>
        <dataType>Boolean</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
        <value>
            <booleanValue>false</booleanValue>
        </value>
    </variables>
    <variables>
        <name>recordId</name>
        <dataType>String</dataType>
        <isCollection>false</isCollection>
        <isInput>true</isInput>
        <isOutput>true</isOutput>
    </variables>
    <variables>
        <name>showErrorSubagente</name>
        <dataType>Boolean</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
        <value>
            <booleanValue>false</booleanValue>
        </value>
    </variables>
    <variables>
        <name>ShowRequiredMsg</name>
        <dataType>Boolean</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
        <value>
            <booleanValue>false</booleanValue>
        </value>
    </variables>
    <variables>
        <name>ShowRequiredMsgAR</name>
        <dataType>Boolean</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
        <value>
            <booleanValue>false</booleanValue>
        </value>
    </variables>
    <variables>
        <name>ShowRequiredMsgEC</name>
        <dataType>Boolean</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
        <value>
            <booleanValue>false</booleanValue>
        </value>
    </variables>
    <variables>
        <name>ShowRequiredMsgNote</name>
        <dataType>Boolean</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
        <value>
            <booleanValue>false</booleanValue>
        </value>
    </variables>
    <variables>
        <name>ufficiQueueList</name>
        <dataType>SObject</dataType>
        <isCollection>true</isCollection>
        <isInput>true</isInput>
        <isOutput>false</isOutput>
        <objectType>Group</objectType>
    </variables>
</Flow>
