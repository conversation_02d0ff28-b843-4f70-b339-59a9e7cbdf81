<?xml version="1.0" encoding="UTF-8"?>
<CustomField xmlns="http://soap.sforce.com/2006/04/metadata">
    <fullName>IsCaseEditable__c</fullName>
    <description>Flag tecnico che determina se un Case è modificabile basato sul Record Type e sulla presenza di valori nella Categoria</description>
    <formula>AND(
   IsClosed == false,
   $User.Id == UtAssegnatario__c,
   OR(
      RecordType.DeveloperName == &quot;ur_CaseCRM&quot;,
      AND(
          RecordType.DeveloperName == &quot;ur_CaseES&quot;,
          NOT(ISBLANK(TEXT(Categoria__c)))
      )
   )
)</formula>
    <formulaTreatBlanksAs>BlankAsZero</formulaTreatBlanksAs>
    <label>IsCaseEditable</label>
    <trackHistory>false</trackHistory>
    <trackTrending>false</trackTrending>
    <type>Checkbox</type>
</CustomField>
