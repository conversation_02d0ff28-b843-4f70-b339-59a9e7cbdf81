------------------------------------------------------------------------------
--------------------------------- DESCRIPTION ---------------------------------
-------------------------------------------------------------------------------
 
The purpose of this manual procedure is to remove all APP from  from the Salesforce App Launcher for Unipol Standard User and Responsabili User Profiles.
 
-------------------------------------------------------------------------------
--------------------------- MANUAL PROCEDURE STEPS ----------------------------
-------------------------------------------------------------------------------

1. Log in to Salesforce as a System Administrator.
2. Go to Setup.
3. In the Quick Find box, search for "Profiles" and select "Profiles".
4. Select the profile "Unipol Standard User" .
5. Scroll down to the section "Assigned Apps" .
6. Click "Edit".
7. In the list of apps, deselect all the apps below:
   Analytics Studio (standard__Insights)
   Approvals (standard__Approvals)
   Bolt Solutions (standard__LightningBolt)
   Branch Management (standard__BranchManagementConsole)
   Business Rules Engine (standard__ExpressionSetConsole)
   Community (standard__Community)
   Data Manager (standard__DataManager)
   Digital Experiences (standard__SalesforceCMS)
   Digital Waitlist (standard__QueueManagement)
   FSC Learning Experience (FSC_Learning_Experience)
   Insurance Agent Console (standard__InsuranceConsole)
   Marketing CRM Classic (standard__Marketing)
   My Service Journey (standard__MSJApp)
   OmniStudio (omnistudio__Vlocity_Digital_Studio)
   Policy Center (standard__DataGovernanceConsole)
   Product Catalog Management (standard__IndustriesEpc)
   Sales Cloud Mobile (standard__SalesCloudMobile)
   Salesforce Chatter (standard__Chatter)
   Salesforce Optimizer (standard__Optimizer)
   Service Console (standard__LightningService)
   Service Console — Salesforce (standard__PreconfiguredLightningServiceConsole)
   Your Account (standard__OnlineSales)
8. Repeat for profile "Unipol Responsabile User".
