------------------------------------------------------------------------------
--------------------------------- DESCRIPTION ---------------------------------
-------------------------------------------------------------------------------
 
This manual procedure aims to configure EmailtoCase for DEV environment.
 
-------------------------------------------------------------------------------
--------------------------- MANUAL PROCEDURE STEPS ----------------------------
-------------------------------------------------------------------------------
 
1. Login to Salesforce
2. Open Setup - Search for "Email-to-Case" - New
    - Routing Name: <EMAIL> [Test Bandi e Gare MailBox]
    - Email Address: <EMAIL>
    - Controlled by Permission Set: Checked
    - Case Owner: Queue -> BandieGare
    - Case Priority: Medium
    - Case Origin: Email Service
    - Case Record Type: ur_CaseES
    - Save.
    - Request the mailbox owner to complete verification process.
3. Open Setup 
    - Search for "permission Set" 
    - Search for: "Unipol Rental CS Operatore" and select 
    - Find setting for: "Routing" and select "Email-to-Case Routing Address Access"  
    - Click edit and add "<EMAIL>" to Selected Email-to-Case Routing Addresses
    - Save

