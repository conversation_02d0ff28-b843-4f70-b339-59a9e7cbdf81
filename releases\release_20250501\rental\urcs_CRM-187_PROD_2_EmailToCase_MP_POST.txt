------------------------------------------------------------------------------
--------------------------------- DESCRIPTION ---------------------------------
-------------------------------------------------------------------------------
 
This manual procedure aims to configure the missing NBTAssociazioniEConferme queue
for EmailtoCase functionality. This queue was identified as missing from the 
original US 187 implementation.
 
-------------------------------------------------------------------------------
--------------------------- MANUAL PROCEDURE STEPS ----------------------------
-------------------------------------------------------------------------------
 
1. Login to Salesforce
2. Open Setup - Search for "Queues"

===============================================================================
Configure Email-to-Case for NBTAssociazioniEConferme
===============================================================================

3. If there are specific email addresses for this queue, configure Email-to-Case:
   - Open Setup - Search for "Email-to-Case"
   - For each email address that should route to NBTAssociazioniEConferme:
     - Click "New" and configure:
     - Routing Name: <email_address>
     - Email Address: <email_address>
     - Controlled by Permission Set: Checked
     - Case Owner: Queue -> NBTAssociazioniEConferme
     - Case Priority: Medium
     - Case Origin: Email Service
     - Case Record Type: ur_CaseES
     - Save

===============================================================================
SECTION C: Permission Set Configuration
===============================================================================

5. Update Permission Set for new queue access:
   - Open Setup - Search for "Permission Sets"
   - Search for: "Unipol Rental CS Operatore" and select
   - Go to "Object Settings" -> "Case"
   - Ensure the NBTAssociazioniEConferme queue is accessible
   - If Email-to-Case addresses were configured, update:
     - Find setting for: "Routing" and select "Email-to-Case Routing Address Access"
     - Click edit and add any new email addresses to Selected Email-to-Case Routing Addresses
     - Save

===============================================================================
SECTION D: Case Assignment Rules (if applicable)
===============================================================================

6. Update Case Assignment Rules if needed:
   - Open Setup - Search for "Case Assignment Rules"
   - Review existing rules to ensure NBTAssociazioniEConferme queue is properly integrated
   - Update rules as necessary to route cases to the new queue based on business logic

===============================================================================
VERIFICATION STEPS
===============================================================================

7. Verify the NBTAssociazioniEConferme queue is created and active
8. Test queue functionality by creating a test case and assigning it to the queue
9. If Email-to-Case was configured, test email routing to ensure proper case assignment
10. Verify queue members can access and work on cases assigned to this queue

