------------------------------------------------------------------------------
--------------------------------- DESCRIPTION ---------------------------------
-------------------------------------------------------------------------------
 
This manual procedure aims to configure 54 EmailtoCase mailboxes for DEV environment.
The mailboxes are grouped by queue to optimize configuration process.
 
-------------------------------------------------------------------------------
--------------------------- MANUAL PROCEDURE STEPS ----------------------------
-------------------------------------------------------------------------------
 
1. Login to Salesforce
2. Open Setup - Search for "Email-to-Case"

===============================================================================
SECTION A: FleetAccountCustCare Queue (40 mailboxes)
===============================================================================

3. For each of the following 40 email addresses, create a new Email-to-Case configuration:

   <EMAIL>
   <EMAIL>
   <EMAIL>
   <EMAIL>
   <EMAIL>
   <EMAIL>
   <EMAIL>
   <EMAIL>
   <EMAIL>
   <EMAIL>
   <EMAIL>
   <EMAIL>
   <EMAIL>
   <EMAIL>
   <EMAIL>
   <EMAIL>
   <EMAIL>
   <EMAIL>
   <EMAIL>
   <EMAIL>
   <EMAIL>
   <EMAIL>
   <EMAIL>
   <EMAIL>
   <EMAIL>
   <EMAIL>
   <EMAIL>
   <EMAIL>
   <EMAIL>
   <EMAIL>
   <EMAIL>
   <EMAIL>
   <EMAIL>
   <EMAIL>
   <EMAIL>
   <EMAIL>
   <EMAIL>
   <EMAIL>
   <EMAIL>
   <EMAIL>

   For each email, click "New" and configure:
   - Routing Name: [email_address] [FleetAccountCustCare MailBox]
   - Email Address: [email_address]
   - Controlled by Permission Set: Checked
   - Case Owner: Queue -> FleetAccountCustCare
   - Case Priority: Medium
   - Case Origin: Email Service
   - Case Record Type: ur_CaseES
   - Save.
   - Request the mailbox owner to complete verification process.

===============================================================================
SECTION B: BandieGare Queue (9 mailboxes)
===============================================================================

4. For each of the following 9 email addresses, create a new Email-to-Case configuration:

   <EMAIL>
   <EMAIL>
   <EMAIL>
   <EMAIL>
   <EMAIL>
   <EMAIL>
   <EMAIL>
   <EMAIL>
   <EMAIL>

   For each email, click "New" and configure:
   - Routing Name: [email_address] [BandieGare MailBox]
   - Email Address: [email_address]
   - Controlled by Permission Set: Checked
   - Case Owner: Queue -> BandieGare
   - Case Priority: Medium
   - Case Origin: Email Service
   - Case Record Type: ur_CaseES
   - Save.
   - Request the mailbox owner to complete verification process.

===============================================================================
SECTION C: Individual Queue Mailboxes (4 mailboxes)
===============================================================================

5. Configure the following individual mailboxes:

   5.1 UffMulte Queue:
       - Email: <EMAIL>
       - Routing Name: <EMAIL> [UffMulte MailBox]
       - Case Owner: Queue -> UffMulte
       - (Other settings same as above)

   5.2 CarteCarburante Queue:
       - Email: <EMAIL>
       - Routing Name: <EMAIL> [CarteCarburante MailBox]
       - Case Owner: Queue -> CarteCarburante
       - (Other settings same as above)

   5.3 Amministrazione_Art94 Queue:
       - Email: <EMAIL>
       - Routing Name: <EMAIL> [Amministrazione_Art94 MailBox]
       - Case Owner: Queue -> Amministrazione_Art94
       - (Other settings same as above)

   5.4 GestioneBollo Queue:
       - Email: <EMAIL>
       - Routing Name: <EMAIL> [GestioneBollo MailBox]
       - Case Owner: Queue -> GestioneBollo
       - (Other settings same as above)

===============================================================================
SECTION D: Unassigned Mailbox (1 mailbox)
===============================================================================

6. Configure the following mailbox (queue assignment to be confirmed):
   - Email: <EMAIL>
   - Routing Name: <EMAIL> [Preassegnazioni MailBox]
   - Case Owner: Queue -> [TO BE DEFINED]
   - (Other settings same as above)

===============================================================================
SECTION E: Permission Set Configuration
===============================================================================

7. Open Setup 
   - Search for "permission Set" 
   - Search for: "Unipol Rental CS Operatore" and select 
   - Find setting for: "Routing" and select "Email-to-Case Routing Address Access"  
   - Click edit and add ALL 54 configured email addresses to Selected Email-to-Case Routing Addresses
   - Save

===============================================================================
VERIFICATION STEPS
===============================================================================

8. Verify all 54 Email-to-Case configurations are created and active
9. Confirm all mailbox owners have completed verification process
10. Test email routing for each queue to ensure proper case assignment

===============================================================================
NOTES
===============================================================================

- Total mailboxes configured: 54
- FleetAccountCustCare: 40 mailboxes
- BandieGare: 9 mailboxes  
- Individual queues: 4 mailboxes
- Unassigned: 1 mailbox (requires queue assignment)
- All configurations use ur_CaseES record type and Medium priority
- All are controlled by Permission Set for security
